/* Staking Specific Styles */

.stake-container {
    max-width: 600px;
    margin: 0 auto;
}

.stake-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.stake-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.stake-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.apy {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--btn-accent), #059669);
    color: white;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.apy span {
    font-size: 1rem;
}

/* Stake Stats */
.stake-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stake-stats .stat {
    text-align: center;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.stake-stats .stat label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stake-stats .stat .value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Stake Actions */
.stake-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.stake-input {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.stake-input:focus {
    outline: none;
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.stake-input::placeholder {
    color: var(--text-secondary);
}

.max-btn {
    padding: 1rem 1.5rem;
    background-color: var(--btn-secondary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.max-btn:hover {
    background-color: var(--btn-secondary-hover);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

/* LP Info */
.lp-info {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    text-align: center;
}

.lp-info h3 {
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.lp-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Staking Pool Info */
.pool-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
}

.pool-stat {
    text-align: center;
}

.pool-stat-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.pool-stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Rewards Section */
.rewards-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
}

.rewards-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.rewards-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.rewards-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--btn-accent);
}

.rewards-usd {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Staking History */
.staking-history {
    margin-top: 2rem;
}

.staking-history h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.history-action {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.history-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.history-icon.stake {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.history-icon.unstake {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.history-icon.claim {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.history-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.history-type {
    font-weight: 500;
    color: var(--text-primary);
}

.history-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.history-amount {
    font-weight: 600;
    color: var(--text-primary);
}

/* Loading and Error States */
.stake-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.stake-error {
    padding: 1rem;
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--error);
    border-radius: 0.5rem;
    color: var(--error);
    text-align: center;
    margin-bottom: 1rem;
}

/* Responsive Staking */
@media (max-width: 768px) {
    .stake-card {
        padding: 1.5rem;
    }
    
    .stake-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stake-stats {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .pool-info {
        grid-template-columns: 1fr;
    }
    
    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .stake-container {
        padding: 0 0.5rem;
    }
    
    .stake-card {
        padding: 1rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .max-btn {
        width: 100%;
    }
}
