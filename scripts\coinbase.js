(()=>{var _0x4dd2ac={9016(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CoinbaseWalletSDK=void 0;let r=n(2719),i=n(9682),$=n(3143),o=n(3518),s=n(6570),x=n(7472),a=n(4643),u={NODE_ENV:"production",WALLETLINK_URL:void 0,WALLETLINK_VERSION:"3.3.0"}.LINK_API_URL||"https://www.walletlink.org",c={NODE_ENV:"production",WALLETLINK_URL:void 0,WALLETLINK_VERSION:"3.3.0"}.SDK_VERSION||n(626).i8||"unknown";class l{constructor(t){var e,n,r;this._appName="",this._appLogoUrl=null,this._relay=null,this._relayEventManager=null;let $=t.linkAPIUrl||u,a;if(a=t.uiConstructor?t.uiConstructor:t=>new o.WalletSDKUI(t),void 0===t.overrideIsMetaMask?this._overrideIsMetaMask=!1:this._overrideIsMetaMask=t.overrideIsMetaMask,this._overrideIsCoinbaseWallet=null===(e=t.overrideIsCoinbaseWallet)||void 0===e||e,this._overrideIsCoinbaseBrowser=null!==(n=t.overrideIsCoinbaseBrowser)&&void 0!==n&&n,t.diagnosticLogger&&t.eventListener)throw Error("can not have both eventListener and diagnosticLogger options, use only diagnosticLogger");t.eventListener?this._diagnosticLogger={log:t.eventListener.onEvent}:this._diagnosticLogger=t.diagnosticLogger,this._reloadOnDisconnect=null===(r=t.reloadOnDisconnect)||void 0===r||r;let h=new URL($),f=h.protocol+"//"+h.host;this._storage=new i.ScopedLocalStorage("-walletlink:"+f),this._storage.setItem("version",l.VERSION),this.walletExtension||(this._relayEventManager=new x.WalletSDKRelayEventManager,this._relay=new s.WalletSDKRelay({linkAPIUrl:$,version:c,darkMode:!!t.darkMode,uiConstructor:a,storage:this._storage,relayEventManager:this._relayEventManager,diagnosticLogger:this._diagnosticLogger}),this.setAppInfo(t.appName,t.appLogoUrl),t.headlessMode||this._relay.attachUI())}makeWeb3Provider(t="",e=1){let n=this.walletExtension;if(n)return this.isCipherProvider(n)||n.setProviderInfo(t,e),!1===this._reloadOnDisconnect&&"function"==typeof n.disableReloadOnDisconnect&&n.disableReloadOnDisconnect(),n;let r=this._relay;if(!r||!this._relayEventManager||!this._storage)throw Error("Relay not initialized, should never happen");return t||r.setConnectDisabled(!0),new $.CoinbaseWalletProvider({relayProvider:()=>Promise.resolve(r),relayEventManager:this._relayEventManager,storage:this._storage,jsonRpcUrl:t,chainId:e,qrUrl:this.getQrUrl(),diagnosticLogger:this._diagnosticLogger,overrideIsMetaMask:this._overrideIsMetaMask,overrideIsCoinbaseWallet:this._overrideIsCoinbaseWallet,overrideIsCoinbaseBrowser:this._overrideIsCoinbaseBrowser})}setAppInfo(t,e){var n;this._appName=t||"DApp",this._appLogoUrl=e||(0,a.getFavicon)();let r=this.walletExtension;r?this.isCipherProvider(r)||r.setAppInfo(this._appName,this._appLogoUrl):null===(n=this._relay)||void 0===n||n.setAppInfo(this._appName,this._appLogoUrl)}disconnect(){var t;let e=this.walletExtension;e?e.close():null===(t=this._relay)||void 0===t||t.resetAndReload()}getQrUrl(){var t,e;return null!==(e=null===(t=this._relay)||void 0===t?void 0:t.getQRCodeUrl())&&void 0!==e?e:null}getCoinbaseWalletLogo(t,e=240){return(0,r.walletLogo)(t,e)}get walletExtension(){var t;return null!==(t=window.coinbaseWalletExtension)&&void 0!==t?t:window.walletLinkExtension}isCipherProvider(t){return"boolean"==typeof t.isCipher&&t.isCipher}}e.CoinbaseWalletSDK=l,l.VERSION=c},2719(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.walletLogo=void 0,e.walletLogo=(t,e)=>{let n;switch(t){case"standard":default:return n=e,"data:image/svg+xml,%3Csvg width='"+e+"' height='"+n+"' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E ";case"circle":return n=e,"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='"+e+"' height='"+n+"' viewBox='0 0 999.81 999.81'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052fe;%7D.cls-2%7Bfill:%23fefefe;%7D.cls-3%7Bfill:%230152fe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M655-115.9h56c.83,1.59,2.36.88,3.56,1a478,478,0,0,1,75.06,10.42C891.4-81.76,978.33-32.58,1049.19,44q116.7,126,131.94,297.61c.38,4.14-.34,8.53,1.78,12.45v59c-1.58.84-.91,2.35-1,3.56a482.05,482.05,0,0,1-10.38,74.05c-24,106.72-76.64,196.76-158.83,268.93s-178.18,112.82-287.2,122.6c-4.83.43-9.86-.25-14.51,1.77H654c-1-1.68-2.69-.91-4.06-1a496.89,496.89,0,0,1-105.9-18.59c-93.54-27.42-172.78-77.59-236.91-150.94Q199.34,590.1,184.87,426.58c-.47-5.19.25-10.56-1.77-15.59V355c1.68-1,.91-2.7,1-4.06a498.12,498.12,0,0,1,18.58-105.9c26-88.75,72.64-164.9,140.6-227.57q126-116.27,297.21-131.61C645.32-114.57,650.35-113.88,655-115.9Zm377.92,500c0-192.44-156.31-349.49-347.56-350.15-194.13-.68-350.94,155.13-352.29,347.42-1.37,194.55,155.51,352.1,348.56,352.47C876.15,734.23,1032.93,577.84,1032.93,384.11Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-2' d='M1032.93,384.11c0,193.73-156.78,350.12-351.29,349.74-193-.37-349.93-157.92-348.56-352.47C334.43,189.09,491.24,33.28,685.37,34,876.62,34.62,1032.94,191.67,1032.93,384.11ZM683,496.81q43.74,0,87.48,0c15.55,0,25.32-9.72,25.33-25.21q0-87.48,0-175c0-15.83-9.68-25.46-25.59-25.46H595.77c-15.88,0-25.57,9.64-25.58,25.46q0,87.23,0,174.45c0,16.18,9.59,25.7,25.84,25.71Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-3' d='M683,496.81H596c-16.25,0-25.84-9.53-25.84-25.71q0-87.23,0-174.45c0-15.82,9.7-25.46,25.58-25.46H770.22c15.91,0,25.59,9.63,25.59,25.46q0,87.47,0,175c0,15.49-9.78,25.2-25.33,25.21Q726.74,496.84,683,496.81Z' transform='translate(-183.1 115.9)'/%3E%3C/svg%3E";case"text":return n=(.1*e).toFixed(2),"data:image/svg+xml,%3Csvg width='"+e+"' height='"+n+"' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E";case"textWithLogo":return n=(.25*e).toFixed(2),"data:image/svg+xml,%3Csvg width='"+e+"' height='"+n+"' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E";case"textLight":return n=(.1*e).toFixed(2),"data:image/svg+xml,%3Csvg width='"+e+"' height='"+n+"' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E";case"textWithLogoLight":return n=(.25*e).toFixed(2),"data:image/svg+xml,%3Csvg width='"+e+"' height='"+n+"' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E"}}},8202(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LinkFlow=void 0;let r=n(6400),i=n(4143),$=n(101);e.LinkFlow=class{constructor(t){this.extensionUI$=new i.BehaviorSubject({}),this.subscriptions=new i.Subscription,this.isConnected=!1,this.isOpen=!1,this.onCancel=null,this.root=null,this.connectDisabled=!1,this.darkMode=t.darkMode,this.version=t.version,this.sessionId=t.sessionId,this.sessionSecret=t.sessionSecret,this.linkAPIUrl=t.linkAPIUrl,this.isParentConnection=t.isParentConnection,this.connected$=t.connected$}attach(t){this.root=document.createElement("div"),this.root.className="-cbwsdk-link-flow-root",t.appendChild(this.root),this.render(),this.subscriptions.add(this.connected$.subscribe(t=>{this.isConnected!==t&&(this.isConnected=t,this.render())}))}detach(){var t;this.root&&(this.subscriptions.unsubscribe(),(0,r.render)(null,this.root),null===(t=this.root.parentElement)||void 0===t||t.removeChild(this.root))}setConnectDisabled(t){this.connectDisabled=t}open(t){this.isOpen=!0,this.onCancel=t.onCancel,this.render()}close(){this.isOpen=!1,this.onCancel=null,this.render()}render(){if(!this.root)return;let t=this.extensionUI$.subscribe(()=>{this.root&&(0,r.render)((0,r.h)($.TryExtensionLinkDialog,{darkMode:this.darkMode,version:this.version,sessionId:this.sessionId,sessionSecret:this.sessionSecret,linkAPIUrl:this.linkAPIUrl,isOpen:this.isOpen,isConnected:this.isConnected,isParentConnection:this.isParentConnection,onCancel:this.onCancel,connectDisabled:this.connectDisabled}),this.root)});this.subscriptions.add(t)}}},381:function(t,e,n){"use strict";var r=n(8764).Buffer,i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.QRCode=void 0;let $=n(6400),o=n(396),s=i(n(7713));e.QRCode=t=>{let[e,n]=(0,o.useState)("");return(0,o.useEffect)(()=>{var e,i;let $=new s.default({content:t.content,background:t.bgColor||"#ffffff",color:t.fgColor||"#000000",container:"svg",ecl:"M",width:null!==(e=t.width)&&void 0!==e?e:256,height:null!==(i=t.height)&&void 0!==i?i:256,padding:0,image:t.image}),o=r.from($.svg(),"utf8").toString("base64");n("data:image/svg+xml;base64,"+o)}),e?(0,$.h)("img",{src:e,alt:"QR Code"}):null}},2193(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=".-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:**********}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}"},9934:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.SnackbarInstance=e.SnackbarContainer=e.Snackbar=void 0;let i=r(n(6010)),$=n(6400),o=n(396),s=r(n(2193));e.Snackbar=class{constructor(t){this.items=new Map,this.nextItemKey=0,this.root=null,this.darkMode=t.darkMode}attach(t){this.root=document.createElement("div"),this.root.className="-cbwsdk-snackbar-root",t.appendChild(this.root),this.render()}presentItem(t){let e=this.nextItemKey++;return this.items.set(e,t),this.render(),()=>{this.items.delete(e),this.render()}}clear(){this.items.clear(),this.render()}render(){this.root&&(0,$.render)((0,$.h)("div",null,(0,$.h)(e.SnackbarContainer,{darkMode:this.darkMode},Array.from(this.items.entries()).map(([t,n])=>(0,$.h)(e.SnackbarInstance,Object.assign({},n,{key:t}))))),this.root)}},e.SnackbarContainer=t=>(0,$.h)("div",{class:(0,i.default)("-cbwsdk-snackbar-container")},(0,$.h)("style",null,s.default),(0,$.h)("div",{class:"-cbwsdk-snackbar"},t.children)),e.SnackbarInstance=({autoExpand:t,message:e,menuItems:n})=>{let[r,s]=(0,o.useState)(!0),[x,a]=(0,o.useState)(null!=t&&t);return(0,o.useEffect)(()=>{let t=[window.setTimeout(()=>{s(!1)},1),window.setTimeout(()=>{a(!0)},1e4)];return()=>{t.forEach(window.clearTimeout)}}),(0,$.h)("div",{class:(0,i.default)("-cbwsdk-snackbar-instance",r&&"-cbwsdk-snackbar-instance-hidden",x&&"-cbwsdk-snackbar-instance-expanded")},(0,$.h)("div",{class:"-cbwsdk-snackbar-instance-header",onClick(){a(!x)}},(0,$.h)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+",class:"-cbwsdk-snackbar-instance-header-cblogo"}),(0,$.h)("div",{class:"-cbwsdk-snackbar-instance-header-message"},e),(0,$.h)("div",{class:"-gear-container"},!x&&(0,$.h)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,$.h)("circle",{cx:"12",cy:"12",r:"12",fill:"#F5F7F8"})),(0,$.h)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDYuNzV2LTEuNWwtMS43Mi0uNTdjLS4wOC0uMjctLjE5LS41Mi0uMzItLjc3bC44MS0xLjYyLTEuMDYtMS4wNi0xLjYyLjgxYy0uMjQtLjEzLS41LS4yNC0uNzctLjMyTDYuNzUgMGgtMS41bC0uNTcgMS43MmMtLjI3LjA4LS41My4xOS0uNzcuMzJsLTEuNjItLjgxLTEuMDYgMS4wNi44MSAxLjYyYy0uMTMuMjQtLjI0LjUtLjMyLjc3TDAgNS4yNXYxLjVsMS43Mi41N2MuMDguMjcuMTkuNTMuMzIuNzdsLS44MSAxLjYyIDEuMDYgMS4wNiAxLjYyLS44MWMuMjQuMTMuNS4yMy43Ny4zMkw1LjI1IDEyaDEuNWwuNTctMS43MmMuMjctLjA4LjUyLS4xOS43Ny0uMzJsMS42Mi44MSAxLjA2LTEuMDYtLjgxLTEuNjJjLjEzLS4yNC4yMy0uNS4zMi0uNzdMMTIgNi43NXpNNiA4LjVhMi41IDIuNSAwIDAxMC01IDIuNSAyLjUgMCAwMTAgNXoiIGZpbGw9IiMwNTBGMTkiLz48L3N2Zz4=",class:"-gear-icon",title:"Expand"}))),n&&n.length>0&&(0,$.h)("div",{class:"-cbwsdk-snackbar-instance-menu"},n.map((t,e)=>(0,$.h)("div",{class:(0,i.default)("-cbwsdk-snackbar-instance-menu-item",t.isRed&&"-cbwsdk-snackbar-instance-menu-item-is-red"),onClick:t.onClick,key:e},(0,$.h)("svg",{width:t.svgWidth,height:t.svgHeight,viewBox:"0 0 10 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,$.h)("path",{"fill-rule":t.defaultFillRule,"clip-rule":t.defaultClipRule,d:t.path,fill:"#AAAAAA"})),(0,$.h)("span",{class:(0,i.default)("-cbwsdk-snackbar-instance-menu-item-info",t.isRed&&"-cbwsdk-snackbar-instance-menu-item-info-is-red")},t.info)))))}},1964(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=".-cbwsdk-css-reset .-cbwsdk-spinner{display:inline-block}.-cbwsdk-css-reset .-cbwsdk-spinner svg{display:inline-block;animation:2s linear infinite -cbwsdk-spinner-svg}.-cbwsdk-css-reset .-cbwsdk-spinner svg circle{animation:1.9s ease-in-out infinite both -cbwsdk-spinner-circle;display:block;fill:rgba(0,0,0,0);stroke-dasharray:283;stroke-dashoffset:280;stroke-linecap:round;stroke-width:10px;transform-origin:50% 50%}@keyframes -cbwsdk-spinner-svg{0%{transform:rotateZ(0deg)}100%{transform:rotateZ(360deg)}}@keyframes -cbwsdk-spinner-circle{0%,25%{stroke-dashoffset:280;transform:rotate(0)}50%,75%{stroke-dashoffset:75;transform:rotate(45deg)}100%{stroke-dashoffset:280;transform:rotate(360deg)}}"},6148:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Spinner=void 0;let i=n(6400),$=r(n(1964));e.Spinner=t=>{var e;let n=null!==(e=t.size)&&void 0!==e?e:64,r=t.color||"#000";return(0,i.h)("div",{class:"-cbwsdk-spinner"},(0,i.h)("style",null,$.default),(0,i.h)("svg",{viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",style:{width:n,height:n}},(0,i.h)("circle",{style:{cx:50,cy:50,r:45,stroke:r}})))}},421(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default='.-cbwsdk-css-reset .-cbwsdk-extension-dialog{z-index:**********;position:fixed;top:0;left:0;right:0;bottom:0;display:flex;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-backdrop{z-index:**********;position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-backdrop.light{background-color:rgba(0,0,0,.5)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-backdrop.dark{background-color:rgba(50,53,61,.4)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box{display:flex;position:relative;max-width:500px;flex-direction:column;transform:scale(1);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-hidden{opacity:0;transform:scale(0.85)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top{display:flex;flex-direction:row;border-radius:8px;overflow:hidden;min-height:300px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top.dark{color:#fff;background-color:#000;box-shadow:0 4px 16px rgba(255,255,255,.05)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top.light{background-color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-subtext{margin-top:15px;font-size:12px;line-height:1.5}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-install-region{display:flex;flex-basis:50%;flex-direction:column;justify-content:center;padding:32px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-install-region button{display:block;border-radius:8px;background-color:#1652f0;color:#fff;width:90%;min-width:fit-content;height:44px;margin-top:16px;font-size:16px;padding-left:16px;padding-right:16px;cursor:pointer;font-weight:500;text-align:center}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-install-region button.dark{background-color:#3773f5}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-info-region{display:flex;flex-basis:50%;flex-direction:column;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-info-region.light{background-color:#fafbfc}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-info-region.dark{background-color:#141519}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description{display:flex;flex-direction:row;align-items:center;padding-top:14px;padding-bottom:14px;padding-left:24px;padding-right:32px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description-icon-wrapper{display:block;position:relative;width:40px;height:40px;flex-shrink:0;flex-grow:0;border-radius:20px;background-color:#fff;box-shadow:0px 0px 8px rgba(0,0,0,.04),0px 16px 24px rgba(0,0,0,.06)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description-icon-wrapper img{position:absolute;top:0;bottom:0;left:0;right:0;margin:auto}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description-text{margin-left:16px;flex-grow:1;font-size:13px;line-height:19px;align-self:center}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description-text.light{color:#000}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-top-description-text.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom{display:flex;flex-direction:row;overflow:hidden;border-radius:8px;margin-top:8px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom.light{background-color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom.dark{background-color:#000;box-shadow:0 4px 16px rgba(255,255,255,.05)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-description-region{display:flex;flex-direction:column;justify-content:center;padding:32px;flex-grow:1}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-description{font-size:13px;line-height:19px;margin-top:12px;color:#aaa}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-description.dark{color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-description.dark a{color:#3773f5}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-description a{font-size:inherit;line-height:inherit;color:#1652f0;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-region{position:relative;flex-shrink:0;display:flex;flex-direction:column;justify-content:center;padding-left:24px;padding-right:24px;padding-top:16px;padding-bottom:16px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-wrapper{position:relative;display:block;padding:8px;border-radius:8px;box-shadow:0px 4px 12px rgba(0,0,0,.1);background-color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-wrapper img{display:block}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting{position:absolute;top:0;bottom:0;left:0;right:0;display:flex;flex-direction:column;align-items:center;justify-content:center}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting.light{background-color:rgba(255,255,255,.95)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting.light>p{color:#000}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting.dark{background-color:rgba(20,21,25,.9)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting.dark>p{color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-bottom-qr-connecting>p{font-size:12px;font-weight:bold;margin-top:16px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel{position:absolute;-webkit-appearance:none;display:flex;align-items:center;justify-content:center;top:16px;right:16px;width:24px;height:24px;border-radius:12px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel.light{background-color:#fafbfc}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel.dark{background-color:#141519}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x{position:relative;display:block;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x.light::before,.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x.light::after{background-color:#000}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x.dark::before,.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x.dark::after{background-color:#fff}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x::before,.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x::after{content:"";position:absolute;display:block;top:-1px;left:-7px;width:14px;height:1px;transition:background-color .2s}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x::before{transform:rotate(45deg)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel-x::after{transform:rotate(135deg)}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel:hover .-cbwsdk-link-dialog-box-cancel-x-a,.-cbwsdk-css-reset .-cbwsdk-extension-dialog-box-cancel:hover .-cbwsdk-link-dialog-box-cancel-x-b{background-color:#000}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-container{display:block}.-cbwsdk-css-reset .-cbwsdk-extension-dialog-container-hidden{display:none}.-cbwsdk-css-reset .-cbwsdk-extension-dialog h2{display:block;text-align:left;font-size:22px;font-weight:600;line-height:28px}.-cbwsdk-css-reset .-cbwsdk-extension-dialog h2.light{color:#000}.-cbwsdk-css-reset .-cbwsdk-extension-dialog h2.dark{color:#fff}'},101:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.TryExtensionLinkDialog=void 0;let i=r(n(6010)),$=n(6400),o=n(396),s=n(4643),x=n(3604),a=r(n(4744)),u=r(n(4475)),c=r(n(8714)),l=r(n(8196)),h=n(381),f=n(6148),d=r(n(421));e.TryExtensionLinkDialog=t=>{let{isOpen:e,darkMode:n}=t,[r,s]=(0,o.useState)(!e),[x,a]=(0,o.useState)(!e);(0,o.useEffect)(()=>{let t=[window.setTimeout(()=>{a(!e)},10)];return e?s(!1):t.push(window.setTimeout(()=>{s(!0)},360)),()=>{t.forEach(window.clearTimeout)}},[t.isOpen]);let u=n?"dark":"light";return(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-container",r&&"-cbwsdk-extension-dialog-container-hidden")},(0,$.h)("style",null,d.default),(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-backdrop",u,x&&"-cbwsdk-extension-dialog-backdrop-hidden")}),(0,$.h)("div",{class:"-cbwsdk-extension-dialog"},(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-box",x&&"-cbwsdk-extension-dialog-box-hidden")},(0,$.h)(p,{darkMode:n,onInstallClick(){window.open("https://api.wallet.coinbase.com/rpc/v2/desktop/chrome","_blank")}}),t.connectDisabled?null:(0,$.h)(y,{darkMode:n,version:t.version,sessionId:t.sessionId,sessionSecret:t.sessionSecret,linkAPIUrl:t.linkAPIUrl,isConnected:t.isConnected,isParentConnection:t.isParentConnection}),t.onCancel&&(0,$.h)(b,{darkMode:n,onClick:t.onCancel}))))};let p=({darkMode:t,onInstallClick:e})=>{let[n,r]=(0,o.useState)(!1),s=(0,o.useCallback)(()=>{n?window.location.reload():(e(),r(!0))},[e,n]),x=t?"dark":"light";return(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-box-top",x)},(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-top-install-region"},(0,$.h)("h2",{class:x},"Try the Coinbase Wallet extension"),n&&(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-top-subtext"},"After installing Coinbase Wallet, refresh the page and connect again."),(0,$.h)("button",{type:"button",onClick:s},n?"Refresh":"Install")),(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-box-top-info-region",x)},(0,$.h)(g,{darkMode:t,icon:u.default,text:"Connect to crypto apps with one click"}),(0,$.h)(g,{darkMode:t,icon:c.default,text:"Your private key is stored securely"}),(0,$.h)(g,{darkMode:t,icon:a.default,text:"Works with Ethereum, Polygon, and more"})))},y=t=>{let e=(0,s.createQrUrl)(t.sessionId,t.sessionSecret,t.linkAPIUrl,t.isParentConnection),n=t.darkMode?"dark":"light";return(0,$.h)("div",{"data-testid":"scan-qr-box",class:(0,i.default)("-cbwsdk-extension-dialog-box-bottom",n)},(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-bottom-description-region"},(0,$.h)("h2",{class:n},"Or scan to connect"),(0,$.h)("body",{class:(0,i.default)("-cbwsdk-extension-dialog-box-bottom-description",n)},"Open"," ",(0,$.h)("a",{href:"https://wallet.coinbase.com/",target:"_blank",rel:"noopener noreferrer"},"Coinbase Wallet")," ","on your mobile phone and scan")),(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-bottom-qr-region"},(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-bottom-qr-wrapper"},(0,$.h)(h.QRCode,{content:e,width:150,height:150,fgColor:"#000",bgColor:"transparent",image:{svg:l.default,width:34,height:34}})),(0,$.h)("input",{type:"hidden",name:"cbwsdk-version",value:x.LIB_VERSION}),(0,$.h)("input",{type:"hidden",value:e}),!t.isConnected&&(0,$.h)("div",{"data-testid":"connecting-spinner",class:(0,i.default)("-cbwsdk-extension-dialog-box-bottom-qr-connecting",n)},(0,$.h)(f.Spinner,{size:36,color:t.darkMode?"#FFF":"#000"}),(0,$.h)("p",null,"Connecting..."))))},g=t=>{let e=t.darkMode?"dark":"light";return(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-top-description"},(0,$.h)("div",{class:"-cbwsdk-extension-dialog-box-top-description-icon-wrapper"},(0,$.h)("img",{src:t.icon})),(0,$.h)("body",{class:(0,i.default)("-cbwsdk-extension-dialog-box-top-description-text",e)},t.text))},b=t=>{let e=t.darkMode?"dark":"light";return(0,$.h)("button",{type:"button",class:(0,i.default)("-cbwsdk-extension-dialog-box-cancel",e),onClick:t.onClick},(0,$.h)("div",{class:(0,i.default)("-cbwsdk-extension-dialog-box-cancel-x",e)}))}},8196(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default='<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">\n<circle cx="50" cy="50" r="50" fill="white"/>\n<circle cx="49.9996" cy="49.9996" r="43.6363" fill="#1B53E4"/>\n<circle cx="49.9996" cy="49.9996" r="43.6363" stroke="white"/>\n<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3379 49.9484C19.3379 66.8508 33.04 80.553 49.9425 80.553C66.8449 80.553 80.5471 66.8508 80.5471 49.9484C80.5471 33.0459 66.8449 19.3438 49.9425 19.3438C33.04 19.3438 19.3379 33.0459 19.3379 49.9484ZM44.0817 40.0799C41.8725 40.0799 40.0817 41.8708 40.0817 44.0799V55.8029C40.0817 58.012 41.8725 59.8029 44.0817 59.8029H55.8046C58.0138 59.8029 59.8046 58.012 59.8046 55.8029V44.0799C59.8046 41.8708 58.0138 40.0799 55.8046 40.0799H44.0817Z" fill="white"/>\n</svg>\n\n'},4744(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTggMEMzLjU4IDAgMCAzLjU4IDAgOHMzLjU4IDggOCA4IDgtMy41OCA4LTgtMy41OC04LTgtOFptNS45MSA3aC0xLjk0Yy0uMS0xLjU3LS40Mi0zLS45MS00LjE1IDEuNDguODggMi41NSAyLjM4IDIuODUgNC4xNVpNOCAxNGMtLjQ1IDAtMS43Mi0xLjc3LTEuOTUtNWgzLjljLS4yMyAzLjIzLTEuNSA1LTEuOTUgNVpNNi4wNSA3QzYuMjggMy43NyA3LjU1IDIgOCAyYy40NSAwIDEuNzIgMS43NyAxLjk1IDVoLTMuOVpNNC45NCAyLjg1QzQuNDYgNCA0LjEzIDUuNDMgNC4wMyA3SDIuMDljLjMtMS43NyAxLjM3LTMuMjcgMi44NS00LjE1Wk0yLjA5IDloMS45NGMuMSAxLjU3LjQyIDMgLjkxIDQuMTVBNS45OTggNS45OTggMCAwIDEgMi4wOSA5Wm04Ljk3IDQuMTVjLjQ4LTEuMTUuODEtMi41OC45MS00LjE1aDEuOTRhNS45OTggNS45OTggMCAwIDEtMi44NSA0LjE1WiIgZmlsbD0iIzE2NTJGMCIvPjwvc3ZnPg=="},4475(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTciIGhlaWdodD0iMTciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1LjYzNSAyLjExN2EzLjg4OSAzLjg4OSAwIDAgMC01LjUyMSAwTDYuODkgNS4zMzVBMy44OTQgMy44OTQgMCAwIDAgNS44IDguNzM5Yy4wODMuNTA2LjI2OCAxLjAxMS41NTMgMS40NjYuMTUxLjI1My4zMzYuNDcyLjUzNy42OTFsLjYyMS42MjQgMS4xNDEtMS4xNDYtLjYyLS42MjRhMi4xMDUgMi4xMDUgMCAwIDEtLjQ4Ny0uNzQxIDIuMzQgMi4zNCAwIDAgMSAuNTAzLTIuNTFsMy4yMDYtMy4yMmEyLjI5MyAyLjI5MyAwIDAgMSAzLjIzOSAwYy44OS44OTQuODkgMi4zNDMgMCAzLjI1M2wtMS41MjcgMS41MzNjLjIzNC42NC4zMzUgMS4zMzEuMzAyIDIuMDA1bDIuMzgzLTIuMzkyYzEuNTEtMS41MzQgMS40OTMtNC4wMjgtLjAxNy01LjU2MVoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBkPSJNMTEuMjcxIDcuNzQ1YTMuMTMgMy4xMyAwIDAgMC0uNTU0LS42OWwtLjYyLS42MjQtMS4xNDIgMS4xNDYuNjIxLjYyM2MuMjE4LjIyLjM4Ni40ODkuNDg3Ljc1OC4zMzUuODI2LjE2NyAxLjgyLS41MDQgMi40OTRsLTMuMjA1IDMuMjE5YTIuMjkzIDIuMjkzIDAgMCAxLTMuMjQgMCAyLjMxNiAyLjMxNiAwIDAgMSAwLTMuMjUybDEuNTI4LTEuNTM0YTQuODE1IDQuODE1IDAgMCAxLS4yODUtMi4wMDVsLTIuMzgzIDIuMzkzYTMuOTI3IDMuOTI3IDAgMCAwIDAgNS41NDQgMy45MDkgMy45MDkgMCAwIDAgNS41MzggMGwzLjIwNS0zLjIxOWEzLjk1OCAzLjk1OCAwIDAgMCAxLjA5MS0zLjQwNCA0LjIxMSA0LjIxMSAwIDAgMC0uNTM3LTEuNDQ5WiIgZmlsbD0iIzE2NTJGMCIvPjwvc3ZnPg=="},8714(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgN3Y5aDE0VjdIMVptNy41IDQuMzlWMTRoLTF2LTIuNjFjLS40NC0uMTktLjc1LS42My0uNzUtMS4xNGExLjI1IDEuMjUgMCAwIDEgMi41IDBjMCAuNTEtLjMxLjk1LS43NSAxLjE0Wk01LjY3IDZWNC4zM0M1LjY3IDMuMDUgNi43MSAyIDggMnMyLjMzIDEuMDUgMi4zMyAyLjMzVjZoMlY0LjMzQzEyLjMzIDEuOTQgMTAuMzkgMCA4IDBTMy42NyAxLjk0IDMuNjcgNC4zM1Y2aDJaIiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+"},5755(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ClientMessagePublishEvent=e.ClientMessageSetSessionConfig=e.ClientMessageGetSessionConfig=e.ClientMessageIsLinked=e.ClientMessageHostSession=void 0,e.ClientMessageHostSession=function(t){return Object.assign({type:"HostSession"},t)},e.ClientMessageIsLinked=function(t){return Object.assign({type:"IsLinked"},t)},e.ClientMessageGetSessionConfig=function(t){return Object.assign({type:"GetSessionConfig"},t)},e.ClientMessageSetSessionConfig=function(t){return Object.assign({type:"SetSessionConfig"},t)},e.ClientMessagePublishEvent=function(t){return Object.assign({type:"PublishEvent"},t)}},2191(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EVENTS=void 0,e.EVENTS={STARTED_CONNECTING:"walletlink_sdk.started.connecting",CONNECTED_STATE_CHANGE:"walletlink_sdk.connected",DISCONNECTED:"walletlink_sdk.disconnected",METADATA_DESTROYED:"walletlink_sdk_metadata_destroyed",LINKED:"walletlink_sdk.linked",FAILURE:"walletlink_sdk.generic_failure",SESSION_config_RECEIVED:"walletlink_sdk.session_config_event_received",ETH_ACCOUNTS_STATE:"walletlink_sdk.eth_accounts_state",SESSION_STATE_CHANGE:"walletlink_sdk.session_state_change",UNLINKED_ERROR_STATE:"walletlink_sdk.unlinked_error_state",SKIPPED_CLEARING_SESSION:"walletlink_sdk.skipped_clearing_session",GENERAL_ERROR:"walletlink_sdk.general_error",WEB3_REQUEST:"walletlink_sdk.web3.request",WEB3_REQUEST_PUBLISHED:"walletlink_sdk.web3.request_published",WEB3_RESPONSE:"walletlink_sdk.web3.response",UNKNOWN_ADDRESS_ENCOUNTERED:"walletlink_sdk.unknown_address_encountered"}},179(t,e,n){"use strict";var r,i;Object.defineProperty(e,"__esModule",{value:!0}),e.RxWebSocket=e.ConnectionState=void 0;let $=n(4143),o=n(1717);(i=r=e.ConnectionState||(e.ConnectionState={}))[i.DISCONNECTED=0]="DISCONNECTED",i[i.CONNECTING=1]="CONNECTING",i[i.CONNECTED=2]="CONNECTED",e.RxWebSocket=class{constructor(t,e=WebSocket){this.WebSocketClass=e,this.webSocket=null,this.connectionStateSubject=new $.BehaviorSubject(r.DISCONNECTED),this.incomingDataSubject=new $.Subject,this.url=t.replace(/^http/,"ws")}connect(){return this.webSocket?(0,$.throwError)(Error("webSocket object is not null")):new $.Observable(t=>{let e;try{this.webSocket=e=new this.WebSocketClass(this.url)}catch(n){return void t.error(n)}this.connectionStateSubject.next(r.CONNECTING),e.onclose=e=>{this.clearWebSocket(),t.error(Error("websocket error "+e.code+": "+e.reason)),this.connectionStateSubject.next(r.DISCONNECTED)},e.onopen=e=>{t.next(),t.complete(),this.connectionStateSubject.next(r.CONNECTED)},e.onmessage=t=>{this.incomingDataSubject.next(t.data)}}).pipe((0,o.take)(1))}disconnect(){let{webSocket:t}=this;if(t){this.clearWebSocket(),this.connectionStateSubject.next(r.DISCONNECTED);try{t.close()}catch(e){}}}get connectionState$(){return this.connectionStateSubject.asObservable()}get incomingData$(){return this.incomingDataSubject.asObservable()}get incomingJSONData$(){return this.incomingData$.pipe((0,o.flatMap)(t=>{let e;try{e=JSON.parse(t)}catch(n){return(0,$.empty)()}return(0,$.of)(e)}))}sendData(t){let{webSocket:e}=this;if(!e)throw Error("websocket is not connected");e.send(t)}clearWebSocket(){let{webSocket:t}=this;t&&(this.webSocket=null,t.onclose=null,t.onerror=null,t.onmessage=null,t.onopen=null)}}},6156(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isServerMessageFail=void 0,e.isServerMessageFail=function(t){return t&&"Fail"===t.type&&"number"==typeof t.id&&"string"==typeof t.sessionId&&"string"==typeof t.error}},8876(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WalletSDKConnection=void 0;let r=n(4143),i=n(1717),$=n(3526),o=n(1295),s=n(5755),x=n(2191),a=n(179),u=n(6156);e.WalletSDKConnection=class{constructor(t,e,n,s,u=WebSocket){this.sessionId=t,this.sessionKey=e,this.diagnostic=s,this.subscriptions=new r.Subscription,this.destroyed=!1,this.lastHeartbeatResponse=0,this.nextReqId=(0,o.IntNumber)(1),this.connectedSubject=new r.BehaviorSubject(!1),this.linkedSubject=new r.BehaviorSubject(!1),this.sessionConfigSubject=new r.ReplaySubject(1);let c=new a.RxWebSocket(n+"/rpc",u);this.ws=c,this.subscriptions.add(c.connectionState$.pipe((0,i.tap)(e=>{var n;return null===(n=this.diagnostic)||void 0===n?void 0:n.log(x.EVENTS.CONNECTED_STATE_CHANGE,{state:e,sessionIdHash:$.Session.hash(t)})}),(0,i.skip)(1),(0,i.filter)(t=>t===a.ConnectionState.DISCONNECTED&&!this.destroyed),(0,i.delay)(5e3),(0,i.filter)(t=>!this.destroyed),(0,i.flatMap)(t=>c.connect()),(0,i.retry)()).subscribe()),this.subscriptions.add(c.connectionState$.pipe((0,i.skip)(2),(0,i.switchMap)(t=>(0,r.iif)(()=>t===a.ConnectionState.CONNECTED,this.authenticate().pipe((0,i.tap)(t=>this.sendIsLinked()),(0,i.tap)(t=>this.sendGetSessionConfig()),(0,i.map)(t=>!0)),(0,r.of)(!1))),(0,i.distinctUntilChanged)(),(0,i.catchError)(t=>(0,r.of)(!1))).subscribe(t=>this.connectedSubject.next(t))),this.subscriptions.add(c.connectionState$.pipe((0,i.skip)(1),(0,i.switchMap)(t=>(0,r.iif)(()=>t===a.ConnectionState.CONNECTED,(0,r.timer)(0,1e4)))).subscribe(t=>0===t?this.updateLastHeartbeat():this.heartbeat())),this.subscriptions.add(c.incomingData$.pipe((0,i.filter)(t=>"h"===t)).subscribe(t=>this.updateLastHeartbeat())),this.subscriptions.add(c.incomingJSONData$.pipe((0,i.filter)(t=>["IsLinkedOK","Linked"].includes(t.type))).subscribe(e=>{var n;let r=e;null===(n=this.diagnostic)||void 0===n||n.log(x.EVENTS.LINKED,{sessionIdHash:$.Session.hash(t),linked:r.linked,type:e.type,onlineGuests:r.onlineGuests}),this.linkedSubject.next(r.linked||r.onlineGuests>0)})),this.subscriptions.add(c.incomingJSONData$.pipe((0,i.filter)(t=>["GetSessionConfigOK","SessionConfigUpdated"].includes(t.type))).subscribe(e=>{var n;let r=e;null===(n=this.diagnostic)||void 0===n||n.log(x.EVENTS.SESSION_config_RECEIVED,{sessionIdHash:$.Session.hash(t),metadata_keys:r&&r.metadata?Object.keys(r.metadata):void 0}),this.sessionConfigSubject.next({webhookId:r.webhookId,webhookUrl:r.webhookUrl,metadata:r.metadata})}))}connect(){var t;if(this.destroyed)throw Error("instance is destroyed");null===(t=this.diagnostic)||void 0===t||t.log(x.EVENTS.STARTED_CONNECTING,{sessionIdHash:$.Session.hash(this.sessionId)}),this.ws.connect().subscribe()}destroy(){var t;this.subscriptions.unsubscribe(),this.ws.disconnect(),null===(t=this.diagnostic)||void 0===t||t.log(x.EVENTS.DISCONNECTED,{sessionIdHash:$.Session.hash(this.sessionId)}),this.destroyed=!0}get isDestroyed(){return this.destroyed}get connected$(){return this.connectedSubject.asObservable()}get onceConnected$(){return this.connected$.pipe((0,i.filter)(t=>t),(0,i.take)(1),(0,i.map)(()=>{}))}get linked$(){return this.linkedSubject.asObservable()}get onceLinked$(){return this.linked$.pipe((0,i.filter)(t=>t),(0,i.take)(1),(0,i.map)(()=>{}))}get sessionConfig$(){return this.sessionConfigSubject.asObservable()}get incomingEvent$(){return this.ws.incomingJSONData$.pipe((0,i.filter)(t=>{if("Event"!==t.type)return!1;let e=t;return"string"==typeof e.sessionId&&"string"==typeof e.eventId&&"string"==typeof e.event&&"string"==typeof e.data}),(0,i.map)(t=>t))}setSessionMetadata(t,e){let n=(0,s.ClientMessageSetSessionConfig)({id:(0,o.IntNumber)(this.nextReqId++),sessionId:this.sessionId,metadata:{[t]:e}});return this.onceConnected$.pipe((0,i.flatMap)(t=>this.makeRequest(n)),(0,i.map)(t=>{if((0,u.isServerMessageFail)(t))throw Error(t.error||"failed to set session metadata")}))}publishEvent(t,e,n=!1){let r=(0,s.ClientMessagePublishEvent)({id:(0,o.IntNumber)(this.nextReqId++),sessionId:this.sessionId,event:t,data:e,callWebhook:n});return this.onceLinked$.pipe((0,i.flatMap)(t=>this.makeRequest(r)),(0,i.map)(t=>{if((0,u.isServerMessageFail)(t))throw Error(t.error||"failed to publish event");return t.eventId}))}sendData(t){this.ws.sendData(JSON.stringify(t))}updateLastHeartbeat(){this.lastHeartbeatResponse=Date.now()}heartbeat(){if(Date.now()-this.lastHeartbeatResponse>2e4)this.ws.disconnect();else try{this.ws.sendData("h")}catch(t){}}makeRequest(t,e=6e4){let n=t.id;try{this.sendData(t)}catch($){return(0,r.throwError)($)}return this.ws.incomingJSONData$.pipe((0,i.timeoutWith)(e,(0,r.throwError)(Error("request "+n+" timed out"))),(0,i.filter)(t=>t.id===n),(0,i.take)(1))}authenticate(){let t=(0,s.ClientMessageHostSession)({id:(0,o.IntNumber)(this.nextReqId++),sessionId:this.sessionId,sessionKey:this.sessionKey});return this.makeRequest(t).pipe((0,i.map)(t=>{if((0,u.isServerMessageFail)(t))throw Error(t.error||"failed to authentcate")}))}sendIsLinked(){let t=(0,s.ClientMessageIsLinked)({id:(0,o.IntNumber)(this.nextReqId++),sessionId:this.sessionId});this.sendData(t)}sendGetSessionConfig(){let t=(0,s.ClientMessageGetSessionConfig)({id:(0,o.IntNumber)(this.nextReqId++),sessionId:this.sessionId});this.sendData(t)}}},5811(t,e,n){"use strict";let r=n(9016),i=n(3143);n(9016),n(3143),r.CoinbaseWalletSDK,"undefined"!=typeof window&&(window.CoinbaseWalletSDK=r.CoinbaseWalletSDK,window.CoinbaseWalletProvider=i.CoinbaseWalletProvider,window.WalletLink=r.CoinbaseWalletSDK,window.WalletLinkProvider=i.CoinbaseWalletProvider)},9682(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ScopedLocalStorage=void 0,e.ScopedLocalStorage=class{constructor(t){this.scope=t}setItem(t,e){localStorage.setItem(this.scopedKey(t),e)}getItem(t){return localStorage.getItem(this.scopedKey(t))}removeItem(t){localStorage.removeItem(this.scopedKey(t))}clear(){let t=this.scopedKey(""),e=[];for(let n=0;n<localStorage.length;n++){let r=localStorage.key(n);"string"==typeof r&&r.startsWith(t)&&e.push(r)}e.forEach(t=>localStorage.removeItem(t))}scopedKey(t){return this.scope+":"+t}}},1119(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default='@namespace svg "http://www.w3.org/2000/svg";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;bottom:auto;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;content:normal;counter-increment:none;counter-reset:none;cursor:auto;direction:ltr;display:block;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;left:auto;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;max-height:none;max-width:none;min-height:0;min-width:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:"\\201C" "\\201D" "\\2018" "\\2019";right:auto;tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;top:auto;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;width:auto;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset *{box-sizing:border-box;display:initial;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}'},7162:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.injectCssReset=void 0;let i=r(n(1119));e.injectCssReset=function(){let t=document.createElement("style");t.type="text/css",t.appendChild(document.createTextNode(i.default)),document.documentElement.appendChild(t)}},3143:function(t,e,n){"use strict";var r=n(8764).Buffer,i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.CoinbaseWalletProvider=void 0;let $=i(n(9394)),o=i(n(3550)),s=n(9826),x=n(2191),a=n(3526),u=n(5633),c=n(4643),l=i(n(4497)),h=n(3648),f=n(8565),d=n(5313),p="DefaultChainId",y="DefaultJsonRpcUrl",g="HasChainBeenSwitched",b="HasChainOverriddenFromRelay";class _ extends $.default{constructor(t){var e,n;super(),this._filterPolyfill=new h.FilterPolyfill(this),this._subscriptionManager=new d.SubscriptionManager(this),this._relay=null,this._addresses=[],this.hasMadeFirstChainChangedEmission=!1,this._send=this.send.bind(this),this._sendAsync=this.sendAsync.bind(this),this.setProviderInfo=this.setProviderInfo.bind(this),this.updateProviderInfo=this.updateProviderInfo.bind(this),this.getChainId=this.getChainId.bind(this),this.setAppInfo=this.setAppInfo.bind(this),this.enable=this.enable.bind(this),this.close=this.close.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this.request=this.request.bind(this),this._setAddresses=this._setAddresses.bind(this),this.scanQRCode=this.scanQRCode.bind(this),this.genericRequest=this.genericRequest.bind(this),this._jsonRpcUrlFromOpts=t.jsonRpcUrl,this._overrideIsMetaMask=t.overrideIsMetaMask,this._relayProvider=t.relayProvider,this._storage=t.storage,this._relayEventManager=t.relayEventManager,this.diagnostic=t.diagnosticLogger,this.reloadOnDisconnect=!0,this.isCoinbaseWallet=null===(e=t.overrideIsCoinbaseWallet)||void 0===e||e,this.isCoinbaseBrowser=null!==(n=t.overrideIsCoinbaseBrowser)&&void 0!==n&&n,this.qrUrl=t.qrUrl,this.supportsAddressSwitching=t.supportsAddressSwitching;let r=this.getChainId(),i=(0,c.prepend0x)(r.toString(16));this.emit("connect",{chainIdStr:i});let $=this._storage.getItem(u.LOCAL_STORAGE_ADDRESSES_KEY);if($){let o=$.split(" ");""!==o[0]&&(this._addresses=o.map(t=>(0,c.ensureAddressString)(t)),this.emit("accountsChanged",o))}this._subscriptionManager.events.on("notification",t=>{this.emit("message",{type:t.method,data:t.params})}),this._addresses.length>0&&this.initializeRelay(),window.addEventListener("message",t=>{var e;if("walletLinkMessage"===t.data.type&&"defaultChainChanged"===t.data.data.action){let n=t.data.data.chainId,r=null!==(e=t.data.data.jsonRpcUrl)&&void 0!==e?e:this.jsonRpcUrl;this.updateProviderInfo(r,Number(n),!0)}})}get selectedAddress(){return this._addresses[0]||void 0}get networkVersion(){return this.getChainId().toString(10)}get chainId(){return(0,c.prepend0x)(this.getChainId().toString(16))}get isWalletLink(){return!0}get isMetaMask(){return this._overrideIsMetaMask}get host(){return this.jsonRpcUrl}get connected(){return!0}isConnected(){return!0}get jsonRpcUrl(){var t;return null!==(t=this._storage.getItem(y))&&void 0!==t?t:this._jsonRpcUrlFromOpts}set jsonRpcUrl(t){this._storage.setItem(y,t)}get isChainOverridden(){return"true"===this._storage.getItem(b)}set isChainOverridden(t){this._storage.setItem(b,t.toString())}disableReloadOnDisconnect(){this.reloadOnDisconnect=!1}setProviderInfo(t,e){this.isChainOverridden||this.updateProviderInfo(t,this.getChainId(),!1)}updateProviderInfo(t,e,n){if("true"===this._storage.getItem(g)&&n)return;n&&(this.isChainOverridden=!0),this.jsonRpcUrl=t;let r=this.getChainId();this._storage.setItem(p,e.toString(10)),(0,c.ensureIntNumber)(e)===r&&this.hasMadeFirstChainChangedEmission||(this.emit("chainChanged",this.getChainId()),this.hasMadeFirstChainChangedEmission=!0)}async watchAsset(t,e,n,r,i,$){let o=await this.initializeRelay();return!!(await o.watchAsset(t,e,n,r,i,null==$?void 0:$.toString()).promise).result}async addEthereumChain(t,e,n,r,i,$){var o,s;if((0,c.ensureIntNumber)(t)===this.getChainId())return!1;let x=await this.initializeRelay(),a=x.inlineAddEthereumChain(t.toString());this._isAuthorized()||a||await x.requestEthereumAccounts().promise;let u=await x.addEthereumChain(t.toString(),e,i,n,r,$).promise;return!0===(null===(o=u.result)||void 0===o?void 0:o.isApproved)&&(this._storage.setItem(g,"true"),this.updateProviderInfo(e[0],t,!1)),!0===(null===(s=u.result)||void 0===s?void 0:s.isApproved)}async switchEthereumChain(t){if((0,c.ensureIntNumber)(t)===this.getChainId())return;let e=await this.initializeRelay(),n=await e.switchEthereumChain(t.toString(10)).promise;if(n.errorCode)throw s.ethErrors.provider.custom({code:n.errorCode});let r=n.result;r.isApproved&&r.rpcUrl.length>0&&(this._storage.setItem(g,"true"),this.updateProviderInfo(r.rpcUrl,t,!1))}setAppInfo(t,e){this.initializeRelay().then(n=>n.setAppInfo(t,e))}async enable(){var t;return null===(t=this.diagnostic)||void 0===t||t.log(x.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::enable",addresses_length:this._addresses.length,sessionIdHash:this._relay?a.Session.hash(this._relay.session.id):void 0}),this._addresses.length>0?[...this._addresses]:await this._send(f.JSONRPCMethod.eth_requestAccounts)}async close(){(await this.initializeRelay()).resetAndReload()}send(t,e){if("string"==typeof t){let n={jsonrpc:"2.0",id:0,method:t,params:Array.isArray(e)?e:void 0!==e?[e]:[]};return this._sendRequestAsync(n).then(t=>t.result)}if("function"==typeof e){let r=t,i=e;return this._sendAsync(r,i)}if(Array.isArray(t))return t.map(t=>this._sendRequest(t));let $=t;return this._sendRequest($)}async sendAsync(t,e){if("function"!=typeof e)throw Error("callback is required");if(Array.isArray(t)){let n=e;return void this._sendMultipleRequestsAsync(t).then(t=>n(null,t)).catch(t=>n(t,null))}let r=e;return this._sendRequestAsync(t).then(t=>r(null,t)).catch(t=>r(t,null))}async request(t){if(!t||"object"!=typeof t||Array.isArray(t))throw s.ethErrors.rpc.invalidRequest({message:"Expected a single, non-array, object argument.",data:t});let{method:e,params:n}=t;if("string"!=typeof e||0===e.length)throw s.ethErrors.rpc.invalidRequest({message:"'args.method' must be a non-empty string.",data:t});if(void 0!==n&&!Array.isArray(n)&&("object"!=typeof n||null===n))throw s.ethErrors.rpc.invalidRequest({message:"'args.params' must be an object or array if provided.",data:t});let r=void 0===n?[]:n,i=this._relayEventManager.makeRequestId();return(await this._sendRequestAsync({method:e,params:r,jsonrpc:"2.0",id:i})).result}async scanQRCode(t){let e=await this.initializeRelay(),n=await e.scanQRCode((0,c.ensureRegExpString)(t)).promise;if("string"!=typeof n.result)throw Error("result was not a string");return n.result}async genericRequest(t,e){let n=await this.initializeRelay(),r=await n.genericRequest(t,e).promise;if("string"!=typeof r.result)throw Error("result was not a string");return r.result}async selectProvider(t){let e=await this.initializeRelay(),n=await e.selectProvider(t).promise;if("string"!=typeof n.result)throw Error("result was not a string");return n.result}supportsSubscriptions(){return!1}subscribe(){throw Error("Subscriptions are not supported")}unsubscribe(){throw Error("Subscriptions are not supported")}disconnect(){return!0}_sendRequest(t){let e={jsonrpc:"2.0",id:t.id},{method:n}=t;if(e.result=this._handleSynchronousMethods(t),void 0===e.result)throw Error("Coinbase Wallet does not support calling "+n+" synchronously without a callback. Please provide a callback parameter to call "+n+" asynchronously.");return e}_setAddresses(t,e){if(!Array.isArray(t))throw Error("addresses is not an array");let n=t.map(t=>(0,c.ensureAddressString)(t));JSON.stringify(n)!==JSON.stringify(this._addresses)&&(this._addresses.length>0&&!1===this.supportsAddressSwitching&&!e||(this._addresses=n,this.emit("accountsChanged",this._addresses),this._storage.setItem(u.LOCAL_STORAGE_ADDRESSES_KEY,n.join(" "))))}_sendRequestAsync(t){return new Promise((e,n)=>{try{let r=this._handleSynchronousMethods(t);if(void 0!==r)return e({jsonrpc:"2.0",id:t.id,result:r});let i=this._handleAsynchronousFilterMethods(t);if(void 0!==i)return void i.then(n=>e(Object.assign(Object.assign({},n),{id:t.id}))).catch(t=>n(t));let $=this._handleSubscriptionMethods(t);if(void 0!==$)return void $.then(n=>e({jsonrpc:"2.0",id:t.id,result:n.result})).catch(t=>n(t))}catch(o){return n(o)}this._handleAsynchronousMethods(t).then(n=>n&&e(Object.assign(Object.assign({},n),{id:t.id}))).catch(t=>n(t))})}_sendMultipleRequestsAsync(t){return Promise.all(t.map(t=>this._sendRequestAsync(t)))}_handleSynchronousMethods(t){let{method:e}=t,n=t.params||[];switch(e){case f.JSONRPCMethod.eth_accounts:return this._eth_accounts();case f.JSONRPCMethod.eth_coinbase:return this._eth_coinbase();case f.JSONRPCMethod.eth_uninstallFilter:return this._eth_uninstallFilter(n);case f.JSONRPCMethod.net_version:return this._net_version();case f.JSONRPCMethod.eth_chainId:return this._eth_chainId();default:return}}async _handleAsynchronousMethods(t){let{method:e}=t,n=t.params||[];switch(e){case f.JSONRPCMethod.eth_requestAccounts:return this._eth_requestAccounts();case f.JSONRPCMethod.eth_sign:return this._eth_sign(n);case f.JSONRPCMethod.eth_ecRecover:return this._eth_ecRecover(n);case f.JSONRPCMethod.personal_sign:return this._personal_sign(n);case f.JSONRPCMethod.personal_ecRecover:return this._personal_ecRecover(n);case f.JSONRPCMethod.eth_signTransaction:return this._eth_signTransaction(n);case f.JSONRPCMethod.eth_sendRawTransaction:return this._eth_sendRawTransaction(n);case f.JSONRPCMethod.eth_sendTransaction:return this._eth_sendTransaction(n);case f.JSONRPCMethod.eth_signTypedData_v1:return this._eth_signTypedData_v1(n);case f.JSONRPCMethod.eth_signTypedData_v2:return this._throwUnsupportedMethodError();case f.JSONRPCMethod.eth_signTypedData_v3:return this._eth_signTypedData_v3(n);case f.JSONRPCMethod.eth_signTypedData_v4:case f.JSONRPCMethod.eth_signTypedData:return this._eth_signTypedData_v4(n);case f.JSONRPCMethod.cbWallet_arbitrary:return this._cbwallet_arbitrary(n);case f.JSONRPCMethod.wallet_addEthereumChain:return this._wallet_addEthereumChain(n);case f.JSONRPCMethod.wallet_switchEthereumChain:return this._wallet_switchEthereumChain(n);case f.JSONRPCMethod.wallet_watchAsset:return this._wallet_watchAsset(n)}return(await this.initializeRelay()).makeEthereumJSONRPCRequest(t,this.jsonRpcUrl)}_handleAsynchronousFilterMethods(t){let{method:e}=t,n=t.params||[];switch(e){case f.JSONRPCMethod.eth_newFilter:return this._eth_newFilter(n);case f.JSONRPCMethod.eth_newBlockFilter:return this._eth_newBlockFilter();case f.JSONRPCMethod.eth_newPendingTransactionFilter:return this._eth_newPendingTransactionFilter();case f.JSONRPCMethod.eth_getFilterChanges:return this._eth_getFilterChanges(n);case f.JSONRPCMethod.eth_getFilterLogs:return this._eth_getFilterLogs(n)}}_handleSubscriptionMethods(t){switch(t.method){case f.JSONRPCMethod.eth_subscribe:case f.JSONRPCMethod.eth_unsubscribe:return this._subscriptionManager.handleRequest(t)}}_isKnownAddress(t){try{let e=(0,c.ensureAddressString)(t);return this._addresses.map(t=>(0,c.ensureAddressString)(t)).includes(e)}catch(n){}return!1}_ensureKnownAddress(t){var e;if(!this._isKnownAddress(t))throw null===(e=this.diagnostic)||void 0===e||e.log(x.EVENTS.UNKNOWN_ADDRESS_ENCOUNTERED),Error("Unknown Ethereum address")}_prepareTransactionParams(t){let e=t.from?(0,c.ensureAddressString)(t.from):this.selectedAddress;if(!e)throw Error("Ethereum address is unavailable");return this._ensureKnownAddress(e),{fromAddress:e,toAddress:t.to?(0,c.ensureAddressString)(t.to):null,weiValue:null!=t.value?(0,c.ensureBN)(t.value):new o.default(0),data:t.data?(0,c.ensureBuffer)(t.data):r.alloc(0),nonce:null!=t.nonce?(0,c.ensureIntNumber)(t.nonce):null,gasPriceInWei:null!=t.gasPrice?(0,c.ensureBN)(t.gasPrice):null,maxFeePerGas:null!=t.maxFeePerGas?(0,c.ensureBN)(t.maxFeePerGas):null,maxPriorityFeePerGas:null!=t.maxPriorityFeePerGas?(0,c.ensureBN)(t.maxPriorityFeePerGas):null,gasLimit:null!=t.gas?(0,c.ensureBN)(t.gas):null,chainId:this.getChainId()}}_isAuthorized(){return this._addresses.length>0}_requireAuthorization(){if(!this._isAuthorized())throw s.ethErrors.provider.unauthorized({})}_throwUnsupportedMethodError(){throw s.ethErrors.provider.unsupportedMethod({})}async _signEthereumMessage(t,e,n,r){this._ensureKnownAddress(e);try{let i=await this.initializeRelay();return{jsonrpc:"2.0",id:0,result:(await i.signEthereumMessage(t,e,n,r).promise).result}}catch($){if("string"==typeof $.message&&$.message.match(/(denied|rejected)/i))throw s.ethErrors.provider.userRejectedRequest("User denied message signature");throw $}}async _ethereumAddressFromSignedMessage(t,e,n){let r=await this.initializeRelay();return{jsonrpc:"2.0",id:0,result:(await r.ethereumAddressFromSignedMessage(t,e,n).promise).result}}_eth_accounts(){return[...this._addresses]}_eth_coinbase(){return this.selectedAddress||null}_net_version(){return this.getChainId().toString(10)}_eth_chainId(){return(0,c.hexStringFromIntNumber)(this.getChainId())}getChainId(){let t=this._storage.getItem(p)||"1",e=parseInt(t,10);return(0,c.ensureIntNumber)(e)}async _eth_requestAccounts(){var t;if(null===(t=this.diagnostic)||void 0===t||t.log(x.EVENTS.ETH_ACCOUNTS_STATE,{method:"provider::_eth_requestAccounts",addresses_length:this._addresses.length,sessionIdHash:this._relay?a.Session.hash(this._relay.session.id):void 0}),this._addresses.length>0)return Promise.resolve({jsonrpc:"2.0",id:0,result:this._addresses});let e;try{let n=await this.initializeRelay();e=await n.requestEthereumAccounts().promise}catch(r){if("string"==typeof r.message&&r.message.match(/(denied|rejected)/i))throw s.ethErrors.provider.userRejectedRequest("User denied account authorization");throw r}if(!e.result)throw Error("accounts received is empty");return this._setAddresses(e.result),{jsonrpc:"2.0",id:0,result:this._addresses}}_eth_sign(t){this._requireAuthorization();let e=(0,c.ensureAddressString)(t[0]),n=(0,c.ensureBuffer)(t[1]);return this._signEthereumMessage(n,e,!1)}_eth_ecRecover(t){let e=(0,c.ensureBuffer)(t[0]),n=(0,c.ensureBuffer)(t[1]);return this._ethereumAddressFromSignedMessage(e,n,!1)}_personal_sign(t){this._requireAuthorization();let e=(0,c.ensureBuffer)(t[0]),n=(0,c.ensureAddressString)(t[1]);return this._signEthereumMessage(e,n,!0)}_personal_ecRecover(t){let e=(0,c.ensureBuffer)(t[0]),n=(0,c.ensureBuffer)(t[1]);return this._ethereumAddressFromSignedMessage(e,n,!0)}async _eth_signTransaction(t){this._requireAuthorization();let e=this._prepareTransactionParams(t[0]||{});try{let n=await this.initializeRelay();return{jsonrpc:"2.0",id:0,result:(await n.signEthereumTransaction(e).promise).result}}catch(r){if("string"==typeof r.message&&r.message.match(/(denied|rejected)/i))throw s.ethErrors.provider.userRejectedRequest("User denied transaction signature");throw r}}async _eth_sendRawTransaction(t){let e=(0,c.ensureBuffer)(t[0]),n=await this.initializeRelay();return{jsonrpc:"2.0",id:0,result:(await n.submitEthereumTransaction(e,this.getChainId()).promise).result}}async _eth_sendTransaction(t){this._requireAuthorization();let e=this._prepareTransactionParams(t[0]||{});try{let n=await this.initializeRelay();return{jsonrpc:"2.0",id:0,result:(await n.signAndSubmitEthereumTransaction(e).promise).result}}catch(r){if("string"==typeof r.message&&r.message.match(/(denied|rejected)/i))throw s.ethErrors.provider.userRejectedRequest("User denied transaction signature");throw r}}async _eth_signTypedData_v1(t){this._requireAuthorization();let e=(0,c.ensureParsedJSONObject)(t[0]),n=(0,c.ensureAddressString)(t[1]);this._ensureKnownAddress(n);let r=l.default.hashForSignTypedDataLegacy({data:e}),i=JSON.stringify(e,null,2);return this._signEthereumMessage(r,n,!1,i)}async _eth_signTypedData_v3(t){this._requireAuthorization();let e=(0,c.ensureAddressString)(t[0]),n=(0,c.ensureParsedJSONObject)(t[1]);this._ensureKnownAddress(e);let r=l.default.hashForSignTypedData_v3({data:n}),i=JSON.stringify(n,null,2);return this._signEthereumMessage(r,e,!1,i)}async _eth_signTypedData_v4(t){this._requireAuthorization();let e=(0,c.ensureAddressString)(t[0]),n=(0,c.ensureParsedJSONObject)(t[1]);this._ensureKnownAddress(e);let r=l.default.hashForSignTypedData_v4({data:n}),i=JSON.stringify(n,null,2);return this._signEthereumMessage(r,e,!1,i)}async _cbwallet_arbitrary(t){let e=t[0],n=t[1];if("string"!=typeof n)throw Error("parameter must be a string");if("object"!=typeof e||null===e)throw Error("parameter must be an object");return{jsonrpc:"2.0",id:0,result:await this.genericRequest(e,n)}}async _wallet_addEthereumChain(t){var e,n,r,i;let $=t[0];if(0===(null===(e=$.rpcUrls)||void 0===e?void 0:e.length))return{jsonrpc:"2.0",id:0,error:{code:2,message:"please pass in at least 1 rpcUrl"}};if(!$.chainName||""===$.chainName.trim())throw s.ethErrors.provider.custom({code:0,message:"chainName is a required field"});if(!$.nativeCurrency)throw s.ethErrors.provider.custom({code:0,message:"nativeCurrency is a required field"});let o=parseInt($.chainId,16);return await this.addEthereumChain(o,null!==(n=$.rpcUrls)&&void 0!==n?n:[],null!==(r=$.blockExplorerUrls)&&void 0!==r?r:[],$.chainName,null!==(i=$.iconUrls)&&void 0!==i?i:[],$.nativeCurrency)?{jsonrpc:"2.0",id:0,result:null}:{jsonrpc:"2.0",id:0,error:{code:2,message:"unable to add ethereum chain"}}}async _wallet_switchEthereumChain(t){let e=t[0];return await this.switchEthereumChain(parseInt(e.chainId,16)),{jsonrpc:"2.0",id:0,result:null}}async _wallet_watchAsset(t){let e=Array.isArray(t)?t[0]:t;if(!e.type)throw s.ethErrors.rpc.invalidParams({message:"Type is required"});if("ERC20"!==(null==e?void 0:e.type))throw s.ethErrors.rpc.invalidParams({message:"Asset of type '"+e.type+"' is not supported"});if(!(null==e?void 0:e.options))throw s.ethErrors.rpc.invalidParams({message:"Options are required"});if(!(null==e?void 0:e.options.address))throw s.ethErrors.rpc.invalidParams({message:"Address is required"});let n=this.getChainId(),{address:r,symbol:i,image:$,decimals:o}=e.options;return{jsonrpc:"2.0",id:0,result:await this.watchAsset(e.type,r,i,o,$,n)}}_eth_uninstallFilter(t){let e=(0,c.ensureHexString)(t[0]);return this._filterPolyfill.uninstallFilter(e)}async _eth_newFilter(t){let e=t[0];return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newFilter(e)}}async _eth_newBlockFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newBlockFilter()}}async _eth_newPendingTransactionFilter(){return{jsonrpc:"2.0",id:0,result:await this._filterPolyfill.newPendingTransactionFilter()}}_eth_getFilterChanges(t){let e=(0,c.ensureHexString)(t[0]);return this._filterPolyfill.getFilterChanges(e)}_eth_getFilterLogs(t){let e=(0,c.ensureHexString)(t[0]);return this._filterPolyfill.getFilterLogs(e)}initializeRelay(){return this._relay?Promise.resolve(this._relay):this._relayProvider().then(t=>(t.setAccountsCallback((t,e)=>this._setAddresses(t,e)),t.setChainCallback((t,e)=>{this.updateProviderInfo(e,parseInt(t,10),!0)}),this._relay=t,t))}}e.CoinbaseWalletProvider=_},3648(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.filterFromParam=e.FilterPolyfill=void 0;let r=n(1295),i=n(4643),$={jsonrpc:"2.0",id:0};function o(t){return{fromBlock:x(t.fromBlock),toBlock:x(t.toBlock),addresses:void 0===t.address?null:Array.isArray(t.address)?t.address:[t.address],topics:t.topics||[]}}function s(t){let e={fromBlock:a(t.fromBlock),toBlock:a(t.toBlock),topics:t.topics};return null!==t.addresses&&(e.address=t.addresses),e}function x(t){if(void 0===t||"latest"===t||"pending"===t)return"latest";if("earliest"===t)return(0,r.IntNumber)(0);if((0,i.isHexString)(t))return(0,i.intNumberFromHexString)(t);throw Error("Invalid block option: "+String(t))}function a(t){return"latest"===t?t:(0,i.hexStringFromIntNumber)(t)}function u(){return Object.assign(Object.assign({},$),{error:{code:-32e3,message:"filter not found"}})}function c(){return Object.assign(Object.assign({},$),{result:[]})}e.FilterPolyfill=class{constructor(t){this.logFilters=new Map,this.blockFilters=new Set,this.pendingTransactionFilters=new Set,this.cursors=new Map,this.timeouts=new Map,this.nextFilterId=(0,r.IntNumber)(1),this.provider=t}async newFilter(t){let e=o(t),n=this.makeFilterId(),r=await this.setInitialCursorPosition(n,e.fromBlock);return console.log("Installing new log filter("+n+"):",e,"initial cursor position:",r),this.logFilters.set(n,e),this.setFilterTimeout(n),(0,i.hexStringFromIntNumber)(n)}async newBlockFilter(){let t=this.makeFilterId(),e=await this.setInitialCursorPosition(t,"latest");return console.log("Installing new block filter ("+t+") with initial cursor position:",e),this.blockFilters.add(t),this.setFilterTimeout(t),(0,i.hexStringFromIntNumber)(t)}async newPendingTransactionFilter(){let t=this.makeFilterId(),e=await this.setInitialCursorPosition(t,"latest");return console.log("Installing new block filter ("+t+") with initial cursor position:",e),this.pendingTransactionFilters.add(t),this.setFilterTimeout(t),(0,i.hexStringFromIntNumber)(t)}uninstallFilter(t){let e=(0,i.intNumberFromHexString)(t);return console.log("Uninstalling filter ("+e+")"),this.deleteFilter(e),!0}getFilterChanges(t){let e=(0,i.intNumberFromHexString)(t);return this.timeouts.has(e)&&this.setFilterTimeout(e),this.logFilters.has(e)?this.getLogFilterChanges(e):this.blockFilters.has(e)?this.getBlockFilterChanges(e):this.pendingTransactionFilters.has(e)?this.getPendingTransactionFilterChanges(e):Promise.resolve(u())}async getFilterLogs(t){let e=(0,i.intNumberFromHexString)(t),n=this.logFilters.get(e);return n?this.sendAsyncPromise(Object.assign(Object.assign({},$),{method:"eth_getLogs",params:[s(n)]})):u()}makeFilterId(){return(0,r.IntNumber)(++this.nextFilterId)}sendAsyncPromise(t){return new Promise((e,n)=>{this.provider.sendAsync(t,(t,r)=>t?n(t):Array.isArray(r)||null==r?n(Error("unexpected response received: "+JSON.stringify(r))):void e(r))})}deleteFilter(t){console.log("Deleting filter ("+t+")"),this.logFilters.delete(t),this.blockFilters.delete(t),this.pendingTransactionFilters.delete(t),this.cursors.delete(t),this.timeouts.delete(t)}async getLogFilterChanges(t){let e=this.logFilters.get(t),n=this.cursors.get(t);if(!n||!e)return u();let o=await this.getCurrentBlockHeight(),x="latest"===e.toBlock?o:e.toBlock;if(n>o||n>e.toBlock)return c();console.log("Fetching logs from "+n+" to "+x+" for filter "+t);let a=await this.sendAsyncPromise(Object.assign(Object.assign({},$),{method:"eth_getLogs",params:[s(Object.assign(Object.assign({},e),{fromBlock:n,toBlock:x}))]}));if(Array.isArray(a.result)){let l=a.result.map(t=>(0,i.intNumberFromHexString)(t.blockNumber||"0x0")),h=Math.max(...l);if(h&&h>n){let f=(0,r.IntNumber)(h+1);console.log("Moving cursor position for filter ("+t+") from "+n+" to "+f),this.cursors.set(t,f)}}return a}async getBlockFilterChanges(t){let e=this.cursors.get(t);if(!e)return u();let n=await this.getCurrentBlockHeight();if(e>n)return c();console.log("Fetching blocks from "+e+" to "+n+" for filter ("+t+")");let o=(await Promise.all((0,i.range)(e,n+1).map(t=>this.getBlockHashByNumber((0,r.IntNumber)(t))))).filter(t=>!!t),s=(0,r.IntNumber)(e+o.length);return console.log("Moving cursor position for filter ("+t+") from "+e+" to "+s),this.cursors.set(t,s),Object.assign(Object.assign({},$),{result:o})}async getPendingTransactionFilterChanges(t){return Promise.resolve(c())}async setInitialCursorPosition(t,e){let n=await this.getCurrentBlockHeight(),r="number"==typeof e&&e>n?e:n;return this.cursors.set(t,r),r}setFilterTimeout(t){let e=this.timeouts.get(t);e&&window.clearTimeout(e);let n=window.setTimeout(()=>{console.log("Filter ("+t+") timed out"),this.deleteFilter(t)},3e5);this.timeouts.set(t,n)}async getCurrentBlockHeight(){let{result:t}=await this.sendAsyncPromise(Object.assign(Object.assign({},$),{method:"eth_blockNumber",params:[]}));return(0,i.intNumberFromHexString)((0,i.ensureHexString)(t))}async getBlockHashByNumber(t){let e=await this.sendAsyncPromise(Object.assign(Object.assign({},$),{method:"eth_getBlockByNumber",params:[(0,i.hexStringFromIntNumber)(t),!1]}));return e.result&&"string"==typeof e.result.hash?(0,i.ensureHexString)(e.result.hash):null}},e.filterFromParam=o},8565(t,e){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.JSONRPCMethod=void 0,(n=e.JSONRPCMethod||(e.JSONRPCMethod={})).eth_accounts="eth_accounts",n.eth_coinbase="eth_coinbase",n.net_version="net_version",n.eth_chainId="eth_chainId",n.eth_uninstallFilter="eth_uninstallFilter",n.eth_requestAccounts="eth_requestAccounts",n.eth_sign="eth_sign",n.eth_ecRecover="eth_ecRecover",n.personal_sign="personal_sign",n.personal_ecRecover="personal_ecRecover",n.eth_signTransaction="eth_signTransaction",n.eth_sendRawTransaction="eth_sendRawTransaction",n.eth_sendTransaction="eth_sendTransaction",n.eth_signTypedData_v1="eth_signTypedData_v1",n.eth_signTypedData_v2="eth_signTypedData_v2",n.eth_signTypedData_v3="eth_signTypedData_v3",n.eth_signTypedData_v4="eth_signTypedData_v4",n.eth_signTypedData="eth_signTypedData",n.cbWallet_arbitrary="walletlink_arbitrary",n.wallet_addEthereumChain="wallet_addEthereumChain",n.wallet_switchEthereumChain="wallet_switchEthereumChain",n.wallet_watchAsset="wallet_watchAsset",n.eth_subscribe="eth_subscribe",n.eth_unsubscribe="eth_unsubscribe",n.eth_newFilter="eth_newFilter",n.eth_newBlockFilter="eth_newBlockFilter",n.eth_newPendingTransactionFilter="eth_newPendingTransactionFilter",n.eth_getFilterChanges="eth_getFilterChanges",n.eth_getFilterLogs="eth_getFilterLogs"},5313(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SubscriptionManager=void 0;let r=n(5012),i=n(8961),$=()=>{};e.SubscriptionManager=class{constructor(t){let e=new r({provider:t,pollingInterval:15e3,setSkipCacheFlag:!0}),{events:n,middleware:$}=i({blockTracker:e,provider:t});this.events=n,this.subscriptionMiddleware=$}async handleRequest(t){let e={};return await this.subscriptionMiddleware(t,e,$,$),e}destroy(){this.subscriptionMiddleware.destroy()}}},3518(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WalletSDKUI=void 0;let r=n(8202),i=n(9934),$=n(7162);e.WalletSDKUI=class{constructor(t){this.standalone=null,this.attached=!1,this.snackbar=new i.Snackbar({darkMode:t.darkMode}),this.linkFlow=new r.LinkFlow({darkMode:t.darkMode,version:t.version,sessionId:t.session.id,sessionSecret:t.session.secret,linkAPIUrl:t.linkAPIUrl,connected$:t.connected$,isParentConnection:!1})}attach(){if(this.attached)throw Error("Coinbase Wallet SDK UI is already attached");let t=document.documentElement,e=document.createElement("div");e.className="-cbwsdk-css-reset",t.appendChild(e),this.linkFlow.attach(e),this.snackbar.attach(e),this.attached=!0,(0,$.injectCssReset)()}setConnectDisabled(t){this.linkFlow.setConnectDisabled(t)}addEthereumChain(t){}watchAsset(t){}switchEthereumChain(t){}requestEthereumAccounts(t){this.linkFlow.open({onCancel:t.onCancel})}hideRequestEthereumAccounts(){this.linkFlow.close()}signEthereumMessage(t){}signEthereumTransaction(t){}submitEthereumTransaction(t){}ethereumAddressFromSignedMessage(t){}showConnecting(t){let e;return e=t.isUnlinkedErrorState?{autoExpand:!0,message:"Connection lost",menuItems:[{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]}:{message:"Confirm on phone",menuItems:[{isRed:!0,info:"Cancel transaction",svgWidth:"11",svgHeight:"11",path:"M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z",defaultFillRule:"inherit",defaultClipRule:"inherit",onClick:t.onCancel},{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:t.onResetConnection}]},this.snackbar.presentItem(e)}reloadUI(){document.location.reload()}inlineAccountsResponse(){return!1}inlineAddEthereumChain(t){return!1}inlineWatchAsset(){return!1}inlineSwitchEthereumChain(){return!1}setStandalone(t){this.standalone=t}isStandalone(){var t;return null!==(t=this.standalone)&&void 0!==t&&t}}},4493(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WalletUIError=void 0;class n extends Error{constructor(t,e){super(t),this.message=t,this.errorCode=e}}e.WalletUIError=n,n.UserRejectedRequest=new n("User rejected request"),n.SwitchEthereumChainUnsupportedChainId=new n("Unsupported chainId",4902)},5813(t,e){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.RelayMessageType=void 0,(n=e.RelayMessageType||(e.RelayMessageType={})).SESSION_ID_REQUEST="SESSION_ID_REQUEST",n.SESSION_ID_RESPONSE="SESSION_ID_RESPONSE",n.LINKED="LINKED",n.UNLINKED="UNLINKED",n.WEB3_REQUEST="WEB3_REQUEST",n.WEB3_REQUEST_CANCELED="WEB3_REQUEST_CANCELED",n.WEB3_RESPONSE="WEB3_RESPONSE"},3526(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Session=void 0;let r=n(2023),i=n(4143),$=n(1717),o=n(4643),s="session:id",x="session:secret",a="session:linked";class u{constructor(t,e,n,i){this._storage=t,this._id=e||(0,o.randomBytesHex)(16),this._secret=n||(0,o.randomBytesHex)(32);let $=r.sha256.create();$.update(this._id+", "+this._secret+" WalletLink"),this._key=$.hex(),this._linked=!!i}static load(t){let e=t.getItem(s),n=t.getItem(a),r=t.getItem(x);return e&&r?new u(t,e,r,"1"===n):null}static get persistedSessionIdChange$(){return(0,i.fromEvent)(window,"storage").pipe((0,$.filter)(t=>t.key===s),(0,$.map)(t=>({oldValue:t.oldValue||null,newValue:t.newValue||null})))}static hash(t){return r.sha256.create().update(t).hex()}get id(){return this._id}get secret(){return this._secret}get key(){return this._key}get linked(){return this._linked}set linked(t){this._linked=t,this.persistLinked()}save(){return this._storage.setItem(s,this._id),this._storage.setItem(x,this._secret),this.persistLinked(),this}persistLinked(){this._storage.setItem(a,this._linked?"1":"0")}}e.Session=u},6570:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),$=this&&this.__decorate||function(t,e,n,r){var i,$=arguments.length,o=$<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(o=($<3?i(o):$>3?i(e,n,o):i(e,n))||o);return $>3&&o&&Object.defineProperty(e,n,o),o},o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.WalletSDKRelay=void 0;let x=s(n(7056)),a=n(9826),u=n(4143),c=n(1717),l=n(2191),h=n(8876),f=n(4493),d=n(1295),p=n(4643),y=o(n(235)),g=n(3526),b=n(5633),_=n(9739),m=n(5186),v=n(3770),w=n(7386),S=n(287);class k extends b.WalletSDKRelayAbstract{constructor(t){var e;super(),this.accountsCallback=null,this.chainCallback=null,this.appName="",this.appLogoUrl=null,this.subscriptions=new u.Subscription,this.linkAPIUrl=t.linkAPIUrl,this.storage=t.storage,this.options=t;let{session:n,ui:r,connection:i}=this.subscribe();if(this._session=n,this.connection=i,this.relayEventManager=t.relayEventManager,t.diagnosticLogger&&t.eventListener)throw Error("can not have both eventListener and diagnosticLogger options, use only diagnosticLogger");t.eventListener?this.diagnostic={log:t.eventListener.onEvent}:this.diagnostic=t.diagnosticLogger,this._reloadOnDisconnect=null===(e=t.reloadOnDisconnect)||void 0===e||e,this.ui=r}subscribe(){let t=g.Session.load(this.storage)||new g.Session(this.storage).save(),e=new h.WalletSDKConnection(t.id,t.key,this.linkAPIUrl,this.diagnostic);this.subscriptions.add(e.sessionConfig$.subscribe({next:t=>{this.onSessionConfigChanged(t)},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"error while invoking session config callback"})}})),this.subscriptions.add(e.incomingEvent$.pipe((0,c.filter)(t=>"Web3Response"===t.event)).subscribe({next:this.handleIncomingEvent})),this.subscriptions.add(e.linked$.pipe((0,c.skip)(1),(0,c.tap)(t=>{var e;this.isLinked=t;let n=this.storage.getItem(b.LOCAL_STORAGE_ADDRESSES_KEY);if(t&&(this.session.linked=t),this.isUnlinkedErrorState=!1,n){let r=n.split(" "),i="true"===this.storage.getItem("IsStandaloneSigning");if(""!==r[0]&&!t&&this.session.linked&&!i){this.isUnlinkedErrorState=!0;let $=this.getSessionIdHash();null===(e=this.diagnostic)||void 0===e||e.log(l.EVENTS.UNLINKED_ERROR_STATE,{sessionIdHash:$})}}})).subscribe()),this.subscriptions.add(e.sessionConfig$.pipe((0,c.filter)(t=>!!t.metadata&&"1"===t.metadata.__destroyed)).subscribe(()=>{var t;let n=e.isDestroyed;return null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.METADATA_DESTROYED,{alreadyDestroyed:n,sessionIdHash:this.getSessionIdHash()}),this.resetAndReload()})),this.subscriptions.add(e.sessionConfig$.pipe((0,c.filter)(t=>t.metadata&&void 0!==t.metadata.WalletUsername)).pipe((0,c.mergeMap)(e=>y.decrypt(e.metadata.WalletUsername,t.secret))).subscribe({next:t=>{this.storage.setItem(b.WALLET_USER_NAME_KEY,t)},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"username"})}})),this.subscriptions.add(e.sessionConfig$.pipe((0,c.filter)(t=>t.metadata&&void 0!==t.metadata.AppVersion)).pipe((0,c.mergeMap)(e=>y.decrypt(e.metadata.AppVersion,t.secret))).subscribe({next:t=>{this.storage.setItem(b.APP_VERSION_KEY,t)},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"appversion"})}})),this.subscriptions.add(e.sessionConfig$.pipe((0,c.filter)(t=>t.metadata&&void 0!==t.metadata.ChainId&&void 0!==t.metadata.JsonRpcUrl)).pipe((0,c.mergeMap)(e=>(0,u.zip)(y.decrypt(e.metadata.ChainId,t.secret),y.decrypt(e.metadata.JsonRpcUrl,t.secret)))).pipe((0,c.distinctUntilChanged)()).subscribe({next:([t,e])=>{this.chainCallback&&this.chainCallback(t,e)},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"chainId|jsonRpcUrl"})}})),this.subscriptions.add(e.sessionConfig$.pipe((0,c.filter)(t=>t.metadata&&void 0!==t.metadata.EthereumAddress)).pipe((0,c.mergeMap)(e=>y.decrypt(e.metadata.EthereumAddress,t.secret))).subscribe({next:t=>{this.accountsCallback&&this.accountsCallback([t]),k.accountRequestCallbackIds.size>0&&(Array.from(k.accountRequestCallbackIds.values()).forEach(e=>{let n=(0,S.Web3ResponseMessage)({id:e,response:(0,w.RequestEthereumAccountsResponse)([t])});this.invokeCallback(Object.assign(Object.assign({},n),{id:e}))}),k.accountRequestCallbackIds.clear())},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"selectedAddress"})}}));let n=this.options.uiConstructor({linkAPIUrl:this.options.linkAPIUrl,version:this.options.version,darkMode:this.options.darkMode,session:t,connected$:e.connected$});return e.connect(),{session:t,ui:n,connection:e}}attachUI(){this.ui.attach()}resetAndReload(){this.connection.setSessionMetadata("__destroyed","1").pipe((0,c.timeout)(1e3),(0,c.catchError)(t=>(0,u.of)(null))).subscribe(t=>{var e,n,r;let i=this.ui.isStandalone();try{this.subscriptions.unsubscribe()}catch($){null===(e=this.diagnostic)||void 0===e||e.log(l.EVENTS.GENERAL_ERROR,{message:"Had error unsubscribing"})}null===(n=this.diagnostic)||void 0===n||n.log(l.EVENTS.SESSION_STATE_CHANGE,{method:"relay::resetAndReload",sessionMetadataChange:"__destroyed, 1",sessionIdHash:this.getSessionIdHash()}),this.connection.destroy();let o=g.Session.load(this.storage);if((null==o?void 0:o.id)===this._session.id?this.storage.clear():o&&(null===(r=this.diagnostic)||void 0===r||r.log(l.EVENTS.SKIPPED_CLEARING_SESSION,{sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:g.Session.hash(o.id)})),this._reloadOnDisconnect)return void this.ui.reloadUI();this.accountsCallback&&this.accountsCallback([],!0);let{session:s,ui:x,connection:a}=this.subscribe();this._session=s,this.connection=a,this.ui=x,i&&this.ui.setStandalone&&this.ui.setStandalone(!0),this.attachUI()},t=>{var e;null===(e=this.diagnostic)||void 0===e||e.log(l.EVENTS.FAILURE,{method:"relay::resetAndReload",message:"failed to reset and reload with "+t,sessionIdHash:this.getSessionIdHash()})})}setAppInfo(t,e){this.appName=t,this.appLogoUrl=e}getStorageItem(t){return this.storage.getItem(t)}get session(){return this._session}setStorageItem(t,e){this.storage.setItem(t,e)}signEthereumMessage(t,e,n,r){return this.sendRequest({method:_.Web3Method.signEthereumMessage,params:{message:(0,p.hexStringFromBuffer)(t,!0),address:e,addPrefix:n,typedDataJson:r||null}})}ethereumAddressFromSignedMessage(t,e,n){return this.sendRequest({method:_.Web3Method.ethereumAddressFromSignedMessage,params:{message:(0,p.hexStringFromBuffer)(t,!0),signature:(0,p.hexStringFromBuffer)(e,!0),addPrefix:n}})}signEthereumTransaction(t){return this.sendRequest({method:_.Web3Method.signEthereumTransaction,params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:(0,p.bigIntStringFromBN)(t.weiValue),data:(0,p.hexStringFromBuffer)(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?(0,p.bigIntStringFromBN)(t.gasPriceInWei):null,maxFeePerGas:t.gasPriceInWei?(0,p.bigIntStringFromBN)(t.gasPriceInWei):null,maxPriorityFeePerGas:t.gasPriceInWei?(0,p.bigIntStringFromBN)(t.gasPriceInWei):null,gasLimit:t.gasLimit?(0,p.bigIntStringFromBN)(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!1}})}signAndSubmitEthereumTransaction(t){return this.sendRequest({method:_.Web3Method.signEthereumTransaction,params:{fromAddress:t.fromAddress,toAddress:t.toAddress,weiValue:(0,p.bigIntStringFromBN)(t.weiValue),data:(0,p.hexStringFromBuffer)(t.data,!0),nonce:t.nonce,gasPriceInWei:t.gasPriceInWei?(0,p.bigIntStringFromBN)(t.gasPriceInWei):null,maxFeePerGas:t.maxFeePerGas?(0,p.bigIntStringFromBN)(t.maxFeePerGas):null,maxPriorityFeePerGas:t.maxPriorityFeePerGas?(0,p.bigIntStringFromBN)(t.maxPriorityFeePerGas):null,gasLimit:t.gasLimit?(0,p.bigIntStringFromBN)(t.gasLimit):null,chainId:t.chainId,shouldSubmit:!0}})}submitEthereumTransaction(t,e){return this.sendRequest({method:_.Web3Method.submitEthereumTransaction,params:{signedTransaction:(0,p.hexStringFromBuffer)(t,!0),chainId:e}})}scanQRCode(t){return this.sendRequest({method:_.Web3Method.scanQRCode,params:{regExp:t}})}getQRCodeUrl(){return(0,p.createQrUrl)(this._session.id,this._session.secret,this.linkAPIUrl,!1)}genericRequest(t,e){return this.sendRequest({method:_.Web3Method.generic,params:{action:e,data:t}})}sendGenericMessage(t){return this.sendRequest(t)}sendRequest(t){let e=null,n=(0,p.randomBytesHex)(8),r=r=>{this.publishWeb3RequestCanceledEvent(n),this.handleErrorResponse(n,t.method,r),null==e||e()};return{promise:new Promise((i,$)=>{this.ui.isStandalone()||(e=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:r,onResetConnection:this.resetAndReload})),this.relayEventManager.callbacks.set(n,t=>{if(null==e||e(),t.errorMessage)return $(Error(t.errorMessage));i(t)}),this.ui.isStandalone()?this.sendRequestStandalone(n,t):this.publishWeb3RequestEvent(n,t)}),cancel:r}}setConnectDisabled(t){this.ui.setConnectDisabled(t)}setAccountsCallback(t){this.accountsCallback=t}setChainCallback(t){this.chainCallback=t}publishWeb3RequestEvent(t,e){var n;let r=(0,v.Web3RequestMessage)({id:t,request:e}),i=g.Session.load(this.storage);null===(n=this.diagnostic)||void 0===n||n.log(l.EVENTS.WEB3_REQUEST,{eventId:r.id,method:"relay::"+r.request.method,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:i?g.Session.hash(i.id):"",isSessionMismatched:((null==i?void 0:i.id)!==this._session.id).toString()}),this.subscriptions.add(this.publishEvent("Web3Request",r,!0).subscribe({next:t=>{var e;null===(e=this.diagnostic)||void 0===e||e.log(l.EVENTS.WEB3_REQUEST_PUBLISHED,{eventId:r.id,method:"relay::"+r.request.method,sessionIdHash:this.getSessionIdHash(),storedSessionIdHash:i?g.Session.hash(i.id):"",isSessionMismatched:((null==i?void 0:i.id)!==this._session.id).toString()})},error:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:r.id,response:{method:r.request.method,errorMessage:t.message}}))}}))}publishWeb3RequestCanceledEvent(t){let e=(0,m.Web3RequestCanceledMessage)(t);this.subscriptions.add(this.publishEvent("Web3RequestCanceled",e,!1).subscribe())}publishEvent(t,e,n){let r=this.session.secret;return new u.Observable(t=>{y.encrypt(JSON.stringify(Object.assign(Object.assign({},e),{origin:location.origin})),r).then(e=>{t.next(e),t.complete()})}).pipe((0,c.mergeMap)(e=>this.connection.publishEvent(t,e,n)))}handleIncomingEvent(t){try{this.subscriptions.add(y.decrypt(t.data,this.session.secret).pipe((0,c.map)(t=>JSON.parse(t))).subscribe({next:t=>{let e=(0,S.isWeb3ResponseMessage)(t)?t:null;e&&this.handleWeb3ResponseMessage(e)},error:()=>{var t;null===(t=this.diagnostic)||void 0===t||t.log(l.EVENTS.GENERAL_ERROR,{message:"Had error decrypting",value:"incomingEvent"})}}))}catch(e){return}}handleWeb3ResponseMessage(t){var e;let{response:n}=t;if(null===(e=this.diagnostic)||void 0===e||e.log(l.EVENTS.WEB3_RESPONSE,{eventId:t.id,method:"relay::"+n.method,sessionIdHash:this.getSessionIdHash()}),(0,w.isRequestEthereumAccountsResponse)(n))return k.accountRequestCallbackIds.forEach(e=>this.invokeCallback(Object.assign(Object.assign({},t),{id:e}))),void k.accountRequestCallbackIds.clear();this.invokeCallback(t)}handleErrorResponse(t,e,n,r){this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:t,response:(0,w.ErrorResponse)(e,(null!=n?n:f.WalletUIError.UserRejectedRequest).message,r)}))}invokeCallback(t){let e=this.relayEventManager.callbacks.get(t.id);e&&(e(t.response),this.relayEventManager.callbacks.delete(t.id))}requestEthereumAccounts(){let t={method:_.Web3Method.requestEthereumAccounts,params:{appName:this.appName,appLogoUrl:this.appLogoUrl||null}},e=(0,p.randomBytesHex)(8),n=n=>{this.publishWeb3RequestCanceledEvent(e),this.handleErrorResponse(e,t.method,n)};return{promise:new Promise((r,i)=>{var $;this.relayEventManager.callbacks.set(e,t=>{if(this.ui.hideRequestEthereumAccounts(),t.errorMessage)return i(Error(t.errorMessage));r(t)});let o=(null===($=null==window?void 0:window.navigator)||void 0===$?void 0:$.userAgent)||null;if(o&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(o))window.location.href="https://go.cb-w.com/xoXnYwQimhb?cb_url="+encodeURIComponent(window.location.href);else{if(this.ui.inlineAccountsResponse()){let s=t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:e,response:(0,w.RequestEthereumAccountsResponse)(t)}))};this.ui.requestEthereumAccounts({onCancel:n,onAccounts:s})}else this.ui.requestEthereumAccounts({onCancel:n});k.accountRequestCallbackIds.add(e),this.ui.inlineAccountsResponse()||this.ui.isStandalone()||this.publishWeb3RequestEvent(e,t)}}),cancel:n}}selectProvider(t){let e={method:_.Web3Method.selectProvider,params:{providerOptions:t}},n=(0,p.randomBytesHex)(8);return{cancel:t=>{this.publishWeb3RequestCanceledEvent(n),this.handleErrorResponse(n,e.method,t)},promise:new Promise((e,r)=>{this.relayEventManager.callbacks.set(n,t=>{if(t.errorMessage)return r(Error(t.errorMessage));e(t)}),this.ui.selectProvider&&this.ui.selectProvider({onApprove:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:n,response:(0,w.SelectProviderResponse)(t)}))},onCancel:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:n,response:(0,w.SelectProviderResponse)(d.ProviderType.Unselected)}))},providerOptions:t})})}}watchAsset(t,e,n,r,i,$){let o={method:_.Web3Method.watchAsset,params:{type:t,options:{address:e,symbol:n,decimals:r,image:i},chainId:$}},s=null,x=(0,p.randomBytesHex)(8),a=t=>{this.publishWeb3RequestCanceledEvent(x),this.handleErrorResponse(x,o.method,t),null==s||s()};return this.ui.inlineWatchAsset()||(s=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:a,onResetConnection:this.resetAndReload})),{cancel:a,promise:new Promise((a,u)=>{this.relayEventManager.callbacks.set(x,t=>{if(null==s||s(),t.errorMessage)return u(Error(t.errorMessage));a(t)}),this.ui.inlineWatchAsset()&&this.ui.watchAsset({onApprove:()=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:x,response:(0,w.WatchAssetReponse)(!0)}))},onCancel:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:x,response:(0,w.WatchAssetReponse)(!1)}))},type:t,address:e,symbol:n,decimals:r,image:i,chainId:$}),this.ui.inlineWatchAsset()||this.ui.isStandalone()||this.publishWeb3RequestEvent(x,o)})}}addEthereumChain(t,e,n,r,i,$){let o={method:_.Web3Method.addEthereumChain,params:{chainId:t,rpcUrls:e,blockExplorerUrls:r,chainName:i,iconUrls:n,nativeCurrency:$}},s=null,x=(0,p.randomBytesHex)(8),a=t=>{this.publishWeb3RequestCanceledEvent(x),this.handleErrorResponse(x,o.method,t),null==s||s()};return this.ui.inlineAddEthereumChain(t)||(s=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:a,onResetConnection:this.resetAndReload})),{promise:new Promise((e,n)=>{this.relayEventManager.callbacks.set(x,t=>{if(null==s||s(),t.errorMessage)return n(Error(t.errorMessage));e(t)}),this.ui.inlineAddEthereumChain(t)&&this.ui.addEthereumChain({onCancel:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:x,response:(0,w.AddEthereumChainResponse)({isApproved:!1,rpcUrl:""})}))},onApprove:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:x,response:(0,w.AddEthereumChainResponse)({isApproved:!0,rpcUrl:t})}))},chainId:o.params.chainId,rpcUrls:o.params.rpcUrls,blockExplorerUrls:o.params.blockExplorerUrls,chainName:o.params.chainName,iconUrls:o.params.iconUrls,nativeCurrency:o.params.nativeCurrency}),this.ui.inlineAddEthereumChain(t)||this.ui.isStandalone()||this.publishWeb3RequestEvent(x,o)}),cancel:a}}switchEthereumChain(t){let e={method:_.Web3Method.switchEthereumChain,params:{chainId:t}},n=null,r=(0,p.randomBytesHex)(8),i=t=>{this.publishWeb3RequestCanceledEvent(r),this.handleErrorResponse(r,e.method,t),null==n||n()};return this.ui.inlineSwitchEthereumChain()||(n=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:i,onResetConnection:this.resetAndReload})),{promise:new Promise((t,i)=>{this.relayEventManager.callbacks.set(r,e=>(null==n||n(),e.errorMessage&&e.errorCode?i(a.ethErrors.provider.custom({code:e.errorCode,message:"Unrecognized chain ID. Try adding the chain using addEthereumChain first."})):e.errorMessage?i(Error(e.errorMessage)):void t(e))),this.ui.switchEthereumChain({onCancel:t=>{if("number"==typeof t){let e=t;this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:r,response:(0,w.ErrorResponse)(_.Web3Method.switchEthereumChain,f.WalletUIError.SwitchEthereumChainUnsupportedChainId.message,e)}))}else t instanceof f.WalletUIError?this.handleErrorResponse(r,_.Web3Method.switchEthereumChain,t,t.errorCode):this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:r,response:(0,w.SwitchEthereumChainResponse)({isApproved:!1,rpcUrl:""})}))},onApprove:t=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:r,response:(0,w.SwitchEthereumChainResponse)({isApproved:!0,rpcUrl:t})}))},chainId:e.params.chainId}),this.ui.inlineSwitchEthereumChain()||this.ui.isStandalone()||this.publishWeb3RequestEvent(r,e)}),cancel:i}}inlineAddEthereumChain(t){return this.ui.inlineAddEthereumChain(t)}getSessionIdHash(){return g.Session.hash(this._session.id)}sendRequestStandalone(t,e){let n=n=>{this.handleErrorResponse(t,e.method,n)},r=e=>{this.handleWeb3ResponseMessage((0,S.Web3ResponseMessage)({id:t,response:e}))};switch(e.method){case _.Web3Method.signEthereumMessage:this.ui.signEthereumMessage({request:e,onSuccess:r,onCancel:n});break;case _.Web3Method.signEthereumTransaction:this.ui.signEthereumTransaction({request:e,onSuccess:r,onCancel:n});break;case _.Web3Method.submitEthereumTransaction:this.ui.submitEthereumTransaction({request:e,onSuccess:r,onCancel:n});break;case _.Web3Method.ethereumAddressFromSignedMessage:this.ui.ethereumAddressFromSignedMessage({request:e,onSuccess:r});break;default:n()}}onSessionConfigChanged(t){}}k.accountRequestCallbackIds=new Set,$([x.default],k.prototype,"resetAndReload",null),$([x.default],k.prototype,"handleIncomingEvent",null),e.WalletSDKRelay=k},5633(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WalletSDKRelayAbstract=e.APP_VERSION_KEY=e.LOCAL_STORAGE_ADDRESSES_KEY=e.WALLET_USER_NAME_KEY=void 0;let r=n(9826);e.WALLET_USER_NAME_KEY="walletUsername",e.LOCAL_STORAGE_ADDRESSES_KEY="Addresses",e.APP_VERSION_KEY="AppVersion",e.WalletSDKRelayAbstract=class{async makeEthereumJSONRPCRequest(t,e){if(!e)throw Error("Error: No jsonRpcUrl provided");return window.fetch(e,{method:"POST",body:JSON.stringify(t),mode:"cors",headers:{"Content-Type":"application/json"}}).then(t=>t.json()).then(t=>{if(!t)throw r.ethErrors.rpc.parse({});let e=t,{error:n}=e;if(n)throw(0,r.serializeError)(n);return e})}}},7472(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WalletSDKRelayEventManager=void 0;let r=n(4643);e.WalletSDKRelayEventManager=class{constructor(){this._nextRequestId=0,this.callbacks=new Map}makeRequestId(){this._nextRequestId=(this._nextRequestId+1)%**********;let t=this._nextRequestId,e=(0,r.prepend0x)(t.toString(16));return this.callbacks.get(e)&&this.callbacks.delete(e),t}}},9739(t,e){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.Web3Method=void 0,(n=e.Web3Method||(e.Web3Method={})).requestEthereumAccounts="requestEthereumAccounts",n.signEthereumMessage="signEthereumMessage",n.signEthereumTransaction="signEthereumTransaction",n.submitEthereumTransaction="submitEthereumTransaction",n.ethereumAddressFromSignedMessage="ethereumAddressFromSignedMessage",n.scanQRCode="scanQRCode",n.generic="generic",n.childRequestEthereumAccounts="childRequestEthereumAccounts",n.addEthereumChain="addEthereumChain",n.switchEthereumChain="switchEthereumChain",n.makeEthereumJSONRPCRequest="makeEthereumJSONRPCRequest",n.watchAsset="watchAsset",n.selectProvider="selectProvider"},5186(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Web3RequestCanceledMessage=void 0;let r=n(5813);e.Web3RequestCanceledMessage=function(t){return{type:r.RelayMessageType.WEB3_REQUEST_CANCELED,id:t}}},3770(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Web3RequestMessage=void 0;let r=n(5813);e.Web3RequestMessage=function(t){return Object.assign({type:r.RelayMessageType.WEB3_REQUEST},t)}},7386(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EthereumAddressFromSignedMessageResponse=e.SubmitEthereumTransactionResponse=e.SignEthereumTransactionResponse=e.SignEthereumMessageResponse=e.isRequestEthereumAccountsResponse=e.SelectProviderResponse=e.WatchAssetReponse=e.RequestEthereumAccountsResponse=e.SwitchEthereumChainResponse=e.AddEthereumChainResponse=e.ErrorResponse=void 0;let r=n(9739);e.ErrorResponse=function(t,e,n){return{method:t,errorMessage:e,errorCode:n}},e.AddEthereumChainResponse=function(t){return{method:r.Web3Method.addEthereumChain,result:t}},e.SwitchEthereumChainResponse=function(t){return{method:r.Web3Method.switchEthereumChain,result:t}},e.RequestEthereumAccountsResponse=function(t){return{method:r.Web3Method.requestEthereumAccounts,result:t}},e.WatchAssetReponse=function(t){return{method:r.Web3Method.watchAsset,result:t}},e.SelectProviderResponse=function(t){return{method:r.Web3Method.selectProvider,result:t}},e.isRequestEthereumAccountsResponse=function(t){return t&&t.method===r.Web3Method.requestEthereumAccounts},e.SignEthereumMessageResponse=function(t){return{method:r.Web3Method.signEthereumMessage,result:t}},e.SignEthereumTransactionResponse=function(t){return{method:r.Web3Method.signEthereumTransaction,result:t}},e.SubmitEthereumTransactionResponse=function(t){return{method:r.Web3Method.submitEthereumTransaction,result:t}},e.EthereumAddressFromSignedMessageResponse=function(t){return{method:r.Web3Method.ethereumAddressFromSignedMessage,result:t}}},287(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isWeb3ResponseMessage=e.Web3ResponseMessage=void 0;let r=n(5813);e.Web3ResponseMessage=function(t){return Object.assign({type:r.RelayMessageType.WEB3_RESPONSE},t)},e.isWeb3ResponseMessage=function(t){return t&&t.type===r.RelayMessageType.WEB3_RESPONSE}},235(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.decrypt=e.encrypt=void 0;let r=n(4143),i=n(4643);e.encrypt=async function(t,e){if(64!==e.length)throw Error("secret must be 256 bits");let n=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",(0,i.hexStringToUint8Array)(e),{name:"aes-gcm"},!1,["encrypt","decrypt"]),$=new TextEncoder,o=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:n},r,$.encode(t)),s=o.slice(o.byteLength-16),x=o.slice(0,o.byteLength-16),a=new Uint8Array(s),u=new Uint8Array(x),c=new Uint8Array([...n,...a,...u]);return(0,i.uint8ArrayToHex)(c)},e.decrypt=function(t,e){if(64!==e.length)throw Error("secret must be 256 bits");return new r.Observable(n=>{!async function(){let r=await crypto.subtle.importKey("raw",(0,i.hexStringToUint8Array)(e),{name:"aes-gcm"},!1,["encrypt","decrypt"]),$=(0,i.hexStringToUint8Array)(t),o=$.slice(0,12),s=$.slice(12,28),x=$.slice(28),a=new Uint8Array([...x,...s]),u={name:"AES-GCM",iv:new Uint8Array(o)};try{let c=await window.crypto.subtle.decrypt(u,r,a),l=new TextDecoder;n.next(l.decode(c)),n.complete()}catch(h){n.error(h)}}()})}},1295(t,e){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.ProviderType=e.RegExpString=e.IntNumber=e.BigIntString=e.AddressString=e.HexString=e.OpaqueType=void 0,e.OpaqueType=function(){return t=>t},e.HexString=t=>t,e.AddressString=t=>t,e.BigIntString=t=>t,e.IntNumber=function(t){return Math.floor(t)},e.RegExpString=t=>t,(n=e.ProviderType||(e.ProviderType={})).CoinbaseWallet="CoinbaseWallet",n.MetaMask="MetaMask",n.Unselected=""},4643:function(t,e,n){"use strict";var r=n(8764).Buffer,i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.createQrUrl=e.getFavicon=e.range=e.isBigNumber=e.ensureParsedJSONObject=e.ensureBN=e.ensureRegExpString=e.ensureIntNumber=e.ensureBuffer=e.ensureAddressString=e.ensureEvenLengthHexString=e.ensureHexString=e.isHexString=e.prepend0x=e.strip0x=e.has0xPrefix=e.hexStringFromIntNumber=e.intNumberFromHexString=e.bigIntStringFromBN=e.hexStringFromBuffer=e.hexStringToUint8Array=e.uint8ArrayToHex=e.randomBytesHex=void 0;let $=i(n(3550)),o=n(129),s=n(1295),x=/^[0-9]*$/,a=/^[a-f0-9]*$/;function u(t){return[...t].map(t=>t.toString(16).padStart(2,"0")).join("")}function c(t){return t.startsWith("0x")||t.startsWith("0X")}function l(t){return c(t)?t.slice(2):t}function h(t){return c(t)?"0x"+t.slice(2):"0x"+t}function f(t){if("string"!=typeof t)return!1;let e=l(t).toLowerCase();return a.test(e)}function d(t,e=!1){if("string"==typeof t){let n=l(t).toLowerCase();if(a.test(n))return(0,s.HexString)(e?"0x"+n:n)}throw Error('"'+String(t)+'" is not a hexadecimal string')}function p(t,e=!1){let n=d(t,!1);return n.length%2==1&&(n=(0,s.HexString)("0"+n)),e?(0,s.HexString)("0x"+n):n}function y(t){if("number"==typeof t&&Number.isInteger(t))return(0,s.IntNumber)(t);if("string"==typeof t){if(x.test(t))return(0,s.IntNumber)(Number(t));if(f(t))return(0,s.IntNumber)(new $.default(p(t,!1),16).toNumber())}throw Error("Not an integer: "+String(t))}function g(t){if(null==t||"function"!=typeof t.constructor)return!1;let{constructor:e}=t;return"function"==typeof e.config&&"number"==typeof e.EUCLID}e.randomBytesHex=function(t){return u(crypto.getRandomValues(new Uint8Array(t)))},e.uint8ArrayToHex=u,e.hexStringToUint8Array=function(t){return new Uint8Array(t.match(/.{1,2}/g).map(t=>parseInt(t,16)))},e.hexStringFromBuffer=function(t,e=!1){let n=t.toString("hex");return(0,s.HexString)(e?"0x"+n:n)},e.bigIntStringFromBN=function(t){return(0,s.BigIntString)(t.toString(10))},e.intNumberFromHexString=function(t){return(0,s.IntNumber)(new $.default(p(t,!1),16).toNumber())},e.hexStringFromIntNumber=function(t){return(0,s.HexString)("0x"+new $.default(t).toString(16))},e.has0xPrefix=c,e.strip0x=l,e.prepend0x=h,e.isHexString=f,e.ensureHexString=d,e.ensureEvenLengthHexString=p,e.ensureAddressString=function(t){if("string"==typeof t){let e=l(t).toLowerCase();if(f(e)&&40===e.length)return(0,s.AddressString)(h(e))}throw Error("Invalid Ethereum address: "+String(t))},e.ensureBuffer=function(t){if(r.isBuffer(t))return t;if("string"==typeof t){if(f(t)){let e=p(t,!1);return r.from(e,"hex")}return r.from(t,"utf8")}throw Error("Not binary data: "+String(t))},e.ensureIntNumber=y,e.ensureRegExpString=function(t){if(t instanceof RegExp)return(0,s.RegExpString)(t.toString());throw Error("Not a RegExp: "+String(t))},e.ensureBN=function(t){if(null!==t&&($.default.isBN(t)||g(t)))return new $.default(t.toString(10),10);if("number"==typeof t)return new $.default(y(t));if("string"==typeof t){if(x.test(t))return new $.default(t,10);if(f(t))return new $.default(p(t,!1),16)}throw Error("Not an integer: "+String(t))},e.ensureParsedJSONObject=function(t){if("string"==typeof t)return JSON.parse(t);if("object"==typeof t)return t;throw Error("Not a JSON string or an object: "+String(t))},e.isBigNumber=g,e.range=function(t,e){return Array.from({length:e-t},(e,n)=>t+n)},e.getFavicon=function(){let t=document.querySelector('link[sizes="192x192"]')||document.querySelector('link[sizes="180x180"]')||document.querySelector('link[rel="icon"]')||document.querySelector('link[rel="shortcut icon"]'),{protocol:e,host:n}=document.location,r=t?t.getAttribute("href"):null;return!r||r.startsWith("javascript:")?null:r.startsWith("http://")||r.startsWith("https://")||r.startsWith("data:")?r:r.startsWith("//")?e+r:e+"//"+n+r},e.createQrUrl=function(t,e,n,r){let i=r?"parent-id":"id";return n+"/#/link?"+(0,o.stringify)({[i]:t,secret:e,server:n,v:"1"})}},6089(t,e,n){var r=n(8764).Buffer;let i=n(2518),$=n(3550);function o(t){return t.startsWith("int[")?"int256"+t.slice(3):"int"===t?"int256":t.startsWith("uint[")?"uint256"+t.slice(4):"uint"===t?"uint256":t.startsWith("fixed[")?"fixed128x128"+t.slice(5):"fixed"===t?"fixed128x128":t.startsWith("ufixed[")?"ufixed128x128"+t.slice(6):"ufixed"===t?"ufixed128x128":t}function s(t){return parseInt(/^\D+(\d+)$/.exec(t)[1],10)}function x(t){var e=/^\D+(\d+)x(\d+)$/.exec(t);return[parseInt(e[1],10),parseInt(e[2],10)]}function a(t){var e=t.match(/(.*)\[(.*?)\]$/);return e?""===e[2]?"dynamic":parseInt(e[2],10):null}function u(t){var e=typeof t;if("string"===e)return i.isHexString(t)?new $(i.stripHexPrefix(t),16):new $(t,10);if("number"===e)return new $(t);if(t.toArray)return t;throw Error("Argument is not a number")}function c(t,e){if("address"===t)return c("uint160",u(e));if("bool"===t)return c("uint8",e?1:0);if("string"===t)return c("bytes",new r(e,"utf8"));if((f=t).lastIndexOf("]")===f.length-1){if(void 0===e.length)throw Error("Not an array?");if("dynamic"!==(n=a(t))&&0!==n&&e.length>n)throw Error("Elements exceed array size: "+n);for(h in l=[],t=t.slice(0,t.lastIndexOf("[")),"string"==typeof e&&(e=JSON.parse(e)),e)l.push(c(t,e[h]));if("dynamic"===n){var n,o,l,h,f,d=c("uint256",e.length);l.unshift(d)}return r.concat(l)}if("bytes"===t)return e=new r(e),l=r.concat([c("uint256",e.length),e]),e.length%32!=0&&(l=r.concat([l,i.zeros(32-e.length%32)])),l;if(t.startsWith("bytes")){if((n=s(t))<1||n>32)throw Error("Invalid bytes<N> width: "+n);return i.setLengthRight(e,32)}if(t.startsWith("uint")){if((n=s(t))%8||n<8||n>256)throw Error("Invalid uint<N> width: "+n);if((o=u(e)).bitLength()>n)throw Error("Supplied uint exceeds width: "+n+" vs "+o.bitLength());if(o<0)throw Error("Supplied uint is negative");return o.toArrayLike(r,"be",32)}if(t.startsWith("int")){if((n=s(t))%8||n<8||n>256)throw Error("Invalid int<N> width: "+n);if((o=u(e)).bitLength()>n)throw Error("Supplied int exceeds width: "+n+" vs "+o.bitLength());return o.toTwos(256).toArrayLike(r,"be",32)}if(t.startsWith("ufixed")){if(n=x(t),(o=u(e))<0)throw Error("Supplied ufixed is negative");return c("uint256",o.mul(new $(2).pow(new $(n[1]))))}if(t.startsWith("fixed"))return n=x(t),c("int256",u(e).mul(new $(2).pow(new $(n[1]))));throw Error("Unsupported or invalid type: "+t)}function l(t){return"string"===t||"bytes"===t||"dynamic"===a(t)}function h(t,e){if(t.length!==e.length)throw Error("Number of types are not matching the values");for(var n,$,x=[],a=0;a<t.length;a++){var c=o(t[a]),l=e[a];if("bytes"===c)x.push(l);else if("string"===c)x.push(new r(l,"utf8"));else if("bool"===c)x.push(new r(l?"01":"00","hex"));else if("address"===c)x.push(i.setLength(l,20));else if(c.startsWith("bytes")){if((n=s(c))<1||n>32)throw Error("Invalid bytes<N> width: "+n);x.push(i.setLengthRight(l,n))}else if(c.startsWith("uint")){if((n=s(c))%8||n<8||n>256)throw Error("Invalid uint<N> width: "+n);if(($=u(l)).bitLength()>n)throw Error("Supplied uint exceeds width: "+n+" vs "+$.bitLength());x.push($.toArrayLike(r,"be",n/8))}else{if(!c.startsWith("int"))throw Error("Unsupported or invalid type: "+c);if((n=s(c))%8||n<8||n>256)throw Error("Invalid int<N> width: "+n);if(($=u(l)).bitLength()>n)throw Error("Supplied int exceeds width: "+n+" vs "+$.bitLength());x.push($.toTwos(n).toArrayLike(r,"be",n/8))}}return r.concat(x)}t.exports={rawEncode:function(t,e){var n=[],i=[],$=32*t.length;for(var s in t){var x=o(t[s]),a=c(x,e[s]);l(x)?(n.push(c("uint256",$)),i.push(a),$+=a.length):n.push(a)}return r.concat(n.concat(i))},solidityPack:h,soliditySHA3:function(t,e){return i.keccak(h(t,e))}}},4497(t,e,n){var r=n(8764).Buffer;let i=n(2518),$=n(6089),o={type:"object",properties:{types:{type:"object",additionalProperties:{type:"array",items:{type:"object",properties:{name:{type:"string"},type:{type:"string"}},required:["name","type"]}}},primaryType:{type:"string"},domain:{type:"object"},message:{type:"object"}},required:["types","primaryType","domain","message"]},s={encodeData(t,e,n,o=!0){let s=["bytes32"],x=[this.hashType(t,n)];if(o){let a=(t,e,s)=>{if(void 0!==n[e])return["bytes32",null==s?"0x0000000000000000000000000000000000000000000000000000000000000000":i.keccak(this.encodeData(e,s,n,o))];if(void 0===s)throw Error("missing value for field "+t+" of type "+e);if("bytes"===e)return["bytes32",i.keccak(s)];if("string"===e)return"string"==typeof s&&(s=r.from(s,"utf8")),["bytes32",i.keccak(s)];if(e.lastIndexOf("]")===e.length-1){let x=e.slice(0,e.lastIndexOf("[")),u=s.map(e=>a(t,x,e));return["bytes32",i.keccak($.rawEncode(u.map(([t])=>t),u.map(([,t])=>t)))]}return[e,s]};for(let u of n[t]){let[c,l]=a(u.name,u.type,e[u.name]);s.push(c),x.push(l)}}else for(let h of n[t]){let f=e[h.name];if(void 0!==f){if("bytes"===h.type)s.push("bytes32"),f=i.keccak(f),x.push(f);else if("string"===h.type)s.push("bytes32"),"string"==typeof f&&(f=r.from(f,"utf8")),f=i.keccak(f),x.push(f);else if(void 0!==n[h.type])s.push("bytes32"),f=i.keccak(this.encodeData(h.type,f,n,o)),x.push(f);else{if(h.type.lastIndexOf("]")===h.type.length-1)throw Error("Arrays currently unimplemented in encodeData");s.push(h.type),x.push(f)}}}return $.rawEncode(s,x)},encodeType(t,e){let n="",r=this.findTypeDependencies(t,e).filter(e=>e!==t);for(let i of r=[t].concat(r.sort())){if(!e[i])throw Error("No type definition specified: "+i);n+=i+"("+e[i].map(({name:t,type:e})=>e+" "+t).join(",")+")"}return n},findTypeDependencies(t,e,n=[]){if(t=t.match(/^\w*/)[0],n.includes(t)||void 0===e[t])return n;for(let r of(n.push(t),e[t]))for(let i of this.findTypeDependencies(r.type,e,n))n.includes(i)||n.push(i);return n},hashStruct(t,e,n,r=!0){return i.keccak(this.encodeData(t,e,n,r))},hashType(t,e){return i.keccak(this.encodeType(t,e))},sanitizeData(t){let e={};for(let n in o.properties)t[n]&&(e[n]=t[n]);return e.types&&(e.types=Object.assign({EIP712Domain:[]},e.types)),e},hash(t,e=!0){let n=this.sanitizeData(t),$=[r.from("1901","hex")];return $.push(this.hashStruct("EIP712Domain",n.domain,n.types,e)),"EIP712Domain"!==n.primaryType&&$.push(this.hashStruct(n.primaryType,n.message,n.types,e)),i.keccak(r.concat($))}};t.exports={TYPED_MESSAGE_SCHEMA:o,TypedDataUtils:s,hashForSignTypedDataLegacy:function(t){return function(t){let e=Error("Expect argument to be non-empty array");if("object"!=typeof t||!t.length)throw e;let n=t.map(function(t){return"bytes"===t.type?i.toBuffer(t.value):t.value}),r=t.map(function(t){return t.type}),o=t.map(function(t){if(!t.name)throw e;return t.type+" "+t.name});return $.soliditySHA3(["bytes32","bytes32"],[$.soliditySHA3(Array(t.length).fill("string"),o),$.soliditySHA3(r,n)])}(t.data)},hashForSignTypedData_v3:function(t){return s.hash(t.data,!1)},hashForSignTypedData_v4:function(t){return s.hash(t.data)}}},2518(t,e,n){var r=n(8764).Buffer;let i=n(8598),$=n(3550);function o(t){return r.allocUnsafe(t).fill(0)}function s(t,e,n){let r=o(e);return t=x(t),n?t.length<e?(t.copy(r),r):t.slice(0,e):t.length<e?(t.copy(r,e-t.length),r):t.slice(-e)}function x(t){var e;if(!r.isBuffer(t)){if(Array.isArray(t))t=r.from(t);else if("string"==typeof t)t=a(t)?r.from((e=u(t)).length%2?"0"+e:e,"hex"):r.from(t);else if("number"==typeof t)t=intToBuffer(t);else if(null==t)t=r.allocUnsafe(0);else if($.isBN(t))t=t.toArrayLike(r);else{if(!t.toArray)throw Error("invalid type");t=r.from(t.toArray())}}return t}function a(t){return"string"==typeof t&&t.match(/^0x[0-9A-Fa-f]*$/)}function u(t){return"string"==typeof t&&t.startsWith("0x")?t.slice(2):t}t.exports={zeros:o,setLength:s,setLengthRight:function(t,e){return s(t,e,!0)},isHexString:a,stripHexPrefix:u,toBuffer:x,bufferToHex:function(t){return"0x"+(t=x(t)).toString("hex")},keccak:function(t,e){return t=x(t),e||(e=256),i("keccak"+e).update(t).digest()}}},7713(t){function e(t){this.mode=r.MODE_8BIT_BYTE,this.data=t,this.parsedData=[];for(var e=0,n=this.data.length;e<n;e++){var i=[],$=this.data.charCodeAt(e);$>65536?(i[0]=240|(1835008&$)>>>18,i[1]=128|(258048&$)>>>12,i[2]=128|(4032&$)>>>6,i[3]=128|63&$):$>2048?(i[0]=224|(61440&$)>>>12,i[1]=128|(4032&$)>>>6,i[2]=128|63&$):$>128?(i[0]=192|(1984&$)>>>6,i[1]=128|63&$):i[0]=$,this.parsedData.push(i)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function n(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}e.prototype={getLength:function(t){return this.parsedData.length},write:function(t){for(var e=0,n=this.parsedData.length;e<n;e++)t.put(this.parsedData[e],8)}},n.prototype={addData:function(t){var n=new e(t);this.dataList.push(n),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[r][i]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=n.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)},setupPositionProbePattern:function(t,e){for(var n=-1;n<=7;n++)if(!(t+n<=-1||this.moduleCount<=t+n))for(var r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+n][e+r]=0<=n&&n<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=r&&r<=4)},getBestMaskPattern:function(){for(var t=0,e=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=i.getLostPoint(this);(0==n||t>r)&&(t=r,e=n)}return e},createMovieClip:function(t,e,n){var r=t.createEmptyMovieClip(e,n);this.make();for(var i=0;i<this.modules.length;i++)for(var $=1*i,o=0;o<this.modules[i].length;o++){var s=1*o;this.modules[i][o]&&(r.beginFill(0,100),r.moveTo(s,$),r.lineTo(s+1,$),r.lineTo(s+1,$+1),r.lineTo(s,$+1),r.endFill())}return r},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=i.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var n=0;n<t.length;n++){var r=t[e],$=t[n];if(null==this.modules[r][$])for(var o=-2;o<=2;o++)for(var s=-2;s<=2;s++)this.modules[r+o][$+s]=-2==o||2==o||-2==s||2==s||0==o&&0==s}},setupTypeNumber:function(t){for(var e=i.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!t&&1==(e>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(n=0;n<18;n++)r=!t&&1==(e>>n&1),this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r},setupTypeInfo:function(t,e){for(var n=this.errorCorrectLevel<<3|e,r=i.getBCHTypeInfo(n),$=0;$<15;$++){var o=!t&&1==(r>>$&1);$<6?this.modules[$][8]=o:$<8?this.modules[$+1][8]=o:this.modules[this.moduleCount-15+$][8]=o}for($=0;$<15;$++)o=!t&&1==(r>>$&1),$<8?this.modules[8][this.moduleCount-$-1]=o:$<9?this.modules[8][15-$-1+1]=o:this.modules[8][15-$-1]=o;this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var n=-1,r=this.moduleCount-1,$=7,o=0,s=this.moduleCount-1;s>0;s-=2)for(6==s&&s--;;){for(var x=0;x<2;x++)if(null==this.modules[r][s-x]){var a=!1;o<t.length&&(a=1==(t[o]>>>$&1)),i.getMask(e,r,s-x)&&(a=!a),this.modules[r][s-x]=a,-1==--$&&(o++,$=7)}if((r+=n)<0||this.moduleCount<=r){r-=n,n=-n;break}}}},n.PAD0=236,n.PAD1=17,n.createData=function(t,e,r){for(var $=x.getRSBlocks(t,e),o=new a,s=0;s<r.length;s++){var u=r[s];o.put(u.mode,4),o.put(u.getLength(),i.getLengthInBits(u.mode,t)),u.write(o)}var c=0;for(s=0;s<$.length;s++)c+=$[s].dataCount;if(o.getLengthInBits()>8*c)throw Error("code length overflow. ("+o.getLengthInBits()+">"+8*c+")");for(o.getLengthInBits()+4<=8*c&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*c||(o.put(n.PAD0,8),o.getLengthInBits()>=8*c));)o.put(n.PAD1,8);return n.createBytes(o,$)},n.createBytes=function(t,e){for(var n=0,r=0,$=0,o=Array(e.length),x=Array(e.length),a=0;a<e.length;a++){var u=e[a].dataCount,c=e[a].totalCount-u;r=Math.max(r,u),$=Math.max($,c),o[a]=Array(u);for(var l=0;l<o[a].length;l++)o[a][l]=255&t.buffer[l+n];n+=u;var h=i.getErrorCorrectPolynomial(c),f=new s(o[a],h.getLength()-1).mod(h);for(x[a]=Array(h.getLength()-1),l=0;l<x[a].length;l++){var d=l+f.getLength()-x[a].length;x[a][l]=d>=0?f.get(d):0}}var p=0;for(l=0;l<e.length;l++)p+=e[l].totalCount;var y=Array(p),g=0;for(l=0;l<r;l++)for(a=0;a<e.length;a++)l<o[a].length&&(y[g++]=o[a][l]);for(l=0;l<$;l++)for(a=0;a<e.length;a++)l<x[a].length&&(y[g++]=x[a][l]);return y};for(var r={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},i={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){for(var e=t<<10;i.getBCHDigit(e)-i.getBCHDigit(i.G15)>=0;)e^=i.G15<<i.getBCHDigit(e)-i.getBCHDigit(i.G15);return(t<<10|e)^i.G15_MASK},getBCHTypeNumber:function(t){for(var e=t<<12;i.getBCHDigit(e)-i.getBCHDigit(i.G18)>=0;)e^=i.G18<<i.getBCHDigit(e)-i.getBCHDigit(i.G18);return t<<12|e},getBCHDigit:function(t){for(var e=0;0!=t;)e++,t>>>=1;return e},getPatternPosition:function(t){return i.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,n){switch(t){case 0:return(e+n)%2==0;case 1:return e%2==0;case 2:return n%3==0;case 3:return(e+n)%3==0;case 4:return(Math.floor(e/2)+Math.floor(n/3))%2==0;case 5:return e*n%2+e*n%3==0;case 6:return(e*n%2+e*n%3)%2==0;case 7:return(e*n%3+(e+n)%2)%2==0;default:throw Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new s([1],0),n=0;n<t;n++)e=e.multiply(new s([1,$.gexp(n)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case r.MODE_NUMBER:return 10;case r.MODE_ALPHA_NUM:return 9;case r.MODE_8BIT_BYTE:case r.MODE_KANJI:return 8;default:throw Error("mode:"+t)}else if(e<27)switch(t){case r.MODE_NUMBER:return 12;case r.MODE_ALPHA_NUM:return 11;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 10;default:throw Error("mode:"+t)}else{if(!(e<41))throw Error("type:"+e);switch(t){case r.MODE_NUMBER:return 14;case r.MODE_ALPHA_NUM:return 13;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 12;default:throw Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),n=0,r=0;r<e;r++)for(var i=0;i<e;i++){for(var $=0,o=t.isDark(r,i),s=-1;s<=1;s++)if(!(r+s<0||e<=r+s))for(var x=-1;x<=1;x++)i+x<0||e<=i+x||0==s&&0==x||o==t.isDark(r+s,i+x)&&$++;$>5&&(n+=3+$-5)}for(r=0;r<e-1;r++)for(i=0;i<e-1;i++){var a=0;t.isDark(r,i)&&a++,t.isDark(r+1,i)&&a++,t.isDark(r,i+1)&&a++,t.isDark(r+1,i+1)&&a++,0!=a&&4!=a||(n+=3)}for(r=0;r<e;r++)for(i=0;i<e-6;i++)t.isDark(r,i)&&!t.isDark(r,i+1)&&t.isDark(r,i+2)&&t.isDark(r,i+3)&&t.isDark(r,i+4)&&!t.isDark(r,i+5)&&t.isDark(r,i+6)&&(n+=40);for(i=0;i<e;i++)for(r=0;r<e-6;r++)t.isDark(r,i)&&!t.isDark(r+1,i)&&t.isDark(r+2,i)&&t.isDark(r+3,i)&&t.isDark(r+4,i)&&!t.isDark(r+5,i)&&t.isDark(r+6,i)&&(n+=40);var u=0;for(i=0;i<e;i++)for(r=0;r<e;r++)t.isDark(r,i)&&u++;return n+Math.abs(100*u/e/e-50)/5*10}},$={glog:function(t){if(t<1)throw Error("glog("+t+")");return $.LOG_TABLE[t]},gexp:function(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return $.EXP_TABLE[t]},EXP_TABLE:Array(256),LOG_TABLE:Array(256)},o=0;o<8;o++)$.EXP_TABLE[o]=1<<o;for(o=8;o<256;o++)$.EXP_TABLE[o]=$.EXP_TABLE[o-4]^$.EXP_TABLE[o-5]^$.EXP_TABLE[o-6]^$.EXP_TABLE[o-8];for(o=0;o<255;o++)$.LOG_TABLE[$.EXP_TABLE[o]]=o;function s(t,e){if(null==t.length)throw Error(t.length+"/"+e);for(var n=0;n<t.length&&0==t[n];)n++;this.num=Array(t.length-n+e);for(var r=0;r<t.length-n;r++)this.num[r]=t[r+n]}function x(t,e){this.totalCount=t,this.dataCount=e}function a(){this.buffer=[],this.length=0}s.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=Array(this.getLength()+t.getLength()-1),n=0;n<this.getLength();n++)for(var r=0;r<t.getLength();r++)e[n+r]^=$.gexp($.glog(this.get(n))+$.glog(t.get(r)));return new s(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=$.glog(this.get(0))-$.glog(t.get(0)),n=Array(this.getLength()),r=0;r<this.getLength();r++)n[r]=this.get(r);for(r=0;r<t.getLength();r++)n[r]^=$.gexp($.glog(t.get(r))+e);return new s(n,0).mod(t)}},x.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],x.getRSBlocks=function(t,e){var n=x.getRsBlockTable(t,e);if(null==n)throw Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var r=n.length/3,i=[],$=0;$<r;$++)for(var o=n[3*$+0],s=n[3*$+1],a=n[3*$+2],u=0;u<o;u++)i.push(new x(s,a));return i},x.getRsBlockTable=function(t,e){switch(e){case 1:return x.RS_BLOCK_TABLE[4*(t-1)+0];case 0:return x.RS_BLOCK_TABLE[4*(t-1)+1];case 3:return x.RS_BLOCK_TABLE[4*(t-1)+2];case 2:return x.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},a.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var u=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function c(t){if(this.options={padding:4,width:256,height:256,typeNumber:4,color:"#000000",background:"#ffffff",ecl:"M",image:{svg:"",width:0,height:0}},"string"==typeof t&&(t={content:t}),t)for(var e in t)this.options[e]=t[e];if("string"!=typeof this.options.content)throw Error("Expected 'content' as string!");if(0===this.options.content.length)throw Error("Expected 'content' to be non-empty!");if(!(this.options.padding>=0))throw Error("Expected 'padding' value to be non-negative!");if(!(this.options.width>0&&this.options.height>0))throw Error("Expected 'width' or 'height' value to be higher than zero!");var r=this.options.content,i=function(t,e){for(var n,r,i=(n=t,(r=encodeURI(n).toString().replace(/\%[0-9a-fA-F]{2}/g,"a")).length+(r.length!=n?3:0)),$=1,o=0,s=0,x=u.length;s<=x;s++){var a=u[s];if(!a)throw Error("Content too long: expected "+o+" but got "+i);switch(e){case"L":o=a[0];break;case"M":o=a[1];break;case"Q":o=a[2];break;case"H":o=a[3];break;default:throw Error("Unknwon error correction level: "+e)}if(i<=o)break;$++}if($>u.length)throw Error("Content too long");return $}(r,this.options.ecl),$=function(t){switch(t){case"L":return 1;case"M":return 0;case"Q":return 3;case"H":return 2;default:throw Error("Unknwon error correction level: "+t)}}(this.options.ecl);this.qrcode=new n(i,$),this.qrcode.addData(r),this.qrcode.make()}c.prototype.svg=function(t){var e=this.options||{},n=this.qrcode.modules;void 0===t&&(t={container:e.container||"svg"});for(var r=void 0===e.pretty||!!e.pretty,i=r?"  ":"",$=r?"\r\n":"",o=e.width,s=e.height,x=n.length,a=o/(x+2*e.padding),u=s/(x+2*e.padding),c=void 0!==e.join&&!!e.join,l=void 0!==e.swap&&!!e.swap,h=void 0===e.xmlDeclaration||!!e.xmlDeclaration,f=void 0!==e.predefined&&!!e.predefined,d=f?i+'<defs><path id="qrmodule" d="M0 0 h'+u+" v"+a+' H0 z" style="fill:'+e.color+';shape-rendering:crispEdges;" /></defs>'+$:"",p=i+'<rect x="0" y="0" width="'+o+'" height="'+s+'" style="fill:'+e.background+';shape-rendering:crispEdges;"/>'+$,y="",g="",b=0;b<x;b++)for(var _=0;_<x;_++)if(n[_][b]){var m=_*a+e.padding*a,v=b*u+e.padding*u;if(l){var w=m;m=v,v=w}if(c){var S=a+m,k=u+v;m=Number.isInteger(m)?Number(m):m.toFixed(2),v=Number.isInteger(v)?Number(v):v.toFixed(2),S=Number.isInteger(S)?Number(S):S.toFixed(2),g+="M"+m+","+v+" V"+(k=Number.isInteger(k)?Number(k):k.toFixed(2))+" H"+S+" V"+v+" H"+m+" Z "}else y+=f?i+'<use x="'+m.toString()+'" y="'+v.toString()+'" href="#qrmodule" />'+$:i+'<rect x="'+m.toString()+'" y="'+v.toString()+'" width="'+a+'" height="'+u+'" style="fill:'+e.color+';shape-rendering:crispEdges;"/>'+$}c&&(y=i+'<path x="0" y="0" style="fill:'+e.color+';shape-rendering:crispEdges;" d="'+g+'" />');let E="";if(void 0!==this.options.image&&this.options.image.svg){let C=o*this.options.image.width/100,M=s*this.options.image.height/100;E+='<svg x="'+(o/2-C/2)+'" y="'+(s/2-M/2)+'" width="'+C+'" height="'+M+'" viewBox="0 0 100 100" preserveAspectRatio="xMinYMin meet">',E+=this.options.image.svg+$,E+="</svg>"}var I="";switch(t.container){case"svg":h&&(I+='<?xml version="1.0" standalone="yes"?>'+$),I+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="'+o+'" height="'+s+'">'+$,I+=d+p+y,I+=E,I+="</svg>";break;case"svg-viewbox":h&&(I+='<?xml version="1.0" standalone="yes"?>'+$),I+='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 '+o+" "+s+'">'+$,I+=d+p+y,I+=E,I+="</svg>";break;case"g":I+='<g width="'+o+'" height="'+s+'">'+$,I+=d+p+y,I+=E,I+="</g>";break;default:I+=(d+p+y+E).replace(/^\s+/,"")}return I},t.exports=c},3604(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LIB_VERSION=void 0,e.LIB_VERSION="3.3.0"},9394(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});let r=n(7187);function i(t,e,n){try{Reflect.apply(t,e,n)}catch(r){setTimeout(()=>{throw r})}}class $ extends r.EventEmitter{emit(t,...e){let n="error"===t,r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let $;if(e.length>0&&([$]=e),$ instanceof Error)throw $;let o=Error("Unhandled error."+($?" ("+$.message+")":""));throw o.context=$,o}let s=r[t];if(void 0===s)return!1;if("function"==typeof s)i(s,this,e);else{let x=s.length,a=function(t){let e=t.length,n=Array(e);for(let r=0;r<e;r+=1)n[r]=t[r];return n}(s);for(let u=0;u<x;u+=1)i(a[u],this,e)}return!0}}e.default=$},5078(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(655),i=n(2403),$=function(){function t(){this._semaphore=new i.default(1)}return t.prototype.acquire=function(){return r.__awaiter(this,void 0,void 0,function(){return r.__generator(this,function(t){switch(t.label){case 0:return[4,this._semaphore.acquire()];case 1:return[2,t.sent()[1]]}})})},t.prototype.runExclusive=function(t){return this._semaphore.runExclusive(function(){return t()})},t.prototype.isLocked=function(){return this._semaphore.isLocked()},t.prototype.release=function(){this._semaphore.release()},t}();e.default=$},2403(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(655),i=function(){function t(t){if(this._maxConcurrency=t,this._queue=[],t<=0)throw Error("semaphore must be initialized to a positive value");this._value=t}return t.prototype.acquire=function(){var t=this,e=this.isLocked(),n=new Promise(function(e){return t._queue.push(e)});return e||this._dispatch(),n},t.prototype.runExclusive=function(t){return r.__awaiter(this,void 0,void 0,function(){var e,n,i;return r.__generator(this,function(r){switch(r.label){case 0:return[4,this.acquire()];case 1:n=(e=r.sent())[0],i=e[1],r.label=2;case 2:return r.trys.push([2,,4,5]),[4,t(n)];case 3:return[2,r.sent()];case 4:return i(),[7];case 5:return[2]}})})},t.prototype.isLocked=function(){return this._value<=0},t.prototype.release=function(){if(this._maxConcurrency>1)throw Error("this method is unavailabel on semaphores with concurrency > 1; use the scoped release returned by acquire instead");if(this._currentReleaser){var t=this._currentReleaser;this._currentReleaser=void 0,t()}},t.prototype._dispatch=function(){var t=this,e=this._queue.shift();if(e){var n=!1;this._currentReleaser=function(){n||(n=!0,t._value++,t._dispatch())},e([this._value--,this._currentReleaser])}},t}();e.default=i},8125(t,e,n){"use strict";e.WU=void 0;var r=n(5078);Object.defineProperty(e,"WU",{enumerable:!0,get:function(){return r.default}}),n(2403),n(1960)},1960(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.withTimeout=void 0;var r=n(655);e.withTimeout=function(t,e,n){var i=this;return void 0===n&&(n=Error("timeout")),{acquire:function(){return new Promise(function($,o){return r.__awaiter(i,void 0,void 0,function(){var i,s;return r.__generator(this,function(r){switch(r.label){case 0:return i=!1,setTimeout(function(){i=!0,o(n)},e),[4,t.acquire()];case 1:return s=r.sent(),i?(Array.isArray(s)?s[1]:s)():$(s),[2]}})})})},runExclusive:function(t){return r.__awaiter(this,void 0,void 0,function(){var e,n;return r.__generator(this,function(r){switch(r.label){case 0:e=function(){},r.label=1;case 1:return r.trys.push([1,,7,8]),[4,this.acquire()];case 2:return Array.isArray(n=r.sent())?(e=n[1],[4,t(n[0])]):[3,4];case 3:case 5:return[2,r.sent()];case 4:return e=n,[4,t()];case 6:return[3,8];case 7:return e(),[7];case 8:return[2]}})})},release:function(){t.release()},isLocked:function(){return t.isLocked()}}}},9742(t,e){"use strict";e.byteLength=function(t){var e=x(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,$,o,s=x(t),a=s[0],u=s[1],c=new i((e=a,n=u,3*(e+n)/4-n)),l=0,h=u>0?a-4:a;for(o=0;o<h;o+=4)$=r[t.charCodeAt(o)]<<18|r[t.charCodeAt(o+1)]<<12|r[t.charCodeAt(o+2)]<<6|r[t.charCodeAt(o+3)],c[l++]=$>>16&255,c[l++]=$>>8&255,c[l++]=255&$;return 2===u&&($=r[t.charCodeAt(o)]<<2|r[t.charCodeAt(o+1)]>>4,c[l++]=255&$),1===u&&($=r[t.charCodeAt(o)]<<10|r[t.charCodeAt(o+1)]<<4|r[t.charCodeAt(o+2)]>>2,c[l++]=$>>8&255,c[l++]=255&$),c},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,$=[],o=16383,s=0,x=r-i;s<x;s+=o)$.push(a(t,s,s+o>x?x:s+o));return 1===i?$.push(n[(e=t[r-1])>>2]+n[e<<4&63]+"=="):2===i&&$.push(n[(e=(t[r-2]<<8)+t[r-1])>>10]+n[e>>4&63]+n[e<<2&63]+"="),$.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,$="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,s=$.length;o<s;++o)n[o]=$[o],r[$.charCodeAt(o)]=o;function x(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return -1===n&&(n=e),[n,n===e?0:4-n%4]}function a(t,e,r){for(var i,$,o=[],s=e;s<r;s+=3)o.push(n[($=i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]))>>18&63]+n[$>>12&63]+n[$>>6&63]+n[63&$]);return o.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},7056(t,e){"use strict";var n,r;function i(t,e,r){if(!r||typeof r.value!==n.typeOfFunction)throw TypeError("Only methods can be decorated with @bind. <"+e+"> is not a method!");return{configurable:n.boolTrue,get:function(){var t=r.value.bind(this);return Object.defineProperty(this,e,{value:t,configurable:n.boolTrue,writable:n.boolTrue}),t}}}Object.defineProperty(e,"__esModule",{value:!0}),(r=n||(n={})).typeOfFunction="function",r.boolTrue=!0,e.bind=i,e.default=i},3550:function(t,e,n){!function(t,e){"use strict";function r(t,e){if(!t)throw Error(e||"Assertion failed")}function i(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}function $(t,e,n){if($.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==e&&"be"!==e||(n=e,e=10),this._init(t||0,e||10,n||"be"))}"object"==typeof t?t.exports=$:e.BN=$,$.BN=$,$.wordSize=26;try{h="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:n(6601).Buffer}catch(o){}function s(t,e){var n=t.charCodeAt(e);return n>=48&&n<=57?n-48:n>=65&&n<=70?n-55:n>=97&&n<=102?n-87:void r(!1,"Invalid character in "+t)}function x(t,e,n){var r=s(t,n);return n-1>=e&&(r|=s(t,n-1)<<4),r}function a(t,e,n,i){for(var $=0,o=0,s=Math.min(t.length,n),x=e;x<s;x++){var a=t.charCodeAt(x)-48;$*=i,o=a>=49?a-49+10:a>=17?a-17+10:a,r(a>=0&&o<i,"Invalid character"),$+=o}return $}function u(t,e){t.words=e.words,t.length=e.length,t.negative=e.negative,t.red=e.red}if($.isBN=function(t){return t instanceof $||null!==t&&"object"==typeof t&&t.constructor.wordSize===$.wordSize&&Array.isArray(t.words)},$.max=function(t,e){return t.cmp(e)>0?t:e},$.min=function(t,e){return 0>t.cmp(e)?t:e},$.prototype._init=function(t,e,n){if("number"==typeof t)return this._initNumber(t,e,n);if("object"==typeof t)return this._initArray(t,e,n);"hex"===e&&(e=16),r(e===(0|e)&&e>=2&&e<=36);var i=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(i++,this.negative=1),i<t.length&&(16===e?this._parseHex(t,i,n):(this._parseBase(t,e,i),"le"===n&&this._initArray(this.toArray(),e,n)))},$.prototype._initNumber=function(t,e,n){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(r(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===n&&this._initArray(this.toArray(),e,n)},$.prototype._initArray=function(t,e,n){if(r("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var $,o,s=0;if("be"===n)for(i=t.length-1,$=0;i>=0;i-=3)o=t[i]|t[i-1]<<8|t[i-2]<<16,this.words[$]|=o<<s&67108863,this.words[$+1]=o>>>26-s&67108863,(s+=24)>=26&&(s-=26,$++);else if("le"===n)for(i=0,$=0;i<t.length;i+=3)o=t[i]|t[i+1]<<8|t[i+2]<<16,this.words[$]|=o<<s&67108863,this.words[$+1]=o>>>26-s&67108863,(s+=24)>=26&&(s-=26,$++);return this._strip()},$.prototype._parseHex=function(t,e,n){this.length=Math.ceil((t.length-e)/6),this.words=Array(this.length);for(var r=0;r<this.length;r++)this.words[r]=0;var i,$=0,o=0;if("be"===n)for(r=t.length-1;r>=e;r-=2)i=x(t,e,r)<<$,this.words[o]|=67108863&i,$>=18?($-=18,o+=1,this.words[o]|=i>>>26):$+=8;else for(r=(t.length-e)%2==0?e+1:e;r<t.length;r+=2)i=x(t,e,r)<<$,this.words[o]|=67108863&i,$>=18?($-=18,o+=1,this.words[o]|=i>>>26):$+=8;this._strip()},$.prototype._parseBase=function(t,e,n){this.words=[0],this.length=1;for(var r=0,i=1;i<=67108863;i*=e)r++;r--,i=i/e|0;for(var $=t.length-n,o=$%r,s=Math.min($,$-o)+n,x=0,u=n;u<s;u+=r)x=a(t,u,u+r,e),this.imuln(i),this.words[0]+x<67108864?this.words[0]+=x:this._iaddn(x);if(0!==o){var c=1;for(x=a(t,u,t.length,e),u=0;u<o;u++)c*=e;this.imuln(c),this.words[0]+x<67108864?this.words[0]+=x:this._iaddn(x)}this._strip()},$.prototype.copy=function(t){t.words=Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},$.prototype._move=function(t){u(t,this)},$.prototype.clone=function(){var t=new $(null);return this.copy(t),t},$.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},$.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},$.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},"undefined"!=typeof Symbol&&"function"==typeof Symbol.for)try{$.prototype[Symbol.for("nodejs.util.inspect.custom")]=l}catch(c){$.prototype.inspect=l}else $.prototype.inspect=l;function l(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var h,f=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],p=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function y(t,e,n){n.negative=e.negative^t.negative;var r=t.length+e.length|0;n.length=r,r=r-1|0;var i=0|t.words[0],$=0|e.words[0],o=i*$,s=67108863&o,x=o/67108864|0;n.words[0]=s;for(var a=1;a<r;a++){for(var u=x>>>26,c=67108863&x,l=Math.min(a,e.length-1),h=Math.max(0,a-t.length+1);h<=l;h++){var f=a-h|0;u+=(o=(i=0|t.words[f])*($=0|e.words[h])+c)/67108864|0,c=67108863&o}n.words[a]=0|c,x=0|u}return 0!==x?n.words[a]=0|x:n.length--,n._strip()}$.prototype.toString=function(t,e){var n;if(e=0|e||1,16===(t=t||10)||"hex"===t){n="";for(var i=0,$=0,o=0;o<this.length;o++){var s=this.words[o],x=(16777215&(s<<i|$)).toString(16);$=s>>>24-i&16777215,(i+=2)>=26&&(i-=26,o--),n=0!==$||o!==this.length-1?f[6-x.length]+x+n:x+n}for(0!==$&&(n=$.toString(16)+n);n.length%e!=0;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}if(t===(0|t)&&t>=2&&t<=36){var a=d[t],u=p[t];n="";var c=this.clone();for(c.negative=0;!c.isZero();){var l=c.modrn(u).toString(t);n=(c=c.idivn(u)).isZero()?l+n:f[a-l.length]+l+n}for(this.isZero()&&(n="0"+n);n.length%e!=0;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}r(!1,"Base should be between 2 and 36")},$.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&r(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},$.prototype.toJSON=function(){return this.toString(16,2)},h&&($.prototype.toBuffer=function(t,e){return this.toArrayLike(h,t,e)}),$.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},$.prototype.toArrayLike=function(t,e,n){this._strip();var i,$,o=this.byteLength(),s=n||Math.max(1,o);r(o<=s,"byte array longer than desired length"),r(s>0,"Requested array length <= 0");var x=(i=t,$=s,i.allocUnsafe?i.allocUnsafe($):new i($));return this["_toArrayLike"+("le"===e?"LE":"BE")](x,o),x},$.prototype._toArrayLikeLE=function(t,e){for(var n=0,r=0,i=0,$=0;i<this.length;i++){var o=this.words[i]<<$|r;t[n++]=255&o,n<t.length&&(t[n++]=o>>8&255),n<t.length&&(t[n++]=o>>16&255),6===$?(n<t.length&&(t[n++]=o>>24&255),r=0,$=0):(r=o>>>24,$+=2)}if(n<t.length)for(t[n++]=r;n<t.length;)t[n++]=0},$.prototype._toArrayLikeBE=function(t,e){for(var n=t.length-1,r=0,i=0,$=0;i<this.length;i++){var o=this.words[i]<<$|r;t[n--]=255&o,n>=0&&(t[n--]=o>>8&255),n>=0&&(t[n--]=o>>16&255),6===$?(n>=0&&(t[n--]=o>>24&255),r=0,$=0):(r=o>>>24,$+=2)}if(n>=0)for(t[n--]=r;n>=0;)t[n--]=0},Math.clz32?$.prototype._countBits=function(t){return 32-Math.clz32(t)}:$.prototype._countBits=function(t){var e=t,n=0;return e>=4096&&(n+=13,e>>>=13),e>=64&&(n+=7,e>>>=7),e>=8&&(n+=4,e>>>=4),e>=2&&(n+=2,e>>>=2),n+e},$.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,n=0;return 0==(8191&e)&&(n+=13,e>>>=13),0==(127&e)&&(n+=7,e>>>=7),0==(15&e)&&(n+=4,e>>>=4),0==(3&e)&&(n+=2,e>>>=2),0==(1&e)&&n++,n},$.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},$.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var n=this._zeroBits(this.words[e]);if(t+=n,26!==n)break}return t},$.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},$.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},$.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},$.prototype.isNeg=function(){return 0!==this.negative},$.prototype.neg=function(){return this.clone().ineg()},$.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},$.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this._strip()},$.prototype.ior=function(t){return r(0==(this.negative|t.negative)),this.iuor(t)},$.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},$.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},$.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var n=0;n<e.length;n++)this.words[n]=this.words[n]&t.words[n];return this.length=e.length,this._strip()},$.prototype.iand=function(t){return r(0==(this.negative|t.negative)),this.iuand(t)},$.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},$.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},$.prototype.iuxor=function(t){var e,n;this.length>t.length?(e=this,n=t):(e=t,n=this);for(var r=0;r<n.length;r++)this.words[r]=e.words[r]^n.words[r];if(this!==e)for(;r<e.length;r++)this.words[r]=e.words[r];return this.length=e.length,this._strip()},$.prototype.ixor=function(t){return r(0==(this.negative|t.negative)),this.iuxor(t)},$.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},$.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},$.prototype.inotn=function(t){r("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),n=t%26;this._expand(e),n>0&&e--;for(var i=0;i<e;i++)this.words[i]=67108863&~this.words[i];return n>0&&(this.words[i]=~this.words[i]&67108863>>26-n),this._strip()},$.prototype.notn=function(t){return this.clone().inotn(t)},$.prototype.setn=function(t,e){r("number"==typeof t&&t>=0);var n=t/26|0,i=t%26;return this._expand(n+1),this.words[n]=e?this.words[n]|1<<i:this.words[n]&~(1<<i),this._strip()},$.prototype.iadd=function(t){var e,n,r;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(n=this,r=t):(n=t,r=this);for(var i=0,$=0;$<r.length;$++)e=(0|n.words[$])+(0|r.words[$])+i,this.words[$]=67108863&e,i=e>>>26;for(;0!==i&&$<n.length;$++)e=(0|n.words[$])+i,this.words[$]=67108863&e,i=e>>>26;if(this.length=n.length,0!==i)this.words[this.length]=i,this.length++;else if(n!==this)for(;$<n.length;$++)this.words[$]=n.words[$];return this},$.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},$.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var n,r,i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;i>0?(n=this,r=t):(n=t,r=this);for(var $=0,o=0;o<r.length;o++)$=(e=(0|n.words[o])-(0|r.words[o])+$)>>26,this.words[o]=67108863&e;for(;0!==$&&o<n.length;o++)$=(e=(0|n.words[o])+$)>>26,this.words[o]=67108863&e;if(0===$&&o<n.length&&n!==this)for(;o<n.length;o++)this.words[o]=n.words[o];return this.length=Math.max(this.length,o),n!==this&&(this.negative=1),this._strip()},$.prototype.sub=function(t){return this.clone().isub(t)};var g=function(t,e,n){var r,i,$,o=t.words,s=e.words,x=n.words,a=0,u=0|o[0],c=8191&u,l=u>>>13,h=0|o[1],f=8191&h,d=h>>>13,p=0|o[2],y=8191&p,g=p>>>13,b=0|o[3],_=8191&b,m=b>>>13,v=0|o[4],w=8191&v,S=v>>>13,k=0|o[5],E=8191&k,C=k>>>13,M=0|o[6],I=8191&M,A=M>>>13,T=0|o[7],R=8191&T,L=T>>>13,N=0|o[8],P=8191&N,D=N>>>13,O=0|o[9],B=8191&O,j=O>>>13,U=0|s[0],F=8191&U,W=U>>>13,z=0|s[1],H=8191&z,V=z>>>13,q=0|s[2],Z=8191&q,G=q>>>13,Y=0|s[3],K=8191&Y,Q=Y>>>13,J=0|s[4],X=8191&J,tt=J>>>13,te=0|s[5],tn=8191&te,tr=te>>>13,ti=0|s[6],t$=8191&ti,to=ti>>>13,ts=0|s[7],tx=8191&ts,ta=ts>>>13,tu=0|s[8],tc=8191&tu,tl=tu>>>13,th=0|s[9],tf=8191&th,td=th>>>13;n.negative=t.negative^e.negative,n.length=19;var tp=(a+(r=Math.imul(c,F))|0)+((8191&(i=(i=Math.imul(c,W))+Math.imul(l,F)|0))<<13)|0;a=(($=Math.imul(l,W))+(i>>>13)|0)+(tp>>>26)|0,tp&=67108863,r=Math.imul(f,F),i=(i=Math.imul(f,W))+Math.imul(d,F)|0,$=Math.imul(d,W);var t0=(a+(r=r+Math.imul(c,H)|0)|0)+((8191&(i=(i=i+Math.imul(c,V)|0)+Math.imul(l,H)|0))<<13)|0;a=(($=$+Math.imul(l,V)|0)+(i>>>13)|0)+(t0>>>26)|0,t0&=67108863,r=Math.imul(y,F),i=(i=Math.imul(y,W))+Math.imul(g,F)|0,$=Math.imul(g,W),r=r+Math.imul(f,H)|0,i=(i=i+Math.imul(f,V)|0)+Math.imul(d,H)|0,$=$+Math.imul(d,V)|0;var ty=(a+(r=r+Math.imul(c,Z)|0)|0)+((8191&(i=(i=i+Math.imul(c,G)|0)+Math.imul(l,Z)|0))<<13)|0;a=(($=$+Math.imul(l,G)|0)+(i>>>13)|0)+(ty>>>26)|0,ty&=67108863,r=Math.imul(_,F),i=(i=Math.imul(_,W))+Math.imul(m,F)|0,$=Math.imul(m,W),r=r+Math.imul(y,H)|0,i=(i=i+Math.imul(y,V)|0)+Math.imul(g,H)|0,$=$+Math.imul(g,V)|0,r=r+Math.imul(f,Z)|0,i=(i=i+Math.imul(f,G)|0)+Math.imul(d,Z)|0,$=$+Math.imul(d,G)|0;var tg=(a+(r=r+Math.imul(c,K)|0)|0)+((8191&(i=(i=i+Math.imul(c,Q)|0)+Math.imul(l,K)|0))<<13)|0;a=(($=$+Math.imul(l,Q)|0)+(i>>>13)|0)+(tg>>>26)|0,tg&=67108863,r=Math.imul(w,F),i=(i=Math.imul(w,W))+Math.imul(S,F)|0,$=Math.imul(S,W),r=r+Math.imul(_,H)|0,i=(i=i+Math.imul(_,V)|0)+Math.imul(m,H)|0,$=$+Math.imul(m,V)|0,r=r+Math.imul(y,Z)|0,i=(i=i+Math.imul(y,G)|0)+Math.imul(g,Z)|0,$=$+Math.imul(g,G)|0,r=r+Math.imul(f,K)|0,i=(i=i+Math.imul(f,Q)|0)+Math.imul(d,K)|0,$=$+Math.imul(d,Q)|0;var tb=(a+(r=r+Math.imul(c,X)|0)|0)+((8191&(i=(i=i+Math.imul(c,tt)|0)+Math.imul(l,X)|0))<<13)|0;a=(($=$+Math.imul(l,tt)|0)+(i>>>13)|0)+(tb>>>26)|0,tb&=67108863,r=Math.imul(E,F),i=(i=Math.imul(E,W))+Math.imul(C,F)|0,$=Math.imul(C,W),r=r+Math.imul(w,H)|0,i=(i=i+Math.imul(w,V)|0)+Math.imul(S,H)|0,$=$+Math.imul(S,V)|0,r=r+Math.imul(_,Z)|0,i=(i=i+Math.imul(_,G)|0)+Math.imul(m,Z)|0,$=$+Math.imul(m,G)|0,r=r+Math.imul(y,K)|0,i=(i=i+Math.imul(y,Q)|0)+Math.imul(g,K)|0,$=$+Math.imul(g,Q)|0,r=r+Math.imul(f,X)|0,i=(i=i+Math.imul(f,tt)|0)+Math.imul(d,X)|0,$=$+Math.imul(d,tt)|0;var t_=(a+(r=r+Math.imul(c,tn)|0)|0)+((8191&(i=(i=i+Math.imul(c,tr)|0)+Math.imul(l,tn)|0))<<13)|0;a=(($=$+Math.imul(l,tr)|0)+(i>>>13)|0)+(t_>>>26)|0,t_&=67108863,r=Math.imul(I,F),i=(i=Math.imul(I,W))+Math.imul(A,F)|0,$=Math.imul(A,W),r=r+Math.imul(E,H)|0,i=(i=i+Math.imul(E,V)|0)+Math.imul(C,H)|0,$=$+Math.imul(C,V)|0,r=r+Math.imul(w,Z)|0,i=(i=i+Math.imul(w,G)|0)+Math.imul(S,Z)|0,$=$+Math.imul(S,G)|0,r=r+Math.imul(_,K)|0,i=(i=i+Math.imul(_,Q)|0)+Math.imul(m,K)|0,$=$+Math.imul(m,Q)|0,r=r+Math.imul(y,X)|0,i=(i=i+Math.imul(y,tt)|0)+Math.imul(g,X)|0,$=$+Math.imul(g,tt)|0,r=r+Math.imul(f,tn)|0,i=(i=i+Math.imul(f,tr)|0)+Math.imul(d,tn)|0,$=$+Math.imul(d,tr)|0;var tm=(a+(r=r+Math.imul(c,t$)|0)|0)+((8191&(i=(i=i+Math.imul(c,to)|0)+Math.imul(l,t$)|0))<<13)|0;a=(($=$+Math.imul(l,to)|0)+(i>>>13)|0)+(tm>>>26)|0,tm&=67108863,r=Math.imul(R,F),i=(i=Math.imul(R,W))+Math.imul(L,F)|0,$=Math.imul(L,W),r=r+Math.imul(I,H)|0,i=(i=i+Math.imul(I,V)|0)+Math.imul(A,H)|0,$=$+Math.imul(A,V)|0,r=r+Math.imul(E,Z)|0,i=(i=i+Math.imul(E,G)|0)+Math.imul(C,Z)|0,$=$+Math.imul(C,G)|0,r=r+Math.imul(w,K)|0,i=(i=i+Math.imul(w,Q)|0)+Math.imul(S,K)|0,$=$+Math.imul(S,Q)|0,r=r+Math.imul(_,X)|0,i=(i=i+Math.imul(_,tt)|0)+Math.imul(m,X)|0,$=$+Math.imul(m,tt)|0,r=r+Math.imul(y,tn)|0,i=(i=i+Math.imul(y,tr)|0)+Math.imul(g,tn)|0,$=$+Math.imul(g,tr)|0,r=r+Math.imul(f,t$)|0,i=(i=i+Math.imul(f,to)|0)+Math.imul(d,t$)|0,$=$+Math.imul(d,to)|0;var tv=(a+(r=r+Math.imul(c,tx)|0)|0)+((8191&(i=(i=i+Math.imul(c,ta)|0)+Math.imul(l,tx)|0))<<13)|0;a=(($=$+Math.imul(l,ta)|0)+(i>>>13)|0)+(tv>>>26)|0,tv&=67108863,r=Math.imul(P,F),i=(i=Math.imul(P,W))+Math.imul(D,F)|0,$=Math.imul(D,W),r=r+Math.imul(R,H)|0,i=(i=i+Math.imul(R,V)|0)+Math.imul(L,H)|0,$=$+Math.imul(L,V)|0,r=r+Math.imul(I,Z)|0,i=(i=i+Math.imul(I,G)|0)+Math.imul(A,Z)|0,$=$+Math.imul(A,G)|0,r=r+Math.imul(E,K)|0,i=(i=i+Math.imul(E,Q)|0)+Math.imul(C,K)|0,$=$+Math.imul(C,Q)|0,r=r+Math.imul(w,X)|0,i=(i=i+Math.imul(w,tt)|0)+Math.imul(S,X)|0,$=$+Math.imul(S,tt)|0,r=r+Math.imul(_,tn)|0,i=(i=i+Math.imul(_,tr)|0)+Math.imul(m,tn)|0,$=$+Math.imul(m,tr)|0,r=r+Math.imul(y,t$)|0,i=(i=i+Math.imul(y,to)|0)+Math.imul(g,t$)|0,$=$+Math.imul(g,to)|0,r=r+Math.imul(f,tx)|0,i=(i=i+Math.imul(f,ta)|0)+Math.imul(d,tx)|0,$=$+Math.imul(d,ta)|0;var tw=(a+(r=r+Math.imul(c,tc)|0)|0)+((8191&(i=(i=i+Math.imul(c,tl)|0)+Math.imul(l,tc)|0))<<13)|0;a=(($=$+Math.imul(l,tl)|0)+(i>>>13)|0)+(tw>>>26)|0,tw&=67108863,r=Math.imul(B,F),i=(i=Math.imul(B,W))+Math.imul(j,F)|0,$=Math.imul(j,W),r=r+Math.imul(P,H)|0,i=(i=i+Math.imul(P,V)|0)+Math.imul(D,H)|0,$=$+Math.imul(D,V)|0,r=r+Math.imul(R,Z)|0,i=(i=i+Math.imul(R,G)|0)+Math.imul(L,Z)|0,$=$+Math.imul(L,G)|0,r=r+Math.imul(I,K)|0,i=(i=i+Math.imul(I,Q)|0)+Math.imul(A,K)|0,$=$+Math.imul(A,Q)|0,r=r+Math.imul(E,X)|0,i=(i=i+Math.imul(E,tt)|0)+Math.imul(C,X)|0,$=$+Math.imul(C,tt)|0,r=r+Math.imul(w,tn)|0,i=(i=i+Math.imul(w,tr)|0)+Math.imul(S,tn)|0,$=$+Math.imul(S,tr)|0,r=r+Math.imul(_,t$)|0,i=(i=i+Math.imul(_,to)|0)+Math.imul(m,t$)|0,$=$+Math.imul(m,to)|0,r=r+Math.imul(y,tx)|0,i=(i=i+Math.imul(y,ta)|0)+Math.imul(g,tx)|0,$=$+Math.imul(g,ta)|0,r=r+Math.imul(f,tc)|0,i=(i=i+Math.imul(f,tl)|0)+Math.imul(d,tc)|0,$=$+Math.imul(d,tl)|0;var t8=(a+(r=r+Math.imul(c,tf)|0)|0)+((8191&(i=(i=i+Math.imul(c,td)|0)+Math.imul(l,tf)|0))<<13)|0;a=(($=$+Math.imul(l,td)|0)+(i>>>13)|0)+(t8>>>26)|0,t8&=67108863,r=Math.imul(B,H),i=(i=Math.imul(B,V))+Math.imul(j,H)|0,$=Math.imul(j,V),r=r+Math.imul(P,Z)|0,i=(i=i+Math.imul(P,G)|0)+Math.imul(D,Z)|0,$=$+Math.imul(D,G)|0,r=r+Math.imul(R,K)|0,i=(i=i+Math.imul(R,Q)|0)+Math.imul(L,K)|0,$=$+Math.imul(L,Q)|0,r=r+Math.imul(I,X)|0,i=(i=i+Math.imul(I,tt)|0)+Math.imul(A,X)|0,$=$+Math.imul(A,tt)|0,r=r+Math.imul(E,tn)|0,i=(i=i+Math.imul(E,tr)|0)+Math.imul(C,tn)|0,$=$+Math.imul(C,tr)|0,r=r+Math.imul(w,t$)|0,i=(i=i+Math.imul(w,to)|0)+Math.imul(S,t$)|0,$=$+Math.imul(S,to)|0,r=r+Math.imul(_,tx)|0,i=(i=i+Math.imul(_,ta)|0)+Math.imul(m,tx)|0,$=$+Math.imul(m,ta)|0,r=r+Math.imul(y,tc)|0,i=(i=i+Math.imul(y,tl)|0)+Math.imul(g,tc)|0,$=$+Math.imul(g,tl)|0;var t1=(a+(r=r+Math.imul(f,tf)|0)|0)+((8191&(i=(i=i+Math.imul(f,td)|0)+Math.imul(d,tf)|0))<<13)|0;a=(($=$+Math.imul(d,td)|0)+(i>>>13)|0)+(t1>>>26)|0,t1&=67108863,r=Math.imul(B,Z),i=(i=Math.imul(B,G))+Math.imul(j,Z)|0,$=Math.imul(j,G),r=r+Math.imul(P,K)|0,i=(i=i+Math.imul(P,Q)|0)+Math.imul(D,K)|0,$=$+Math.imul(D,Q)|0,r=r+Math.imul(R,X)|0,i=(i=i+Math.imul(R,tt)|0)+Math.imul(L,X)|0,$=$+Math.imul(L,tt)|0,r=r+Math.imul(I,tn)|0,i=(i=i+Math.imul(I,tr)|0)+Math.imul(A,tn)|0,$=$+Math.imul(A,tr)|0,r=r+Math.imul(E,t$)|0,i=(i=i+Math.imul(E,to)|0)+Math.imul(C,t$)|0,$=$+Math.imul(C,to)|0,r=r+Math.imul(w,tx)|0,i=(i=i+Math.imul(w,ta)|0)+Math.imul(S,tx)|0,$=$+Math.imul(S,ta)|0,r=r+Math.imul(_,tc)|0,i=(i=i+Math.imul(_,tl)|0)+Math.imul(m,tc)|0,$=$+Math.imul(m,tl)|0;var tS=(a+(r=r+Math.imul(y,tf)|0)|0)+((8191&(i=(i=i+Math.imul(y,td)|0)+Math.imul(g,tf)|0))<<13)|0;a=(($=$+Math.imul(g,td)|0)+(i>>>13)|0)+(tS>>>26)|0,tS&=67108863,r=Math.imul(B,K),i=(i=Math.imul(B,Q))+Math.imul(j,K)|0,$=Math.imul(j,Q),r=r+Math.imul(P,X)|0,i=(i=i+Math.imul(P,tt)|0)+Math.imul(D,X)|0,$=$+Math.imul(D,tt)|0,r=r+Math.imul(R,tn)|0,i=(i=i+Math.imul(R,tr)|0)+Math.imul(L,tn)|0,$=$+Math.imul(L,tr)|0,r=r+Math.imul(I,t$)|0,i=(i=i+Math.imul(I,to)|0)+Math.imul(A,t$)|0,$=$+Math.imul(A,to)|0,r=r+Math.imul(E,tx)|0,i=(i=i+Math.imul(E,ta)|0)+Math.imul(C,tx)|0,$=$+Math.imul(C,ta)|0,r=r+Math.imul(w,tc)|0,i=(i=i+Math.imul(w,tl)|0)+Math.imul(S,tc)|0,$=$+Math.imul(S,tl)|0;var t2=(a+(r=r+Math.imul(_,tf)|0)|0)+((8191&(i=(i=i+Math.imul(_,td)|0)+Math.imul(m,tf)|0))<<13)|0;a=(($=$+Math.imul(m,td)|0)+(i>>>13)|0)+(t2>>>26)|0,t2&=67108863,r=Math.imul(B,X),i=(i=Math.imul(B,tt))+Math.imul(j,X)|0,$=Math.imul(j,tt),r=r+Math.imul(P,tn)|0,i=(i=i+Math.imul(P,tr)|0)+Math.imul(D,tn)|0,$=$+Math.imul(D,tr)|0,r=r+Math.imul(R,t$)|0,i=(i=i+Math.imul(R,to)|0)+Math.imul(L,t$)|0,$=$+Math.imul(L,to)|0,r=r+Math.imul(I,tx)|0,i=(i=i+Math.imul(I,ta)|0)+Math.imul(A,tx)|0,$=$+Math.imul(A,ta)|0,r=r+Math.imul(E,tc)|0,i=(i=i+Math.imul(E,tl)|0)+Math.imul(C,tc)|0,$=$+Math.imul(C,tl)|0;var tk=(a+(r=r+Math.imul(w,tf)|0)|0)+((8191&(i=(i=i+Math.imul(w,td)|0)+Math.imul(S,tf)|0))<<13)|0;a=(($=$+Math.imul(S,td)|0)+(i>>>13)|0)+(tk>>>26)|0,tk&=67108863,r=Math.imul(B,tn),i=(i=Math.imul(B,tr))+Math.imul(j,tn)|0,$=Math.imul(j,tr),r=r+Math.imul(P,t$)|0,i=(i=i+Math.imul(P,to)|0)+Math.imul(D,t$)|0,$=$+Math.imul(D,to)|0,r=r+Math.imul(R,tx)|0,i=(i=i+Math.imul(R,ta)|0)+Math.imul(L,tx)|0,$=$+Math.imul(L,ta)|0,r=r+Math.imul(I,tc)|0,i=(i=i+Math.imul(I,tl)|0)+Math.imul(A,tc)|0,$=$+Math.imul(A,tl)|0;var tE=(a+(r=r+Math.imul(E,tf)|0)|0)+((8191&(i=(i=i+Math.imul(E,td)|0)+Math.imul(C,tf)|0))<<13)|0;a=(($=$+Math.imul(C,td)|0)+(i>>>13)|0)+(tE>>>26)|0,tE&=67108863,r=Math.imul(B,t$),i=(i=Math.imul(B,to))+Math.imul(j,t$)|0,$=Math.imul(j,to),r=r+Math.imul(P,tx)|0,i=(i=i+Math.imul(P,ta)|0)+Math.imul(D,tx)|0,$=$+Math.imul(D,ta)|0,r=r+Math.imul(R,tc)|0,i=(i=i+Math.imul(R,tl)|0)+Math.imul(L,tc)|0,$=$+Math.imul(L,tl)|0;var t6=(a+(r=r+Math.imul(I,tf)|0)|0)+((8191&(i=(i=i+Math.imul(I,td)|0)+Math.imul(A,tf)|0))<<13)|0;a=(($=$+Math.imul(A,td)|0)+(i>>>13)|0)+(t6>>>26)|0,t6&=67108863,r=Math.imul(B,tx),i=(i=Math.imul(B,ta))+Math.imul(j,tx)|0,$=Math.imul(j,ta),r=r+Math.imul(P,tc)|0,i=(i=i+Math.imul(P,tl)|0)+Math.imul(D,tc)|0,$=$+Math.imul(D,tl)|0;var t3=(a+(r=r+Math.imul(R,tf)|0)|0)+((8191&(i=(i=i+Math.imul(R,td)|0)+Math.imul(L,tf)|0))<<13)|0;a=(($=$+Math.imul(L,td)|0)+(i>>>13)|0)+(t3>>>26)|0,t3&=67108863,r=Math.imul(B,tc),i=(i=Math.imul(B,tl))+Math.imul(j,tc)|0,$=Math.imul(j,tl);var t4=(a+(r=r+Math.imul(P,tf)|0)|0)+((8191&(i=(i=i+Math.imul(P,td)|0)+Math.imul(D,tf)|0))<<13)|0;a=(($=$+Math.imul(D,td)|0)+(i>>>13)|0)+(t4>>>26)|0,t4&=67108863;var tC=(a+(r=Math.imul(B,tf))|0)+((8191&(i=(i=Math.imul(B,td))+Math.imul(j,tf)|0))<<13)|0;return a=(($=Math.imul(j,td))+(i>>>13)|0)+(tC>>>26)|0,tC&=67108863,x[0]=tp,x[1]=t0,x[2]=ty,x[3]=tg,x[4]=tb,x[5]=t_,x[6]=tm,x[7]=tv,x[8]=tw,x[9]=t8,x[10]=t1,x[11]=tS,x[12]=t2,x[13]=tk,x[14]=tE,x[15]=t6,x[16]=t3,x[17]=t4,x[18]=tC,0!==a&&(x[19]=a,n.length++),n};function b(t,e,n){n.negative=e.negative^t.negative,n.length=t.length+e.length;for(var r=0,i=0,$=0;$<n.length-1;$++){var o=i;i=0;for(var s=67108863&r,x=Math.min($,e.length-1),a=Math.max(0,$-t.length+1);a<=x;a++){var u=$-a,c=(0|t.words[u])*(0|e.words[a]),l=67108863&c;s=67108863&(l=l+s|0),i+=(o=(o=o+(c/67108864|0)|0)+(l>>>26)|0)>>>26,o&=67108863}n.words[$]=s,r=o,o=i}return 0!==r?n.words[$]=r:n.length--,n._strip()}function _(t,e,n){return b(t,e,n)}function m(t,e){this.x=t,this.y=e}Math.imul||(g=y),$.prototype.mulTo=function(t,e){var n=this.length+t.length;return 10===this.length&&10===t.length?g(this,t,e):n<63?y(this,t,e):n<1024?b(this,t,e):_(this,t,e)},m.prototype.makeRBT=function(t){for(var e=Array(t),n=$.prototype._countBits(t)-1,r=0;r<t;r++)e[r]=this.revBin(r,n,t);return e},m.prototype.revBin=function(t,e,n){if(0===t||t===n-1)return t;for(var r=0,i=0;i<e;i++)r|=(1&t)<<e-i-1,t>>=1;return r},m.prototype.permute=function(t,e,n,r,i,$){for(var o=0;o<$;o++)r[o]=e[t[o]],i[o]=n[t[o]]},m.prototype.transform=function(t,e,n,r,i,$){this.permute($,t,e,n,r,i);for(var o=1;o<i;o<<=1)for(var s=o<<1,x=Math.cos(2*Math.PI/s),a=Math.sin(2*Math.PI/s),u=0;u<i;u+=s)for(var c=x,l=a,h=0;h<o;h++){var f=n[u+h],d=r[u+h],p=n[u+h+o],y=r[u+h+o],g=c*p-l*y;y=c*y+l*p,p=g,n[u+h]=f+p,r[u+h]=d+y,n[u+h+o]=f-p,r[u+h+o]=d-y,h!==s&&(g=x*c-a*l,l=x*l+a*c,c=g)}},m.prototype.guessLen13b=function(t,e){var n=1|Math.max(e,t),r=1&n,i=0;for(n=n/2|0;n;n>>>=1)i++;return 1<<i+1+r},m.prototype.conjugate=function(t,e,n){if(!(n<=1))for(var r=0;r<n/2;r++){var i=t[r];t[r]=t[n-r-1],t[n-r-1]=i,i=e[r],e[r]=-e[n-r-1],e[n-r-1]=-i}},m.prototype.normalize13b=function(t,e){for(var n=0,r=0;r<e/2;r++){var i=8192*Math.round(t[2*r+1]/e)+Math.round(t[2*r]/e)+n;t[r]=67108863&i,n=i<67108864?0:i/67108864|0}return t},m.prototype.convert13b=function(t,e,n,i){for(var $=0,o=0;o<e;o++)$+=0|t[o],n[2*o]=8191&$,$>>>=13,n[2*o+1]=8191&$,$>>>=13;for(o=2*e;o<i;++o)n[o]=0;r(0===$),r(0==(-8192&$))},m.prototype.stub=function(t){for(var e=Array(t),n=0;n<t;n++)e[n]=0;return e},m.prototype.mulp=function(t,e,n){var r=2*this.guessLen13b(t.length,e.length),i=this.makeRBT(r),$=this.stub(r),o=Array(r),s=Array(r),x=Array(r),a=Array(r),u=Array(r),c=Array(r),l=n.words;l.length=r,this.convert13b(t.words,t.length,o,r),this.convert13b(e.words,e.length,a,r),this.transform(o,$,s,x,r,i),this.transform(a,$,u,c,r,i);for(var h=0;h<r;h++){var f=s[h]*u[h]-x[h]*c[h];x[h]=s[h]*c[h]+x[h]*u[h],s[h]=f}return this.conjugate(s,x,r),this.transform(s,x,l,$,r,i),this.conjugate(l,$,r),this.normalize13b(l,r),n.negative=t.negative^e.negative,n.length=t.length+e.length,n._strip()},$.prototype.mul=function(t){var e=new $(null);return e.words=Array(this.length+t.length),this.mulTo(t,e)},$.prototype.mulf=function(t){var e=new $(null);return e.words=Array(this.length+t.length),_(this,t,e)},$.prototype.imul=function(t){return this.clone().mulTo(t,this)},$.prototype.imuln=function(t){var e=t<0;e&&(t=-t),r("number"==typeof t),r(t<67108864);for(var n=0,i=0;i<this.length;i++){var $=(0|this.words[i])*t,o=(67108863&$)+(67108863&n);n>>=26,n+=$/67108864|0,n+=o>>>26,this.words[i]=67108863&o}return 0!==n&&(this.words[i]=n,this.length++),e?this.ineg():this},$.prototype.muln=function(t){return this.clone().imuln(t)},$.prototype.sqr=function(){return this.mul(this)},$.prototype.isqr=function(){return this.imul(this.clone())},$.prototype.pow=function(t){var e=function(t){for(var e=Array(t.bitLength()),n=0;n<e.length;n++){var r=n/26|0,i=n%26;e[n]=t.words[r]>>>i&1}return e}(t);if(0===e.length)return new $(1);for(var n=this,r=0;r<e.length&&0===e[r];r++,n=n.sqr());if(++r<e.length)for(var i=n.sqr();r<e.length;r++,i=i.sqr())0!==e[r]&&(n=n.mul(i));return n},$.prototype.iushln=function(t){r("number"==typeof t&&t>=0);var e,n=t%26,i=(t-n)/26,$=67108863>>>26-n<<26-n;if(0!==n){var o=0;for(e=0;e<this.length;e++){var s=this.words[e]&$,x=(0|this.words[e])-s<<n;this.words[e]=x|o,o=s>>>26-n}o&&(this.words[e]=o,this.length++)}if(0!==i){for(e=this.length-1;e>=0;e--)this.words[e+i]=this.words[e];for(e=0;e<i;e++)this.words[e]=0;this.length+=i}return this._strip()},$.prototype.ishln=function(t){return r(0===this.negative),this.iushln(t)},$.prototype.iushrn=function(t,e,n){r("number"==typeof t&&t>=0),i=e?(e-e%26)/26:0;var i,$=t%26,o=Math.min((t-$)/26,this.length),s=67108863^67108863>>>$<<$,x=n;if(i-=o,i=Math.max(0,i),x){for(var a=0;a<o;a++)x.words[a]=this.words[a];x.length=o}if(0===o);else if(this.length>o)for(this.length-=o,a=0;a<this.length;a++)this.words[a]=this.words[a+o];else this.words[0]=0,this.length=1;var u=0;for(a=this.length-1;a>=0&&(0!==u||a>=i);a--){var c=0|this.words[a];this.words[a]=u<<26-$|c>>>$,u=c&s}return x&&0!==u&&(x.words[x.length++]=u),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},$.prototype.ishrn=function(t,e,n){return r(0===this.negative),this.iushrn(t,e,n)},$.prototype.shln=function(t){return this.clone().ishln(t)},$.prototype.ushln=function(t){return this.clone().iushln(t)},$.prototype.shrn=function(t){return this.clone().ishrn(t)},$.prototype.ushrn=function(t){return this.clone().iushrn(t)},$.prototype.testn=function(t){r("number"==typeof t&&t>=0);var e=t%26,n=(t-e)/26,i=1<<e;return!(this.length<=n||!(this.words[n]&i))},$.prototype.imaskn=function(t){r("number"==typeof t&&t>=0);var e=t%26,n=(t-e)/26;if(r(0===this.negative,"imaskn works only with positive numbers"),this.length<=n)return this;if(0!==e&&n++,this.length=Math.min(n,this.length),0!==e){var i=67108863^67108863>>>e<<e;this.words[this.length-1]&=i}return this._strip()},$.prototype.maskn=function(t){return this.clone().imaskn(t)},$.prototype.iaddn=function(t){return r("number"==typeof t),r(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<=t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},$.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},$.prototype.isubn=function(t){if(r("number"==typeof t),r(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this._strip()},$.prototype.addn=function(t){return this.clone().iaddn(t)},$.prototype.subn=function(t){return this.clone().isubn(t)},$.prototype.iabs=function(){return this.negative=0,this},$.prototype.abs=function(){return this.clone().iabs()},$.prototype._ishlnsubmul=function(t,e,n){var i,$,o=t.length+n;this._expand(o);var s=0;for(i=0;i<t.length;i++){$=(0|this.words[i+n])+s;var x=(0|t.words[i])*e;s=(($-=67108863&x)>>26)-(x/67108864|0),this.words[i+n]=67108863&$}for(;i<this.length-n;i++)s=($=(0|this.words[i+n])+s)>>26,this.words[i+n]=67108863&$;if(0===s)return this._strip();for(r(-1===s),s=0,i=0;i<this.length;i++)s=($=-(0|this.words[i])+s)>>26,this.words[i]=67108863&$;return this.negative=1,this._strip()},$.prototype._wordDiv=function(t,e){var n=(this.length,t.length),r=this.clone(),i=t,o=0|i.words[i.length-1];0!=(n=26-this._countBits(o))&&(i=i.ushln(n),r.iushln(n),o=0|i.words[i.length-1]);var s,x=r.length-i.length;if("mod"!==e){(s=new $(null)).length=x+1,s.words=Array(s.length);for(var a=0;a<s.length;a++)s.words[a]=0}var u=r.clone()._ishlnsubmul(i,1,x);0===u.negative&&(r=u,s&&(s.words[x]=1));for(var c=x-1;c>=0;c--){var l=67108864*(0|r.words[i.length+c])+(0|r.words[i.length+c-1]);for(l=Math.min(l/o|0,67108863),r._ishlnsubmul(i,l,c);0!==r.negative;)l--,r.negative=0,r._ishlnsubmul(i,1,c),r.isZero()||(r.negative^=1);s&&(s.words[c]=l)}return s&&s._strip(),r._strip(),"div"!==e&&0!==n&&r.iushrn(n),{div:s||null,mod:r}},$.prototype.divmod=function(t,e,n){var i,o,s;return r(!t.isZero()),this.isZero()?{div:new $(0),mod:new $(0)}:0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,e),"mod"!==e&&(i=s.div.neg()),"div"!==e&&(o=s.mod.neg(),n&&0!==o.negative&&o.iadd(t)),{div:i,mod:o}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),e),"mod"!==e&&(i=s.div.neg()),{div:i,mod:s.mod}):0!=(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),e),"div"!==e&&(o=s.mod.neg(),n&&0!==o.negative&&o.isub(t)),{div:s.div,mod:o}):t.length>this.length||0>this.cmp(t)?{div:new $(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new $(this.modrn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new $(this.modrn(t.words[0]))}:this._wordDiv(t,e)},$.prototype.div=function(t){return this.divmod(t,"div",!1).div},$.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},$.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},$.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var n=0!==e.div.negative?e.mod.isub(t):e.mod,r=t.ushrn(1),i=t.andln(1),$=n.cmp(r);return $<0||1===i&&0===$?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},$.prototype.modrn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var n=67108864%t,i=0,$=this.length-1;$>=0;$--)i=(n*i+(0|this.words[$]))%t;return e?-i:i},$.prototype.modn=function(t){return this.modrn(t)},$.prototype.idivn=function(t){var e=t<0;e&&(t=-t),r(t<=67108863);for(var n=0,i=this.length-1;i>=0;i--){var $=(0|this.words[i])+67108864*n;this.words[i]=$/t|0,n=$%t}return this._strip(),e?this.ineg():this},$.prototype.divn=function(t){return this.clone().idivn(t)},$.prototype.egcd=function(t){r(0===t.negative),r(!t.isZero());var e=this,n=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i=new $(1),o=new $(0),s=new $(0),x=new $(1),a=0;e.isEven()&&n.isEven();)e.iushrn(1),n.iushrn(1),++a;for(var u=n.clone(),c=e.clone();!e.isZero();){for(var l=0,h=1;0==(e.words[0]&h)&&l<26;++l,h<<=1);if(l>0)for(e.iushrn(l);l-- >0;)(i.isOdd()||o.isOdd())&&(i.iadd(u),o.isub(c)),i.iushrn(1),o.iushrn(1);for(var f=0,d=1;0==(n.words[0]&d)&&f<26;++f,d<<=1);if(f>0)for(n.iushrn(f);f-- >0;)(s.isOdd()||x.isOdd())&&(s.iadd(u),x.isub(c)),s.iushrn(1),x.iushrn(1);e.cmp(n)>=0?(e.isub(n),i.isub(s),o.isub(x)):(n.isub(e),s.isub(i),x.isub(o))}return{a:s,b:x,gcd:n.iushln(a)}},$.prototype._invmp=function(t){r(0===t.negative),r(!t.isZero());var e=this,n=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i,o=new $(1),s=new $(0),x=n.clone();e.cmpn(1)>0&&n.cmpn(1)>0;){for(var a=0,u=1;0==(e.words[0]&u)&&a<26;++a,u<<=1);if(a>0)for(e.iushrn(a);a-- >0;)o.isOdd()&&o.iadd(x),o.iushrn(1);for(var c=0,l=1;0==(n.words[0]&l)&&c<26;++c,l<<=1);if(c>0)for(n.iushrn(c);c-- >0;)s.isOdd()&&s.iadd(x),s.iushrn(1);e.cmp(n)>=0?(e.isub(n),o.isub(s)):(n.isub(e),s.isub(o))}return 0>(i=0===e.cmpn(1)?o:s).cmpn(0)&&i.iadd(t),i},$.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),n=t.clone();e.negative=0,n.negative=0;for(var r=0;e.isEven()&&n.isEven();r++)e.iushrn(1),n.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;n.isEven();)n.iushrn(1);var i=e.cmp(n);if(i<0){var $=e;e=n,n=$}else if(0===i||0===n.cmpn(1))break;e.isub(n)}return n.iushln(r)},$.prototype.invm=function(t){return this.egcd(t).a.umod(t)},$.prototype.isEven=function(){return 0==(1&this.words[0])},$.prototype.isOdd=function(){return 1==(1&this.words[0])},$.prototype.andln=function(t){return this.words[0]&t},$.prototype.bincn=function(t){r("number"==typeof t);var e=t%26,n=(t-e)/26,i=1<<e;if(this.length<=n)return this._expand(n+1),this.words[n]|=i,this;for(var $=i,o=n;0!==$&&o<this.length;o++){var s=0|this.words[o];$=(s+=$)>>>26,s&=67108863,this.words[o]=s}return 0!==$&&(this.words[o]=$,this.length++),this},$.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},$.prototype.cmpn=function(t){var e,n=t<0;if(0!==this.negative&&!n)return -1;if(0===this.negative&&n)return 1;if(this._strip(),this.length>1)e=1;else{n&&(t=-t),r(t<=67108863,"Number is too big");var i=0|this.words[0];e=i===t?0:i<t?-1:1}return 0!==this.negative?0|-e:e},$.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return -1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},$.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return -1;for(var e=0,n=this.length-1;n>=0;n--){var r=0|this.words[n],i=0|t.words[n];if(r!==i){r<i?e=-1:r>i&&(e=1);break}}return e},$.prototype.gtn=function(t){return 1===this.cmpn(t)},$.prototype.gt=function(t){return 1===this.cmp(t)},$.prototype.gten=function(t){return this.cmpn(t)>=0},$.prototype.gte=function(t){return this.cmp(t)>=0},$.prototype.ltn=function(t){return -1===this.cmpn(t)},$.prototype.lt=function(t){return -1===this.cmp(t)},$.prototype.lten=function(t){return 0>=this.cmpn(t)},$.prototype.lte=function(t){return 0>=this.cmp(t)},$.prototype.eqn=function(t){return 0===this.cmpn(t)},$.prototype.eq=function(t){return 0===this.cmp(t)},$.red=function(t){return new M(t)},$.prototype.toRed=function(t){return r(!this.red,"Already a number in reduction context"),r(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},$.prototype.fromRed=function(){return r(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},$.prototype._forceRed=function(t){return this.red=t,this},$.prototype.forceRed=function(t){return r(!this.red,"Already a number in reduction context"),this._forceRed(t)},$.prototype.redAdd=function(t){return r(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},$.prototype.redIAdd=function(t){return r(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},$.prototype.redSub=function(t){return r(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},$.prototype.redISub=function(t){return r(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},$.prototype.redShl=function(t){return r(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},$.prototype.redMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},$.prototype.redIMul=function(t){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},$.prototype.redSqr=function(){return r(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},$.prototype.redISqr=function(){return r(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},$.prototype.redSqrt=function(){return r(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},$.prototype.redInvm=function(){return r(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},$.prototype.redNeg=function(){return r(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},$.prototype.redPow=function(t){return r(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function w(t,e){this.name=t,this.p=new $(e,16),this.n=this.p.bitLength(),this.k=new $(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function S(){w.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function k(){w.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function E(){w.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function C(){w.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function M(t){if("string"==typeof t){var e=$._prime(t);this.m=e.p,this.prime=e}else r(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function I(t){M.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new $(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}w.prototype._tmp=function(){var t=new $(null);return t.words=Array(Math.ceil(this.n/13)),t},w.prototype.ireduce=function(t){var e,n=t;do this.split(n,this.tmp),e=(n=(n=this.imulK(n)).iadd(this.tmp)).bitLength();while(e>this.n);var r=e<this.n?-1:n.ucmp(this.p);return 0===r?(n.words[0]=0,n.length=1):r>0?n.isub(this.p):void 0!==n.strip?n.strip():n._strip(),n},w.prototype.split=function(t,e){t.iushrn(this.n,0,e)},w.prototype.imulK=function(t){return t.imul(this.k)},i(S,w),S.prototype.split=function(t,e){for(var n=4194303,r=Math.min(t.length,9),i=0;i<r;i++)e.words[i]=t.words[i];if(e.length=r,t.length<=9)return t.words[0]=0,void(t.length=1);var $=t.words[9];for(e.words[e.length++]=$&n,i=10;i<t.length;i++){var o=0|t.words[i];t.words[i-10]=(o&n)<<4|$>>>22,$=o}$>>>=22,t.words[i-10]=$,0===$&&t.length>10?t.length-=10:t.length-=9},S.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,n=0;n<t.length;n++){var r=0|t.words[n];e+=977*r,t.words[n]=67108863&e,e=64*r+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(k,w),i(E,w),i(C,w),C.prototype.imulK=function(t){for(var e=0,n=0;n<t.length;n++){var r=19*(0|t.words[n])+e,i=67108863&r;r>>>=26,t.words[n]=i,e=r}return 0!==e&&(t.words[t.length++]=e),t},$._prime=function(t){var e;if(v[t])return v[t];if("k256"===t)e=new S;else if("p224"===t)e=new k;else if("p192"===t)e=new E;else{if("p25519"!==t)throw Error("Unknown prime "+t);e=new C}return v[t]=e,e},M.prototype._verify1=function(t){r(0===t.negative,"red works only with positives"),r(t.red,"red works only with red numbers")},M.prototype._verify2=function(t,e){r(0==(t.negative|e.negative),"red works only with positives"),r(t.red&&t.red===e.red,"red works only with red numbers")},M.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):(u(t,t.umod(this.m)._forceRed(this)),t)},M.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},M.prototype.add=function(t,e){this._verify2(t,e);var n=t.add(e);return n.cmp(this.m)>=0&&n.isub(this.m),n._forceRed(this)},M.prototype.iadd=function(t,e){this._verify2(t,e);var n=t.iadd(e);return n.cmp(this.m)>=0&&n.isub(this.m),n},M.prototype.sub=function(t,e){this._verify2(t,e);var n=t.sub(e);return 0>n.cmpn(0)&&n.iadd(this.m),n._forceRed(this)},M.prototype.isub=function(t,e){this._verify2(t,e);var n=t.isub(e);return 0>n.cmpn(0)&&n.iadd(this.m),n},M.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},M.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},M.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},M.prototype.isqr=function(t){return this.imul(t,t.clone())},M.prototype.sqr=function(t){return this.mul(t,t)},M.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(r(e%2==1),3===e){var n=this.m.add(new $(1)).iushrn(2);return this.pow(t,n)}for(var i=this.m.subn(1),o=0;!i.isZero()&&0===i.andln(1);)o++,i.iushrn(1);r(!i.isZero());var s=new $(1).toRed(this),x=s.redNeg(),a=this.m.subn(1).iushrn(1),u=this.m.bitLength();for(u=new $(2*u*u).toRed(this);0!==this.pow(u,a).cmp(x);)u.redIAdd(x);for(var c=this.pow(u,i),l=this.pow(t,i.addn(1).iushrn(1)),h=this.pow(t,i),f=o;0!==h.cmp(s);){for(var d=h,p=0;0!==d.cmp(s);p++)d=d.redSqr();r(p<f);var y=this.pow(c,new $(1).iushln(f-p-1));l=l.redMul(y),c=y.redSqr(),h=h.redMul(c),f=p}return l},M.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},M.prototype.pow=function(t,e){if(e.isZero())return new $(1).toRed(this);if(0===e.cmpn(1))return t.clone();var n=Array(16);n[0]=new $(1).toRed(this),n[1]=t;for(var r=2;r<n.length;r++)n[r]=this.mul(n[r-1],t);var i=n[0],o=0,s=0,x=e.bitLength()%26;for(0===x&&(x=26),r=e.length-1;r>=0;r--){for(var a=e.words[r],u=x-1;u>=0;u--){var c=a>>u&1;i!==n[0]&&(i=this.sqr(i)),0!==c||0!==o?(o<<=1,o|=c,(4==++s||0===r&&0===u)&&(i=this.mul(i,n[o]),s=0,o=0)):s=0}x=26}return i},M.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},M.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},$.mont=function(t){return new I(t)},i(I,M),I.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},I.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},I.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var n=t.imul(e),r=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=n.isub(r).iushrn(this.shift),$=i;return i.cmp(this.m)>=0?$=i.isub(this.m):0>i.cmpn(0)&&($=i.iadd(this.m)),$._forceRed(this)},I.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new $(0)._forceRed(this);var n=t.mul(e),r=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=n.isub(r).iushrn(this.shift),o=i;return i.cmp(this.m)>=0?o=i.isub(this.m):0>i.cmpn(0)&&(o=i.iadd(this.m)),o._forceRed(this)},I.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t=n.nmd(t),this)},8764(t,e,n){"use strict";let r=n(9742),i=n(645),$="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=x,e.SlowBuffer=function(t){return+t!=t&&(t=0),x.alloc(+t)},e.INSPECT_MAX_BYTES=50;let o=**********;function s(t){if(t>o)throw RangeError('The value "'+t+'" is invalid for option "size"');let e=new Uint8Array(t);return Object.setPrototypeOf(e,x.prototype),e}function x(t,e,n){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return a(t,e,n)}function a(t,e,n){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!x.isEncoding(e))throw TypeError("Unknown encoding: "+e);let n=0|d(t,e),r=s(n),i=r.write(t,e);return i!==n&&(r=r.slice(0,i)),r}(t,e);if(ArrayBuffer.isView(t))return function(t){if(Y(t,Uint8Array)){let e=new Uint8Array(t);return h(e.buffer,e.byteOffset,e.byteLength)}return l(t)}(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(Y(t,ArrayBuffer)||t&&Y(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(Y(t,SharedArrayBuffer)||t&&Y(t.buffer,SharedArrayBuffer)))return h(t,e,n);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');let r=t.valueOf&&t.valueOf();if(null!=r&&r!==t)return x.from(r,e,n);let i=function(t){if(x.isBuffer(t)){let e=0|f(t.length),n=s(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||K(t.length)?s(0):l(t):"Buffer"===t.type&&Array.isArray(t.data)?l(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return x.from(t[Symbol.toPrimitive]("string"),e,n);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return u(t),s(t<0?0:0|f(t))}function l(t){let e=t.length<0?0:0|f(t.length),n=s(e);for(let r=0;r<e;r+=1)n[r]=255&t[r];return n}function h(t,e,n){if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw RangeError('"length" is outside of buffer bounds');let r;return Object.setPrototypeOf(r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),x.prototype),r}function f(t){if(t>=o)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|t}function d(t,e){if(x.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||Y(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);let n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return q(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Z(t).length;default:if(i)return r?-1:q(t).length;e=(""+e).toLowerCase(),i=!0}}function p(t,e,n){let r=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===n||n>this.length)&&(n=this.length),n<=0)||(n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return A(this,e,n);case"utf8":case"utf-8":return E(this,e,n);case"ascii":return M(this,e,n);case"latin1":case"binary":return I(this,e,n);case"base64":return k(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,n);default:if(r)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function y(t,e,n){let r=t[e];t[e]=t[n],t[n]=r}function g(t,e,n,r,i){if(0===t.length)return -1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),K(n=+n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return -1;n=t.length-1}else if(n<0){if(!i)return -1;n=0}if("string"==typeof e&&(e=x.from(e,r)),x.isBuffer(e))return 0===e.length?-1:b(t,e,n,r,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):b(t,[e],n,r,i);throw TypeError("val must be string, number or Buffer")}function b(t,e,n,r,i){let $,o=1,s=t.length,x=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return -1;o=2,s/=2,x/=2,n/=2}function a(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){let u=-1;for($=n;$<s;$++)if(a(t,$)===a(e,-1===u?0:$-u)){if(-1===u&&(u=$),$-u+1===x)return u*o}else -1!==u&&($-=$-u),u=-1}else for(n+x>s&&(n=s-x),$=n;$>=0;$--){let c=!0;for(let l=0;l<x;l++)if(a(t,$+l)!==a(e,l)){c=!1;break}if(c)return $}return -1}function _(t,e,n,r){n=Number(n)||0;let i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;let $=e.length,o;for(r>$/2&&(r=$/2),o=0;o<r;++o){let s=parseInt(e.substr(2*o,2),16);if(K(s))break;t[n+o]=s}return o}function m(t,e,n,r){return G(q(e,t.length-n),t,n,r)}function v(t,e,n,r){return G(function(t){let e=[];for(let n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function w(t,e,n,r){return G(Z(e),t,n,r)}function S(t,e,n,r){return G(function(t,e){let n,r,i,$=[];for(let o=0;o<t.length&&!((e-=2)<0);++o)r=(n=t.charCodeAt(o))>>8,i=n%256,$.push(i),$.push(r);return $}(e,t.length-n),t,n,r)}function k(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function E(t,e,n){n=Math.min(t.length,n);let r=[],i=e;for(;i<n;){let $=t[i],o=null,s=$>239?4:$>223?3:$>191?2:1;if(i+s<=n){let x,a,u,c;switch(s){case 1:$<128&&(o=$);break;case 2:128==(192&(x=t[i+1]))&&(c=(31&$)<<6|63&x)>127&&(o=c);break;case 3:x=t[i+1],a=t[i+2],128==(192&x)&&128==(192&a)&&(c=(15&$)<<12|(63&x)<<6|63&a)>2047&&(c<55296||c>57343)&&(o=c);break;case 4:x=t[i+1],a=t[i+2],u=t[i+3],128==(192&x)&&128==(192&a)&&128==(192&u)&&(c=(15&$)<<18|(63&x)<<12|(63&a)<<6|63&u)>65535&&c<1114112&&(o=c)}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}return function(t){let e=t.length;if(e<=C)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=C));return n}(r)}e.kMaxLength=o,x.TYPED_ARRAY_SUPPORT=function(){try{let t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(n){return!1}}(),x.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(x.prototype,"parent",{enumerable:!0,get:function(){if(x.isBuffer(this))return this.buffer}}),Object.defineProperty(x.prototype,"offset",{enumerable:!0,get:function(){if(x.isBuffer(this))return this.byteOffset}}),x.poolSize=8192,x.from=function(t,e,n){return a(t,e,n)},Object.setPrototypeOf(x.prototype,Uint8Array.prototype),Object.setPrototypeOf(x,Uint8Array),x.alloc=function(t,e,n){var r,i,$;return r=t,i=e,$=n,u(r),r<=0?s(r):void 0!==i?"string"==typeof $?s(r).fill(i,$):s(r).fill(i):s(r)},x.allocUnsafe=function(t){return c(t)},x.allocUnsafeSlow=function(t){return c(t)},x.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==x.prototype},x.compare=function(t,e){if(Y(t,Uint8Array)&&(t=x.from(t,t.offset,t.byteLength)),Y(e,Uint8Array)&&(e=x.from(e,e.offset,e.byteLength)),!x.isBuffer(t)||!x.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let i=0,$=Math.min(n,r);i<$;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},x.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},x.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return x.alloc(0);let n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;let r=x.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){let $=t[n];if(Y($,Uint8Array))i+$.length>r.length?(x.isBuffer($)||($=x.from($)),$.copy(r,i)):Uint8Array.prototype.set.call(r,$,i);else{if(!x.isBuffer($))throw TypeError('"list" argument must be an Array of Buffers');$.copy(r,i)}i+=$.length}return r},x.byteLength=d,x.prototype._isBuffer=!0,x.prototype.swap16=function(){let t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)y(this,e,e+1);return this},x.prototype.swap32=function(){let t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},x.prototype.swap64=function(){let t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},x.prototype.toString=function(){let t=this.length;return 0===t?"":0===arguments.length?E(this,0,t):p.apply(this,arguments)},x.prototype.toLocaleString=x.prototype.toString,x.prototype.equals=function(t){if(!x.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===x.compare(this,t)},x.prototype.inspect=function(){let t="",n=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},$&&(x.prototype[$]=x.prototype.inspect),x.prototype.compare=function(t,e,n,r,i){if(Y(t,Uint8Array)&&(t=x.from(t,t.offset,t.byteLength)),!x.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return -1;if(e>=n)return 1;if(this===t)return 0;let $=(i>>>=0)-(r>>>=0),o=(n>>>=0)-(e>>>=0),s=Math.min($,o),a=this.slice(r,i),u=t.slice(e,n);for(let c=0;c<s;++c)if(a[c]!==u[c]){$=a[c],o=u[c];break}return $<o?-1:o<$?1:0},x.prototype.includes=function(t,e,n){return -1!==this.indexOf(t,e,n)},x.prototype.indexOf=function(t,e,n){return g(this,t,e,n,!0)},x.prototype.lastIndexOf=function(t,e,n){return g(this,t,e,n,!1)},x.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}let i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let $=!1;for(;;)switch(r){case"hex":return _(this,t,e,n);case"utf8":case"utf-8":return m(this,t,e,n);case"ascii":case"latin1":case"binary":return v(this,t,e,n);case"base64":return w(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,e,n);default:if($)throw TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),$=!0}},x.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};let C=4096;function M(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function I(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function A(t,e,n){let r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let i="";for(let $=e;$<n;++$)i+=Q[t[$]];return i}function T(t,e,n){let r=t.slice(e,n),i="";for(let $=0;$<r.length-1;$+=2)i+=String.fromCharCode(r[$]+256*r[$+1]);return i}function R(t,e,n){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>n)throw RangeError("Trying to access beyond buffer length")}function L(t,e,n,r,i,$){if(!x.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<$)throw RangeError('"value" argument is out of bounds');if(n+r>t.length)throw RangeError("Index out of range")}function N(t,e,n,r,i){W(e,r,i,t,n,7);let $=Number(e&BigInt(4294967295));t[n++]=$,$>>=8,t[n++]=$,$>>=8,t[n++]=$,$>>=8,t[n++]=$;let o=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o,n}function P(t,e,n,r,i){W(e,r,i,t,n,7);let $=Number(e&BigInt(4294967295));t[n+7]=$,$>>=8,t[n+6]=$,$>>=8,t[n+5]=$,$>>=8,t[n+4]=$;let o=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=o,o>>=8,t[n+2]=o,o>>=8,t[n+1]=o,o>>=8,t[n]=o,n+8}function D(t,e,n,r,i,$){if(n+r>t.length||n<0)throw RangeError("Index out of range")}function O(t,e,n,r,$){return e=+e,n>>>=0,$||D(t,0,n,4),i.write(t,e,n,r,23,4),n+4}function B(t,e,n,r,$){return e=+e,n>>>=0,$||D(t,0,n,8),i.write(t,e,n,r,52,8),n+8}x.prototype.slice=function(t,e){let n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);let r=this.subarray(t,e);return Object.setPrototypeOf(r,x.prototype),r},x.prototype.readUintLE=x.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||R(t,e,this.length);let r=this[t],i=1,$=0;for(;++$<e&&(i*=256);)r+=this[t+$]*i;return r},x.prototype.readUintBE=x.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||R(t,e,this.length);let r=this[t+--e],i=1;for(;e>0&&(i*=256);)r+=this[t+--e]*i;return r},x.prototype.readUint8=x.prototype.readUInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),this[t]},x.prototype.readUint16LE=x.prototype.readUInt16LE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]|this[t+1]<<8},x.prototype.readUint16BE=x.prototype.readUInt16BE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]<<8|this[t+1]},x.prototype.readUint32LE=x.prototype.readUInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},x.prototype.readUint32BE=x.prototype.readUInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},x.prototype.readBigUInt64LE=J(function(t){z(t>>>=0,"offset");let e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);let r=e+256*this[++t]+65536*this[++t]+16777216*this[++t],i=this[++t]+256*this[++t]+65536*this[++t]+16777216*n;return BigInt(r)+(BigInt(i)<<BigInt(32))}),x.prototype.readBigUInt64BE=J(function(t){z(t>>>=0,"offset");let e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);let r=16777216*e+65536*this[++t]+256*this[++t]+this[++t],i=16777216*this[++t]+65536*this[++t]+256*this[++t]+n;return(BigInt(r)<<BigInt(32))+BigInt(i)}),x.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||R(t,e,this.length);let r=this[t],i=1,$=0;for(;++$<e&&(i*=256);)r+=this[t+$]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*e)),r},x.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||R(t,e,this.length);let r=e,i=1,$=this[t+--r];for(;r>0&&(i*=256);)$+=this[t+--r]*i;return $>=(i*=128)&&($-=Math.pow(2,8*e)),$},x.prototype.readInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},x.prototype.readInt16LE=function(t,e){t>>>=0,e||R(t,2,this.length);let n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},x.prototype.readInt16BE=function(t,e){t>>>=0,e||R(t,2,this.length);let n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},x.prototype.readInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},x.prototype.readInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},x.prototype.readBigInt64LE=J(function(t){z(t>>>=0,"offset");let e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);let r=this[t+4]+256*this[t+5]+65536*this[t+6]+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+16777216*this[++t])}),x.prototype.readBigInt64BE=J(function(t){z(t>>>=0,"offset");let e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);let r=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(16777216*this[++t]+65536*this[++t]+256*this[++t]+n)}),x.prototype.readFloatLE=function(t,e){return t>>>=0,e||R(t,4,this.length),i.read(this,t,!0,23,4)},x.prototype.readFloatBE=function(t,e){return t>>>=0,e||R(t,4,this.length),i.read(this,t,!1,23,4)},x.prototype.readDoubleLE=function(t,e){return t>>>=0,e||R(t,8,this.length),i.read(this,t,!0,52,8)},x.prototype.readDoubleBE=function(t,e){return t>>>=0,e||R(t,8,this.length),i.read(this,t,!1,52,8)},x.prototype.writeUintLE=x.prototype.writeUIntLE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||L(this,t,e,n,Math.pow(2,8*n)-1,0);let i=1,$=0;for(this[e]=255&t;++$<n&&(i*=256);)this[e+$]=t/i&255;return e+n},x.prototype.writeUintBE=x.prototype.writeUIntBE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||L(this,t,e,n,Math.pow(2,8*n)-1,0);let i=n-1,$=1;for(this[e+i]=255&t;--i>=0&&($*=256);)this[e+i]=t/$&255;return e+n},x.prototype.writeUint8=x.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,1,255,0),this[e]=255&t,e+1},x.prototype.writeUint16LE=x.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},x.prototype.writeUint16BE=x.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},x.prototype.writeUint32LE=x.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},x.prototype.writeUint32BE=x.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},x.prototype.writeBigUInt64LE=J(function(t,e=0){return N(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),x.prototype.writeBigUInt64BE=J(function(t,e=0){return P(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),x.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){let i=Math.pow(2,8*n-1);L(this,t,e,n,i-1,-i)}let $=0,o=1,s=0;for(this[e]=255&t;++$<n&&(o*=256);)t<0&&0===s&&0!==this[e+$-1]&&(s=1),this[e+$]=(t/o>>0)-s&255;return e+n},x.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){let i=Math.pow(2,8*n-1);L(this,t,e,n,i-1,-i)}let $=n-1,o=1,s=0;for(this[e+$]=255&t;--$>=0&&(o*=256);)t<0&&0===s&&0!==this[e+$+1]&&(s=1),this[e+$]=(t/o>>0)-s&255;return e+n},x.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},x.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},x.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},x.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},x.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||L(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},x.prototype.writeBigInt64LE=J(function(t,e=0){return N(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),x.prototype.writeBigInt64BE=J(function(t,e=0){return P(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),x.prototype.writeFloatLE=function(t,e,n){return O(this,t,e,!0,n)},x.prototype.writeFloatBE=function(t,e,n){return O(this,t,e,!1,n)},x.prototype.writeDoubleLE=function(t,e,n){return B(this,t,e,!0,n)},x.prototype.writeDoubleBE=function(t,e,n){return B(this,t,e,!1,n)},x.prototype.copy=function(t,e,n,r){if(!x.isBuffer(t))throw TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw RangeError("Index out of range");if(r<0)throw RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);let i=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),i},x.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw TypeError("encoding must be a string");if("string"==typeof r&&!x.isEncoding(r))throw TypeError("Unknown encoding: "+r);if(1===t.length){let i=t.charCodeAt(0);("utf8"===r&&i<128||"latin1"===r)&&(t=i)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw RangeError("Out of range index");if(n<=e)return this;let $;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for($=e;$<n;++$)this[$]=t;else{let o=x.isBuffer(t)?t:x.from(t,r),s=o.length;if(0===s)throw TypeError('The value "'+t+'" is invalid for argument "value"');for($=0;$<n-e;++$)this[$+e]=o[$%s]}return this};let j={};function U(t,e,n){j[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=this.name+" ["+t+"]",this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return this.name+" ["+t+"]: "+this.message}}}function F(t){let e="",n=t.length,r="-"===t[0]?1:0;for(;n>=r+4;n-=3)e="_"+t.slice(n-3,n)+e;return""+t.slice(0,n)+e}function W(t,e,n,r,i,$){var o,s,x;if(t>n||t<e){let a="bigint"==typeof e?"n":"",u;throw u=$>3?0===e||e===BigInt(0)?">= 0"+a+" and < 2"+a+" ** "+8*($+1)+a:">= -(2"+a+" ** "+(8*($+1)-1)+a+") and < 2 ** "+(8*($+1)-1)+a:">= "+e+a+" and <= "+n+a,new j.ERR_OUT_OF_RANGE("value",u,t)}o=r,s=i,x=$,z(s,"offset"),void 0!==o[s]&&void 0!==o[s+x]||H(s,o.length-(x+1))}function z(t,e){if("number"!=typeof t)throw new j.ERR_INVALID_ARG_TYPE(e,"number",t)}function H(t,e,n){if(Math.floor(t)!==t)throw z(t,n),new j.ERR_OUT_OF_RANGE(n||"offset","an integer",t);if(e<0)throw new j.ERR_BUFFER_OUT_OF_BOUNDS;throw new j.ERR_OUT_OF_RANGE(n||"offset",">= "+(n?1:0)+" and <= "+e,t)}U("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?t+" is outside of buffer bounds":"Attempt to access memory outside buffer bounds"},RangeError),U("ERR_INVALID_ARG_TYPE",function(t,e){return'The "'+t+'" argument must be of type number. Received type '+typeof e},TypeError),U("ERR_OUT_OF_RANGE",function(t,e,n){let r='The value of "'+t+'" is out of range.',i=n;return Number.isInteger(n)&&Math.abs(n)>4294967296?i=F(String(n)):"bigint"==typeof n&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=F(i)),i+="n"),r+=" It must be "+e+". Received "+i},RangeError);let V=/[^+/0-9A-Za-z-_]/g;function q(t,e){let n;e=e||1/0;let r=t.length,i=null,$=[];for(let o=0;o<r;++o){if((n=t.charCodeAt(o))>55295&&n<57344){if(!i){if(n>56319||o+1===r){(e-=3)>-1&&$.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&$.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&$.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;$.push(n)}else if(n<2048){if((e-=2)<0)break;$.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;$.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw Error("Invalid code point");if((e-=4)<0)break;$.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return $}function Z(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(V,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function G(t,e,n,r){let i;for(i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function Y(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function K(t){return t!=t}let Q=function(){let t="0123456789abcdef",e=Array(256);for(let n=0;n<16;++n){let r=16*n;for(let i=0;i<16;++i)e[r+i]=t[n]+t[i]}return e}();function J(t){return"undefined"==typeof BigInt?X:t}function X(){throw Error("BigInt not supported")}},1924(t,e,n){"use strict";var r=n(210),i=n(5559),$=i(r("String.prototype.indexOf"));t.exports=function(t,e){var n=r(t,!!e);return"function"==typeof n&&$(t,".prototype.")>-1?i(n):n}},5559(t,e,n){"use strict";var r=n(8612),i=n(210),$=i("%Function.prototype.apply%"),o=i("%Function.prototype.call%"),s=i("%Reflect.apply%",!0)||r.call(o,$),x=i("%Object.getOwnPropertyDescriptor%",!0),a=i("%Object.defineProperty%",!0),u=i("%Math.max%");if(a)try{a({},"a",{value:1})}catch(c){a=null}t.exports=function(t){var e=s(r,o,arguments);return x&&a&&x(e,"length").configurable&&a(e,"length",{value:1+u(0,t.length-(arguments.length-1))}),e};var l=function(){return s(r,$,arguments)};a?a(t.exports,"apply",{value:l}):t.exports.apply=l},6010(t,e,n){"use strict";function r(t){var e,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=r(t[e]))&&(i&&(i+=" "),i+=n);else for(e in t)t[e]&&(i&&(i+=" "),i+=e)}return i}function i(){for(var t,e,n=0,i="";n<arguments.length;)(t=arguments[n++])&&(e=r(t))&&(i&&(i+=" "),i+=e);return i}n.r(e),n.d(e,{default:()=>i})},2696(t,e,n){n(5682),n(2352);let r=n(7253),i=(t,e)=>t+e,$=["sync","latest"];function o(t){return Number.parseInt(t,16)}t.exports=class extends r{constructor(t={}){super(),this._blockResetDuration=t.blockResetDuration||2e4,this._blockResetTimeout,this._currentBlock=null,this._isRunning=!1,this._onNewListener=this._onNewListener.bind(this),this._onRemoveListener=this._onRemoveListener.bind(this),this._resetCurrentBlock=this._resetCurrentBlock.bind(this),this._setupInternalEvents()}isRunning(){return this._isRunning}getCurrentBlock(){return this._currentBlock}async getLatestBlock(){return this._currentBlock?this._currentBlock:await new Promise(t=>this.once("latest",t))}removeAllListeners(t){t?super.removeAllListeners(t):super.removeAllListeners(),this._setupInternalEvents(),this._onRemoveListener()}_start(){}_end(){}_setupInternalEvents(){this.removeListener("newListener",this._onNewListener),this.removeListener("removeListener",this._onRemoveListener),this.on("newListener",this._onNewListener),this.on("removeListener",this._onRemoveListener)}_onNewListener(t,e){$.includes(t)&&this._maybeStart()}_onRemoveListener(t,e){this._getBlockTrackerEventCount()>0||this._maybeEnd()}_maybeStart(){this._isRunning||(this._isRunning=!0,this._cancelBlockResetTimeout(),this._start())}_maybeEnd(){this._isRunning&&(this._isRunning=!1,this._setupBlockResetTimeout(),this._end())}_getBlockTrackerEventCount(){return $.map(t=>this.listenerCount(t)).reduce(i)}_newPotentialLatest(t){let e=this._currentBlock;e&&o(t)<=o(e)||this._setCurrentBlock(t)}_setCurrentBlock(t){let e=this._currentBlock;this._currentBlock=t,this.emit("latest",t),this.emit("sync",{oldBlock:e,newBlock:t})}_setupBlockResetTimeout(){this._cancelBlockResetTimeout(),this._blockResetTimeout=setTimeout(this._resetCurrentBlock,this._blockResetDuration),this._blockResetTimeout.unref&&this._blockResetTimeout.unref()}_cancelBlockResetTimeout(){clearTimeout(this._blockResetTimeout)}_resetCurrentBlock(){this._currentBlock=null}}},5012(t,e,n){let r=n(2352),i=n(2696);function $(t,e){return new Promise(n=>{let r=setTimeout(n,t);r.unref&&e&&r.unref()})}t.exports=class extends i{constructor(t={}){if(!t.provider)throw Error("PollingBlockTracker - no provider specified.");let e=t.pollingInterval||2e4,n=t.retryTimeout||e/10,r=void 0===t.keepEventLoopActive||t.keepEventLoopActive,i=t.setSkipCacheFlag||!1;super(Object.assign({blockResetDuration:e},t)),this._provider=t.provider,this._pollingInterval=e,this._retryTimeout=n,this._keepEventLoopActive=r,this._setSkipCacheFlag=i}async checkForLatestBlock(){return await this._updateLatestBlock(),await this.getLatestBlock()}_start(){this._performSync().catch(t=>this.emit("error",t))}async _performSync(){for(;this._isRunning;)try{await this._updateLatestBlock(),await $(this._pollingInterval,!this._keepEventLoopActive)}catch(t){let e=Error("PollingBlockTracker - encountered an error while attempting to update latest block:\n"+t.stack);try{this.emit("error",e)}catch(n){console.error(e)}await $(this._retryTimeout,!this._keepEventLoopActive)}}async _updateLatestBlock(){let t=await this._fetchLatestBlock();this._newPotentialLatest(t)}async _fetchLatestBlock(){let t={jsonrpc:"2.0",id:1,method:"eth_blockNumber",params:[]};this._setSkipCacheFlag&&(t.skipCache=!0);let e=await r(e=>this._provider.sendAsync(t,e))();if(e.error)throw Error("PollingBlockTracker - encountered error fetching block:\n"+e.error);return e.result}}},3256(t,e,n){let r=n(6622);t.exports=class extends r{constructor(){super(),this.allResults=[]}async update(){throw Error("BaseFilterWithHistory - no update method specified")}addResults(t){this.allResults=this.allResults.concat(t),super.addResults(t)}addInitialResults(t){this.allResults=this.allResults.concat(t),super.addInitialResults(t)}getAllResults(){return this.allResults}}},6622(t,e,n){let r=n(9394).default;t.exports=class extends r{constructor(){super(),this.updates=[]}async initialize(){}async update(){throw Error("BaseFilter - no update method specified")}addResults(t){this.updates=this.updates.concat(t),t.forEach(t=>this.emit("update",t))}addInitialResults(t){}getChangesAndClear(){let t=this.updates;return this.updates=[],t}}},2785(t,e,n){let r=n(6622),i=n(207),{incrementHexInt:$}=n(8112);t.exports=class extends r{constructor({provider:t,params:e}){super(),this.type="block",this.provider=t}async update({oldBlock:t,newBlock:e}){let n=e,r=$(t),o=(await i({provider:this.provider,fromBlock:r,toBlock:n})).map(t=>t.hash);this.addResults(o)}}},207(t){function e(t){return null==t?t:Number.parseInt(t,16)}function n(t){return null==t?t:"0x"+t.toString(16)}t.exports=async function({provider:t,fromBlock:r,toBlock:i}){r||(r=i);let $=e(r),o=e(i),s=Array(o-$+1).fill().map((t,e)=>$+e).map(n);return await Promise.all(s.map(e=>{var n,r,i=0;return n=t,r=[e,!1],new Promise((t,e)=>{n.sendAsync({id:1,jsonrpc:"2.0",method:"eth_getBlockByNumber",params:r},(n,r)=>{if(n)return e(n);t(r.result)})})}))}},8112(t){function e(t){return t.sort((t,e)=>"latest"===t||"earliest"===e?1:"latest"===e||"earliest"===t?-1:n(t)-n(e))}function n(t){return null==t?t:Number.parseInt(t,16)}function r(t){if(null==t)return t;let e=t.toString(16);return e.length%2&&(e="0"+e),"0x"+e}function i(){return Math.floor(16*Math.random()).toString(16)}t.exports={minBlockRef:function(...t){return e(t)[0]},maxBlockRef:function(...t){let n=e(t);return n[n.length-1]},sortBlockRefs:e,bnToHex:function(t){return"0x"+t.toString(16)},blockRefIsNumber:function(t){return t&&!["earliest","latest","pending"].includes(t)},hexToInt:n,incrementHexInt:function(t){return null==t?t:r(n(t)+1)},intToHex:r,unsafeRandomBytes:function(t){let e="0x";for(let n=0;n<t;n++)e+=i(),e+=i();return e}}},8406(t,e,n){let r=n(8125).WU,{createAsyncMiddleware:i}=n(8625),$=n(7688),o=n(1663),s=n(2785),x=n(5792),{intToHex:a,hexToInt:u}=n(8112);function c(t){return l(async(...e)=>{let n=await t(...e);return a(n.id)})}function l(t){return i(async(e,n)=>{let r=await t.apply(null,e.params);n.result=r})}function h(t,e){let n=[];for(let r in t)n.push(t[r]);return n}t.exports=function({blockTracker:t,provider:e}){let n=0,i={},f=new r,d=function({mutex:t}){return e=>async(n,r,i,$)=>{(await t.acquire())(),e(n,r,i,$)}}({mutex:f}),p=$({eth_newFilter:d(c(g)),eth_newBlockFilter:d(c(b)),eth_newPendingTransactionFilter:d(c(_)),eth_uninstallFilter:d(l(w)),eth_getFilterChanges:d(l(m)),eth_getFilterLogs:d(l(v))}),y=async({oldBlock:t,newBlock:e})=>{if(0===i.length)return;let n=await f.acquire();try{await Promise.all(h(i).map(async n=>{try{await n.update({oldBlock:t,newBlock:e})}catch(r){console.error(r)}}))}catch(r){console.error(r)}n()};return p.newLogFilter=g,p.newBlockFilter=b,p.newPendingTransactionFilter=_,p.uninstallFilter=w,p.getFilterChanges=m,p.getFilterLogs=v,p.destroy=()=>{!async function(){let t=h(i).length;i={},k({prevFilterCount:t,newFilterCount:0})}()},p;async function g(t){let n=new o({provider:e,params:t});return await S(n),n}async function b(){let t=new s({provider:e});return await S(t),t}async function _(){let t=new x({provider:e});return await S(t),t}async function m(t){let e=u(t),n=i[e];if(!n)throw Error('No filter for index "'+e+'"');return n.getChangesAndClear()}async function v(t){let e=u(t),n=i[e];if(!n)throw Error('No filter for index "'+e+'"');return"log"===n.type?n.getAllResults():[]}async function w(t){let e=u(t),n=i[e],r=Boolean(n);return r&&await async function(t){let e=h(i).length;delete i[t],k({prevFilterCount:e,newFilterCount:h(i).length})}(e),r}async function S(e){let r=h(i).length,$=await t.getLatestBlock();return await e.initialize({currentBlock:$}),i[++n]=e,e.id=n,e.idHex=a(n),k({prevFilterCount:r,newFilterCount:h(i).length}),n}function k({prevFilterCount:e,newFilterCount:n}){0===e&&n>0?t.on("sync",y):e>0&&0===n&&t.removeListener("sync",y)}}},1663(t,e,n){let r=n(5682),i=n(6417),$=n(3256),{bnToHex:o,hexToInt:s,incrementHexInt:x,minBlockRef:a,blockRefIsNumber:u}=n(8112);t.exports=class extends ${constructor({provider:t,params:e}){super(),this.type="log",this.ethQuery=new r(t),this.params=Object.assign({fromBlock:"latest",toBlock:"latest",address:void 0,topics:[]},e),this.params.address&&(Array.isArray(this.params.address)||(this.params.address=[this.params.address]),this.params.address=this.params.address.map(t=>t.toLowerCase()))}async initialize({currentBlock:t}){let e=this.params.fromBlock;["latest","pending"].includes(e)&&(e=t),"earliest"===e&&(e="0x0"),this.params.fromBlock=e;let n=a(this.params.toBlock,t),r=Object.assign({},this.params,{toBlock:n}),i=await this._fetchLogs(r);this.addInitialResults(i)}async update({oldBlock:t,newBlock:e}){let n=e,r;r=t?x(t):e;let i=Object.assign({},this.params,{fromBlock:r,toBlock:n}),$=(await this._fetchLogs(i)).filter(t=>this.matchLog(t));this.addResults($)}async _fetchLogs(t){return await i(e=>this.ethQuery.getLogs(t,e))()}matchLog(t){if(s(this.params.fromBlock)>=s(t.blockNumber)||u(this.params.toBlock)&&s(this.params.toBlock)<=s(t.blockNumber))return!1;let e=t.address&&t.address.toLowerCase();return!(this.params.address&&e&&!this.params.address.includes(e))&&this.params.topics.every((e,n)=>{let r=t.topics[n];if(!r)return!1;r=r.toLowerCase();let i=Array.isArray(e)?e:[e];return!!i.includes(null)||(i=i.map(t=>t.toLowerCase())).includes(r)})}}},6417(t){"use strict";let e=(t,e,n,r)=>function(...i){return new e.promiseModule(($,o)=>{e.multiArgs?i.push((...t)=>{e.errorFirst?t[0]?o(t):(t.shift(),$(t)):$(t)}):e.errorFirst?i.push((t,e)=>{t?o(t):$(e)}):i.push($);let s=this===n?r:this;Reflect.apply(t,s,i)})},n=new WeakMap;t.exports=(t,r)=>{r={exclude:[/.+(?:Sync|Stream)$/],errorFirst:!0,promiseModule:Promise,...r};let i=typeof t;if(null===t||"object"!==i&&"function"!==i)throw TypeError("Expected `input` to be a `Function` or `Object`, got `"+(null===t?"null":i)+"`");let $=new WeakMap,o=new Proxy(t,{apply(t,n,i){let s=$.get(t);if(s)return Reflect.apply(s,n,i);let x=r.excludeMain?t:e(t,r,o,t);return $.set(t,x),Reflect.apply(x,n,i)},get(t,i){let s=t[i];if(!((t,e)=>{let i=n.get(t);if(i||(i={},n.set(t,i)),e in i)return i[e];let $=t=>"string"==typeof t||"symbol"==typeof e?e===t:t.test(e),o=Reflect.getOwnPropertyDescriptor(t,e),s=void 0===o||o.writable||o.configurable,x=(r.include?r.include.some($):!r.exclude.some($))&&s;return i[e]=x,x})(t,i)||s===Function.prototype[i])return s;let x=$.get(s);if(x)return x;if("function"==typeof s){let a=e(s,r,o,t);return $.set(s,a),a}return s}});return o}},8961(t,e,n){let r=n(9394).default,i=n(7688),{createAsyncMiddleware:$}=n(8625),o=n(8406),{unsafeRandomBytes:s,incrementHexInt:x}=n(8112),a=n(207);function u(t){return{hash:t.hash,parentHash:t.parentHash,sha3Uncles:t.sha3Uncles,miner:t.miner,stateRoot:t.stateRoot,transactionsRoot:t.transactionsRoot,receiptsRoot:t.receiptsRoot,logsBloom:t.logsBloom,difficulty:t.difficulty,number:t.number,gasLimit:t.gasLimit,gasUsed:t.gasUsed,nonce:t.nonce,mixHash:t.mixHash,timestamp:t.timestamp,extraData:t.extraData}}t.exports=function({blockTracker:t,provider:e}){let n={},c=o({blockTracker:t,provider:e}),l=!1,h=new r,f=i({eth_subscribe:$(async function(r,i){if(l)throw Error("SubscriptionManager - attempting to use after destroying");let $=r.params[0],o=s(16),h;switch($){case"newHeads":h=function({subId:n}){let r={type:$,async destroy(){t.removeListener("sync",r.update)},async update({oldBlock:t,newBlock:r}){let i=r,$=x(t);(await a({provider:e,fromBlock:$,toBlock:i})).map(u).forEach(t=>{d(n,t)})}};return t.on("sync",r.update),r}({subId:o});break;case"logs":let f=r.params[1];h=function({subId:t,filter:e}){return e.on("update",e=>d(t,e)),{type:$,destroy:async()=>await c.uninstallFilter(e.idHex)}}({subId:o,filter:await c.newLogFilter(f)});break;default:throw Error('SubscriptionManager - unsupported subscription type "'+$+'"')}return n[o]=h,void(i.result=o)}),eth_unsubscribe:$(async function(t,e){if(l)throw Error("SubscriptionManager - attempting to use after destroying");let r=t.params[0],i=n[r];i?(delete n[r],await i.destroy(),e.result=!0):e.result=!1})});return f.destroy=function(){for(let t in h.removeAllListeners(),n)n[t].destroy(),delete n[t];l=!0},{events:h,middleware:f};function d(t,e){h.emit("notification",{jsonrpc:"2.0",method:"eth_subscription",params:{subscription:t,result:e}})}}},5792(t,e,n){let r=n(6622),i=n(207),{incrementHexInt:$}=n(8112);t.exports=class extends r{constructor({provider:t}){super(),this.type="tx",this.provider=t}async update({oldBlock:t}){let e=t,n=$(t),r=await i({provider:this.provider,fromBlock:n,toBlock:e}),o=[];for(let s of r)o.push(...s.transactions);this.addResults(o)}}},9721(t){t.exports=function(t){return(e,n,r,i)=>{let $=t[e.method];return void 0===$?r():"function"==typeof $?$(e,n,r,i):(n.result=$,i())}}},7688(t,e,n){t.exports=n(9721)},5682(t,e,n){let r=n(7529),i=n(3420)();function $(t){this.currentProvider=t}function o(t){return function(){let e=this;var n=[].slice.call(arguments),r=n.pop();e.sendAsync({method:t,params:n},r)}}function s(t,e){return function(){let n=this;var r=[].slice.call(arguments),i=r.pop();r.length<t&&r.push("latest"),n.sendAsync({method:e,params:r},i)}}t.exports=$,$.prototype.getBalance=s(2,"eth_getBalance"),$.prototype.getCode=s(2,"eth_getCode"),$.prototype.getTransactionCount=s(2,"eth_getTransactionCount"),$.prototype.getStorageAt=s(3,"eth_getStorageAt"),$.prototype.call=s(2,"eth_call"),$.prototype.protocolVersion=o("eth_protocolVersion"),$.prototype.syncing=o("eth_syncing"),$.prototype.coinbase=o("eth_coinbase"),$.prototype.mining=o("eth_mining"),$.prototype.hashrate=o("eth_hashrate"),$.prototype.gasPrice=o("eth_gasPrice"),$.prototype.accounts=o("eth_accounts"),$.prototype.blockNumber=o("eth_blockNumber"),$.prototype.getBlockTransactionCountByHash=o("eth_getBlockTransactionCountByHash"),$.prototype.getBlockTransactionCountByNumber=o("eth_getBlockTransactionCountByNumber"),$.prototype.getUncleCountByBlockHash=o("eth_getUncleCountByBlockHash"),$.prototype.getUncleCountByBlockNumber=o("eth_getUncleCountByBlockNumber"),$.prototype.sign=o("eth_sign"),$.prototype.sendTransaction=o("eth_sendTransaction"),$.prototype.sendRawTransaction=o("eth_sendRawTransaction"),$.prototype.estimateGas=o("eth_estimateGas"),$.prototype.getBlockByHash=o("eth_getBlockByHash"),$.prototype.getBlockByNumber=o("eth_getBlockByNumber"),$.prototype.getTransactionByHash=o("eth_getTransactionByHash"),$.prototype.getTransactionByBlockHashAndIndex=o("eth_getTransactionByBlockHashAndIndex"),$.prototype.getTransactionByBlockNumberAndIndex=o("eth_getTransactionByBlockNumberAndIndex"),$.prototype.getTransactionReceipt=o("eth_getTransactionReceipt"),$.prototype.getUncleByBlockHashAndIndex=o("eth_getUncleByBlockHashAndIndex"),$.prototype.getUncleByBlockNumberAndIndex=o("eth_getUncleByBlockNumberAndIndex"),$.prototype.getCompilers=o("eth_getCompilers"),$.prototype.compileLLL=o("eth_compileLLL"),$.prototype.compileSolidity=o("eth_compileSolidity"),$.prototype.compileSerpent=o("eth_compileSerpent"),$.prototype.newFilter=o("eth_newFilter"),$.prototype.newBlockFilter=o("eth_newBlockFilter"),$.prototype.newPendingTransactionFilter=o("eth_newPendingTransactionFilter"),$.prototype.uninstallFilter=o("eth_uninstallFilter"),$.prototype.getFilterChanges=o("eth_getFilterChanges"),$.prototype.getFilterLogs=o("eth_getFilterLogs"),$.prototype.getLogs=o("eth_getLogs"),$.prototype.getWork=o("eth_getWork"),$.prototype.submitWork=o("eth_submitWork"),$.prototype.submitHashrate=o("eth_submitHashrate"),$.prototype.sendAsync=function(t,e){var n;this.currentProvider.sendAsync((n=t,r({id:i(),jsonrpc:"2.0",params:[]},n)),function(t,n){if(!t&&n.error&&(t=Error("EthQuery - RPC Error - "+n.error.message)),t)return e(t);e(null,n.result)})}},2294(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EthereumProviderError=e.EthereumRpcError=void 0;let r=n(4445);class i extends Error{constructor(t,e,n){if(!Number.isInteger(t))throw Error('"code" must be an integer.');if(!e||"string"!=typeof e)throw Error('"message" must be a nonempty string.');super(e),this.code=t,void 0!==n&&(this.data=n)}serialize(){let t={code:this.code,message:this.message};return void 0!==this.data&&(t.data=this.data),this.stack&&(t.stack=this.stack),t}toString(){return r.default(this.serialize(),$,2)}}function $(t,e){if("[Circular]"!==e)return e}e.EthereumRpcError=i,e.EthereumProviderError=class extends i{constructor(t,e,n){var r;if(!Number.isInteger(r=t)||!(r>=1e3)||!(r<=4999))throw Error('"code" must be an integer such that: 1000 <= code <= 4999');super(t,e,n)}}},2662(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.errorValues=e.errorCodes=void 0,e.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},e.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}}},8797(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ethErrors=void 0;let r=n(2294),i=n(8753),$=n(2662);function o(t,e){let[n,$]=x(e);return new r.EthereumRpcError(t,n||i.getMessageFromCode(t),$)}function s(t,e){let[n,$]=x(e);return new r.EthereumProviderError(t,n||i.getMessageFromCode(t),$)}function x(t){if(t){if("string"==typeof t)return[t];if("object"==typeof t&&!Array.isArray(t)){let{message:e,data:n}=t;if(e&&"string"!=typeof e)throw Error("Must specify string message.");return[e||void 0,n]}}return[]}e.ethErrors={rpc:{parse:t=>o($.errorCodes.rpc.parse,t),invalidRequest:t=>o($.errorCodes.rpc.invalidRequest,t),invalidParams:t=>o($.errorCodes.rpc.invalidParams,t),methodNotFound:t=>o($.errorCodes.rpc.methodNotFound,t),internal:t=>o($.errorCodes.rpc.internal,t),server(t){if(!t||"object"!=typeof t||Array.isArray(t))throw Error("Ethereum RPC Server errors must provide single object argument.");let{code:e}=t;if(!Number.isInteger(e)||e>-32005||e<-32099)throw Error('"code" must be an integer such that: -32099 <= code <= -32005');return o(e,t)},invalidInput:t=>o($.errorCodes.rpc.invalidInput,t),resourceNotFound:t=>o($.errorCodes.rpc.resourceNotFound,t),resourceUnavailable:t=>o($.errorCodes.rpc.resourceUnavailable,t),transactionRejected:t=>o($.errorCodes.rpc.transactionRejected,t),methodNotSupported:t=>o($.errorCodes.rpc.methodNotSupported,t),limitExceeded:t=>o($.errorCodes.rpc.limitExceeded,t)},provider:{userRejectedRequest:t=>s($.errorCodes.provider.userRejectedRequest,t),unauthorized:t=>s($.errorCodes.provider.unauthorized,t),unsupportedMethod:t=>s($.errorCodes.provider.unsupportedMethod,t),disconnected:t=>s($.errorCodes.provider.disconnected,t),chainDisconnected:t=>s($.errorCodes.provider.chainDisconnected,t),custom(t){if(!t||"object"!=typeof t||Array.isArray(t))throw Error("Ethereum Provider custom errors must provide single object argument.");let{code:e,message:n,data:i}=t;if(!n||"string"!=typeof n)throw Error('"message" must be a nonempty string');return new r.EthereumProviderError(e,n,i)}}}},9826(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getMessageFromCode=e.serializeError=e.EthereumProviderError=e.EthereumRpcError=e.ethErrors=e.errorCodes=void 0;let r=n(2294);Object.defineProperty(e,"EthereumRpcError",{enumerable:!0,get:function(){return r.EthereumRpcError}}),Object.defineProperty(e,"EthereumProviderError",{enumerable:!0,get:function(){return r.EthereumProviderError}});let i=n(8753);Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return i.serializeError}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return i.getMessageFromCode}});let $=n(8797);Object.defineProperty(e,"ethErrors",{enumerable:!0,get:function(){return $.ethErrors}});let o=n(2662);Object.defineProperty(e,"errorCodes",{enumerable:!0,get:function(){return o.errorCodes}})},8753(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.serializeError=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;let r=n(2662),i=n(2294),$=r.errorCodes.rpc.internal,o={code:$,message:s($)};function s(t,n="Unspecified error message. This is a bug, please report it."){if(Number.isInteger(t)){let i=t.toString();if(c(r.errorValues,i))return r.errorValues[i].message;if(a(t))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return n}function x(t){if(!Number.isInteger(t))return!1;let e=t.toString();return!!r.errorValues[e]||!!a(t)}function a(t){return t>=-32099&&t<=-32e3}function u(t){return t&&"object"==typeof t&&!Array.isArray(t)?Object.assign({},t):t}function c(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",e.getMessageFromCode=s,e.isValidCode=x,e.serializeError=function(t,{fallbackError:e=o,shouldIncludeStack:n=!1}={}){var r,$;if(!e||!Number.isInteger(e.code)||"string"!=typeof e.message)throw Error("Must provide fallback error with integer number code and string message.");if(t instanceof i.EthereumRpcError)return t.serialize();let a={};if(t&&"object"==typeof t&&!Array.isArray(t)&&c(t,"code")&&x(t.code)){let l=t;a.code=l.code,l.message&&"string"==typeof l.message?(a.message=l.message,c(l,"data")&&(a.data=l.data)):(a.message=s(a.code),a.data={originalError:u(t)})}else{a.code=e.code;let h=null===(r=t)||void 0===r?void 0:r.message;a.message=h&&"string"==typeof h?h:e.message,a.data={originalError:u(t)}}let f=null===($=t)||void 0===$?void 0:$.stack;return n&&t&&f&&"string"==typeof f&&(a.stack=f),a}},7187(t){"use strict";var e,n="object"==typeof Reflect?Reflect:null,r=n&&"function"==typeof n.apply?n.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};e=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var i=Number.isNaN||function(t){return t!=t};function $(){$.init.call(this)}t.exports=$,t.exports.once=function(t,e){return new Promise(function(n,r){var i,$;function o(n){t.removeListener(e,s),r(n)}function s(){"function"==typeof t.removeListener&&t.removeListener("error",o),n([].slice.call(arguments))}d(t,e,s,{once:!0}),"error"!==e&&(i=t,$=o,"function"==typeof i.on&&d(i,"error",$,{once:!0}))})},$.EventEmitter=$,$.prototype._events=void 0,$.prototype._eventsCount=0,$.prototype._maxListeners=void 0;var o=10;function s(t){if("function"!=typeof t)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function x(t){return void 0===t._maxListeners?$.defaultMaxListeners:t._maxListeners}function a(t,e,n,r){var i,$,o,a;if(s(n),void 0===($=t._events)?($=t._events=Object.create(null),t._eventsCount=0):(void 0!==$.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),$=t._events),o=$[e]),void 0===o)o=$[e]=n,++t._eventsCount;else if("function"==typeof o?o=$[e]=r?[n,o]:[o,n]:r?o.unshift(n):o.push(n),(i=x(t))>0&&o.length>i&&!o.warned){o.warned=!0;var u=Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=e,u.count=o.length,a=u,console&&console.warn&&console.warn(a)}return t}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},i=u.bind(r);return i.listener=n,r.wrapFn=i,i}function l(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(t){for(var e=Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}(i):f(i,i.length)}function h(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function f(t,e){for(var n=Array(e),r=0;r<e;++r)n[r]=t[r];return n}function d(t,e,n,r){if("function"==typeof t.on)r.once?t.once(e,n):t.on(e,n);else{if("function"!=typeof t.addEventListener)throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,function i($){r.once&&t.removeEventListener(e,i),n($)})}}Object.defineProperty($,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(t){if("number"!=typeof t||t<0||i(t))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");o=t}}),$.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},$.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||i(t))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},$.prototype.getMaxListeners=function(){return x(this)},$.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var i="error"===t,$=this._events;if(void 0!==$)i=i&&void 0===$.error;else if(!i)return!1;if(i){if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var o,s=Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var x=$[t];if(void 0===x)return!1;if("function"==typeof x)r(x,this,e);else{var a=x.length,u=f(x,a);for(n=0;n<a;++n)r(u[n],this,e)}return!0},$.prototype.addListener=function(t,e){return a(this,t,e,!1)},$.prototype.on=$.prototype.addListener,$.prototype.prependListener=function(t,e){return a(this,t,e,!0)},$.prototype.once=function(t,e){return s(e),this.on(t,c(this,t,e)),this},$.prototype.prependOnceListener=function(t,e){return s(e),this.prependListener(t,c(this,t,e)),this},$.prototype.removeListener=function(t,e){var n,r,i,$,o;if(s(e),void 0===(r=this._events)||void 0===(n=r[t]))return this;if(n===e||n.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!=typeof n){for(i=-1,$=n.length-1;$>=0;$--)if(n[$]===e||n[$].listener===e){o=n[$].listener,i=$;break}if(i<0)return this;0===i?n.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,o||e)}return this},$.prototype.off=$.prototype.removeListener,$.prototype.removeAllListeners=function(t){var e,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,$=Object.keys(n);for(r=0;r<$.length;++r)"removeListener"!==(i=$[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=n[t]))this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;r>=0;r--)this.removeListener(t,e[r]);return this},$.prototype.listeners=function(t){return l(this,t,!0)},$.prototype.rawListeners=function(t){return l(this,t,!1)},$.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):h.call(t,e)},$.prototype.listenerCount=h,$.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}},4445(t){t.exports=o,o.default=o,o.stable=u,o.stableStringify=u;var e="[...]",n="[Circular]",r=[],i=[];function $(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(t,e,n,o){var s;void 0===o&&(o=$()),x(t,"",0,[],void 0,0,o);try{s=0===i.length?JSON.stringify(t,e,n):JSON.stringify(t,l(e),n)}catch(a){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var u=r.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return s}function s(t,e,n,$){var o=Object.getOwnPropertyDescriptor($,n);void 0!==o.get?o.configurable?(Object.defineProperty($,n,{value:t}),r.push([$,n,e,o])):i.push([e,n,t]):($[n]=t,r.push([$,n,e]))}function x(t,r,i,$,o,a,u){var c;if(a+=1,"object"==typeof t&&null!==t){for(c=0;c<$.length;c++)if($[c]===t)return void s(n,t,r,o);if(void 0!==u.depthLimit&&a>u.depthLimit||void 0!==u.edgesLimit&&i+1>u.edgesLimit)return void s(e,t,r,o);if($.push(t),Array.isArray(t))for(c=0;c<t.length;c++)x(t[c],c,c,$,t,a,u);else{var l=Object.keys(t);for(c=0;c<l.length;c++){var h=l[c];x(t[h],h,c,$,t,a,u)}}$.pop()}}function a(t,e){return t<e?-1:t>e?1:0}function u(t,e,n,o){void 0===o&&(o=$());var s,x=c(t,"",0,[],void 0,0,o)||t;try{s=0===i.length?JSON.stringify(x,e,n):JSON.stringify(x,l(e),n)}catch(a){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var u=r.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return s}function c(t,i,$,o,x,u,l){var h;if(u+=1,"object"==typeof t&&null!==t){for(h=0;h<o.length;h++)if(o[h]===t)return void s(n,t,i,x);try{if("function"==typeof t.toJSON)return}catch(f){return}if(void 0!==l.depthLimit&&u>l.depthLimit||void 0!==l.edgesLimit&&$+1>l.edgesLimit)return void s(e,t,i,x);if(o.push(t),Array.isArray(t))for(h=0;h<t.length;h++)c(t[h],h,h,o,t,u,l);else{var d={},p=Object.keys(t).sort(a);for(h=0;h<p.length;h++){var y=p[h];c(t[y],y,h,o,t,u,l),d[y]=t[y]}if(void 0===x)return d;r.push([x,i,t]),x[i]=d}o.pop()}}function l(t){return t=void 0!==t?t:function(t,e){return e},function(e,n){if(i.length>0)for(var r=0;r<i.length;r++){var $=i[r];if($[1]===e&&$[0]===n){n=$[2],i.splice(r,1);break}}return t.call(this,e,n)}}},4029(t,e,n){"use strict";var r=n(5320),i=Object.prototype.toString,$=Object.prototype.hasOwnProperty,o=function(t,e,n){for(var r=0,i=t.length;r<i;r++)$.call(t,r)&&(null==n?e(t[r],r,t):e.call(n,t[r],r,t))},s=function(t,e,n){for(var r=0,i=t.length;r<i;r++)null==n?e(t.charAt(r),r,t):e.call(n,t.charAt(r),r,t)},x=function(t,e,n){for(var r in t)$.call(t,r)&&(null==n?e(t[r],r,t):e.call(n,t[r],r,t))};t.exports=function(t,e,n){var $;if(!r(e))throw TypeError("iterator must be a function");arguments.length>=3&&($=n),"[object Array]"===i.call(t)?o(t,e,$):"string"==typeof t?s(t,e,$):x(t,e,$)}},7648(t){"use strict";var e="Function.prototype.bind called on incompatible ",n=Array.prototype.slice,r=Object.prototype.toString,i="[object Function]";t.exports=function(t){var $=this;if("function"!=typeof $||r.call($)!==i)throw TypeError(e+$);for(var o,s=n.call(arguments,1),x=function(){if(this instanceof o){var e=$.apply(this,s.concat(n.call(arguments)));return Object(e)===e?e:this}return $.apply(t,s.concat(n.call(arguments)))},a=Math.max(0,$.length-s.length),u=[],c=0;c<a;c++)u.push("$"+c);if(o=Function("binder","return function ("+u.join(",")+"){ return binder.apply(this,arguments); }")(x),$.prototype){var l=function(){};l.prototype=$.prototype,o.prototype=new l,l.prototype=null}return o}},8612(t,e,n){"use strict";var r=n(7648);t.exports=Function.prototype.bind||r},210(_0x59440f,_0x3dccf2,_0x24d710){"use strict";var _0x5e5bf5,_0x13e726=SyntaxError,_0x825bf2=Function,_0x4b03f9=TypeError,_0x1c004e=function(t){try{return _0x825bf2('"use strict"; return ('+t+").constructor;")()}catch(e){}},_0x591fd8=Object.getOwnPropertyDescriptor;if(_0x591fd8)try{_0x591fd8({},"")}catch(_0x22fae2){_0x591fd8=null}var _0x219d6c=function(){throw new _0x4b03f9},_0x52bd93=_0x591fd8?function(){try{return _0x219d6c}catch(t){try{return _0x591fd8(arguments,"callee").get}catch(e){return _0x219d6c}}}():_0x219d6c,_0x1166d1=_0x24d710(1405)(),_0x412085=Object.getPrototypeOf||function(t){return t.__proto__},_0xffbe2d={},_0x3426ad="undefined"==typeof Uint8Array?_0x5e5bf5:_0x412085(Uint8Array),_0x46faa6={"%AggregateError%":"undefined"==typeof AggregateError?_0x5e5bf5:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?_0x5e5bf5:ArrayBuffer,"%ArrayIteratorPrototype%":_0x1166d1?_0x412085([][Symbol.iterator]()):_0x5e5bf5,"%AsyncFromSyncIteratorPrototype%":_0x5e5bf5,"%AsyncFunction%":_0xffbe2d,"%AsyncGenerator%":_0xffbe2d,"%AsyncGeneratorFunction%":_0xffbe2d,"%AsyncIteratorPrototype%":_0xffbe2d,"%Atomics%":"undefined"==typeof Atomics?_0x5e5bf5:Atomics,"%BigInt%":"undefined"==typeof BigInt?_0x5e5bf5:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?_0x5e5bf5:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?_0x5e5bf5:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?_0x5e5bf5:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?_0x5e5bf5:FinalizationRegistry,"%Function%":_0x825bf2,"%GeneratorFunction%":_0xffbe2d,"%Int8Array%":"undefined"==typeof Int8Array?_0x5e5bf5:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?_0x5e5bf5:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?_0x5e5bf5:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":_0x1166d1?_0x412085(_0x412085([][Symbol.iterator]())):_0x5e5bf5,"%JSON%":"object"==typeof JSON?JSON:_0x5e5bf5,"%Map%":"undefined"==typeof Map?_0x5e5bf5:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&_0x1166d1?_0x412085(new Map()[Symbol.iterator]()):_0x5e5bf5,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?_0x5e5bf5:Promise,"%Proxy%":"undefined"==typeof Proxy?_0x5e5bf5:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?_0x5e5bf5:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?_0x5e5bf5:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&_0x1166d1?_0x412085(new Set()[Symbol.iterator]()):_0x5e5bf5,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?_0x5e5bf5:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":_0x1166d1?_0x412085(""[Symbol.iterator]()):_0x5e5bf5,"%Symbol%":_0x1166d1?Symbol:_0x5e5bf5,"%SyntaxError%":_0x13e726,"%ThrowTypeError%":_0x52bd93,"%TypedArray%":_0x3426ad,"%TypeError%":_0x4b03f9,"%Uint8Array%":"undefined"==typeof Uint8Array?_0x5e5bf5:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?_0x5e5bf5:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?_0x5e5bf5:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?_0x5e5bf5:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?_0x5e5bf5:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?_0x5e5bf5:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?_0x5e5bf5:WeakSet},_0x4edf62=function t(e){var n;if("%AsyncFunction%"===e)n=_0x1c004e("async function () {}");else if("%GeneratorFunction%"===e)n=_0x1c004e("function* () {}");else if("%AsyncGeneratorFunction%"===e)n=_0x1c004e("async function* () {}");else if("%AsyncGenerator%"===e){var r=t("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===e){var i=t("%AsyncGenerator%");i&&(n=_0x412085(i.prototype))}return _0x46faa6[e]=n,n},_0x5e05c1={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},_0x11ec9f=_0x24d710(8612),_0x9f3221=_0x24d710(7642),_0x37312c=_0x11ec9f.call(Function.call,Array.prototype.concat),_0x3f4942=_0x11ec9f.call(Function.apply,Array.prototype.splice),_0x4c3d06=_0x11ec9f.call(Function.call,String.prototype.replace),_0x159e4e=_0x11ec9f.call(Function.call,String.prototype.slice),_0xfeb629=_0x11ec9f.call(Function.call,RegExp.prototype.exec),_0x1dc01b=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,_0x937fd8=/\\(\\)?/g,_0x10291b=function(t){var e=_0x159e4e(t,0,1),n=_0x159e4e(t,-1);if("%"===e&&"%"!==n)throw new _0x13e726("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==e)throw new _0x13e726("invalid intrinsic syntax, expected opening `%`");var r=[];return _0x4c3d06(t,_0x1dc01b,function(t,e,n,i){r[r.length]=n?_0x4c3d06(i,_0x937fd8,"$1"):e||t}),r},_0x73a98f=function(t,e){var n,r=t;if(_0x9f3221(_0x5e05c1,r)&&(r="%"+(n=_0x5e05c1[r])[0]+"%"),_0x9f3221(_0x46faa6,r)){var i=_0x46faa6[r];if(i===_0xffbe2d&&(i=_0x4edf62(r)),void 0===i&&!e)throw new _0x4b03f9("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:i}}throw new _0x13e726("intrinsic "+t+" does not exist!")};_0x59440f.exports=function(t,e){if("string"!=typeof t||0===t.length)throw new _0x4b03f9("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new _0x4b03f9('"allowMissing" argument must be a boolean');if(null===_0xfeb629(/^%?[^%]*%?$/g,t))throw new _0x13e726("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=_0x10291b(t),r=n.length>0?n[0]:"",i=_0x73a98f("%"+r+"%",e),$=i.name,o=i.value,s=!1,x=i.alias;x&&(r=x[0],_0x3f4942(n,_0x37312c([0,1],x)));for(var a=1,u=!0;a<n.length;a+=1){var c=n[a],l=_0x159e4e(c,0,1),h=_0x159e4e(c,-1);if(('"'===l||"'"===l||"`"===l||'"'===h||"'"===h||"`"===h)&&l!==h)throw new _0x13e726("property names with quotes must have matching quotes");if("constructor"!==c&&u||(s=!0),_0x9f3221(_0x46faa6,$="%"+(r+="."+c)+"%"))o=_0x46faa6[$];else if(null!=o){if(!(c in o)){if(!e)throw new _0x4b03f9("base intrinsic for "+t+" exists, but the property is not available.");return}if(_0x591fd8&&a+1>=n.length){var f=_0x591fd8(o,c);o=(u=!!f)&&"get"in f&&!("originalValue"in f.get)?f.get:o[c]}else u=_0x9f3221(o,c),o=o[c];u&&!s&&(_0x46faa6[$]=o)}}return o}},1405(t,e,n){"use strict";var r="undefined"!=typeof Symbol&&Symbol,i=n(5419);t.exports=function(){return"function"==typeof r&&"function"==typeof Symbol&&"symbol"==typeof r("foo")&&"symbol"==typeof Symbol("bar")&&i()}},5419(t){"use strict";t.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),n=Object(e);if("string"==typeof e||"[object Symbol]"!==Object.prototype.toString.call(e)||"[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var r=Object.getOwnPropertySymbols(t);if(1!==r.length||r[0]!==e||!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(t,e);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},6410(t,e,n){"use strict";var r=n(5419);t.exports=function(){return r()&&!!Symbol.toStringTag}},7642(t,e,n){"use strict";var r=n(8612);t.exports=r.call(Function.call,Object.prototype.hasOwnProperty)},645(t,e){e.read=function(t,e,n,r,i){var $,o,s=8*i-r-1,x=(1<<s)-1,a=x>>1,u=-7,c=n?i-1:0,l=n?-1:1,h=t[e+c];for(c+=l,$=h&(1<<-u)-1,h>>=-u,u+=s;u>0;$=256*$+t[e+c],c+=l,u-=8);for(o=$&(1<<-u)-1,$>>=-u,u+=r;u>0;o=256*o+t[e+c],c+=l,u-=8);if(0===$)$=1-a;else{if($===x)return o?NaN:1/0*(h?-1:1);o+=Math.pow(2,r),$-=a}return(h?-1:1)*o*Math.pow(2,$-r)},e.write=function(t,e,n,r,i,$){var o,s,x,a=8*$-i-1,u=(1<<a)-1,c=u>>1,l=23===i?5960464477539062e-23:0,h=r?0:$-1,f=r?1:-1,d=e<0||0===e&&1/e<0?1:0;for(isNaN(e=Math.abs(e))||e===1/0?(s=isNaN(e)?1:0,o=u):(o=Math.floor(Math.log(e)/Math.LN2),e*(x=Math.pow(2,-o))<1&&(o--,x*=2),(e+=o+c>=1?l/x:l*Math.pow(2,1-c))*x>=2&&(o++,x/=2),o+c>=u?(s=0,o=u):o+c>=1?(s=(e*x-1)*Math.pow(2,i),o+=c):(s=e*Math.pow(2,c-1)*Math.pow(2,i),o=0));i>=8;t[n+h]=255&s,h+=f,s/=256,i-=8);for(o=o<<i|s,a+=i;a>0;t[n+h]=255&o,h+=f,o/=256,a-=8);t[n+h-f]|=128*d}},5717(t){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},2584(t,e,n){"use strict";var r=n(6410)(),i=n(1924)("Object.prototype.toString"),$=function(t){return!(r&&t&&"object"==typeof t&&Symbol.toStringTag in t)&&"[object Arguments]"===i(t)},o=function(t){return!!$(t)||null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Array]"!==i(t)&&"[object Function]"===i(t.callee)},s=function(){return $(arguments)}();$.isLegacyArguments=o,t.exports=s?$:o},5320(t){"use strict";var e,n,r=Function.prototype.toString,i="object"==typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"==typeof i&&"function"==typeof Object.defineProperty)try{e=Object.defineProperty({},"length",{get:function(){throw n}}),n={},i(function(){throw 42},null,e)}catch($){$!==n&&(i=null)}else i=null;var o=/^\s*class\b/,s=function(t){try{var e=r.call(t);return o.test(e)}catch(n){return!1}},x=Object.prototype.toString,a="function"==typeof Symbol&&!!Symbol.toStringTag,u="object"==typeof document&&void 0===document.all&&void 0!==document.all?document.all:{};t.exports=i?function(t){if(t===u)return!0;if(!t||"function"!=typeof t&&"object"!=typeof t)return!1;if("function"==typeof t&&!t.prototype)return!0;try{i(t,null,e)}catch(r){if(r!==n)return!1}return!s(t)}:function(t){if(t===u)return!0;if(!t||"function"!=typeof t&&"object"!=typeof t)return!1;if("function"==typeof t&&!t.prototype)return!0;if(a)return function(t){try{return!s(t)&&(r.call(t),!0)}catch(e){return!1}}(t);if(s(t))return!1;var e=x.call(t);return"[object Function]"===e||"[object GeneratorFunction]"===e}},8662(t,e,n){"use strict";var r,i=Object.prototype.toString,$=Function.prototype.toString,o=/^\s*(?:function)?\*/,s=n(6410)(),x=Object.getPrototypeOf;t.exports=function(t){if("function"!=typeof t)return!1;if(o.test($.call(t)))return!0;if(!s)return"[object GeneratorFunction]"===i.call(t);if(!x)return!1;if(void 0===r){var e=function(){if(!s)return!1;try{return Function("return function*() {}")()}catch(t){}}();r=!!e&&x(e)}return x(t)===r}},5692(t,e,n){"use strict";var r=n(4029),i=n(3083),$=n(1924),o=$("Object.prototype.toString"),s=n(6410)(),x="undefined"==typeof globalThis?n.g:globalThis,a=i(),u=$("Array.prototype.indexOf",!0)||function(t,e){for(var n=0;n<t.length;n+=1)if(t[n]===e)return n;return -1},c=$("String.prototype.slice"),l={},h=n(882),f=Object.getPrototypeOf;s&&h&&f&&r(a,function(t){var e=new x[t];if(Symbol.toStringTag in e){var n=f(e),r=h(n,Symbol.toStringTag);if(!r){var i=f(n);r=h(i,Symbol.toStringTag)}l[t]=r.get}}),t.exports=function(t){if(!t||"object"!=typeof t)return!1;if(!s||!(Symbol.toStringTag in t)){var e,n,i=c(o(t),8,-1);return u(a,i)>-1}return!!h&&(e=t,n=!1,r(l,function(t,r){if(!n)try{n=t.call(e)===r}catch(i){}}),n)}},2023(_0x1708d9,_0x43d519,_0x204202){var _0xfca2f2;!function(){"use strict";var _0x6d9595="input is invalid type",_0x2912bf="object"==typeof window,_0xdd1c38=_0x2912bf?window:{};_0xdd1c38.JS_SHA256_NO_WINDOW&&(_0x2912bf=!1);var _0x1ea5ae=!_0x2912bf&&"object"==typeof self,_0x292dac=!_0xdd1c38.JS_SHA256_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;_0x292dac?_0xdd1c38=_0x204202.g:_0x1ea5ae&&(_0xdd1c38=self);var _0xc770a2=!_0xdd1c38.JS_SHA256_NO_COMMON_JS&&_0x1708d9.exports,_0x23a0f8=_0x204202.amdO,_0x20d2b2=!_0xdd1c38.JS_SHA256_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,_0x331a07="0123456789abcdef".split(""),_0x4d5034=[-2147483648,8388608,32768,128],_0x3fb27f=[24,16,8,0],_0x33ccee=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],_0x390463=["hex","array","digest","arrayBuffer"],_0x1a13b2=[];!_0xdd1c38.JS_SHA256_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),_0x20d2b2&&(_0xdd1c38.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var _0xb88176=function(t,e){return function(n){return new _0xce2908(e,!0).update(n)[t]()}},_0x29ff5c=function(t){var e=_0xb88176("hex",t);_0x292dac&&(e=_0x409665(e,t)),e.create=function(){return new _0xce2908(t)},e.update=function(t){return e.create().update(t)};for(var n=0;n<_0x390463.length;++n){var r=_0x390463[n];e[r]=_0xb88176(r,t)}return e},_0x409665=function(_0x40f178,_0x31d24d){var _0x5d711c,_0x3d6b3d=eval("require('crypto');"),_0x563f9c=eval("require('buffer')['Buffer'];"),_0x3855c5=_0x31d24d?"sha224":"sha256";return function(t){if("string"==typeof t)return _0x3d6b3d.createHash(_0x3855c5).update(t,"utf8").digest("hex");if(null==t)throw Error(_0x6d9595);return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===_0x563f9c?_0x3d6b3d.createHash(_0x3855c5).update(new _0x563f9c(t)).digest("hex"):_0x40f178(t)}},_0x2332dd=function(t,e){return function(n,r){return new _0x1847fc(n,e,!0).update(r)[t]()}},_0x1cfdbe=function(t){var e=_0x2332dd("hex",t);e.create=function(e){return new _0x1847fc(e,t)},e.update=function(t,n){return e.create(t).update(n)};for(var n=0;n<_0x390463.length;++n){var r=_0x390463[n];e[r]=_0x2332dd(r,t)}return e};function _0xce2908(t,e){e?(_0x1a13b2[0]=_0x1a13b2[16]=_0x1a13b2[1]=_0x1a13b2[2]=_0x1a13b2[3]=_0x1a13b2[4]=_0x1a13b2[5]=_0x1a13b2[6]=_0x1a13b2[7]=_0x1a13b2[8]=_0x1a13b2[9]=_0x1a13b2[10]=_0x1a13b2[11]=_0x1a13b2[12]=_0x1a13b2[13]=_0x1a13b2[14]=_0x1a13b2[15]=0,this.blocks=_0x1a13b2):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t?(this.h0=3238371032,this.h1=914150663,this.h2=812702999,this.h3=4144912697,this.h4=4290775857,this.h5=1750603025,this.h6=1694076839,this.h7=3204075428):(this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=t}function _0x1847fc(t,e,n){var r,i=typeof t;if("string"===i){var $,o=[],s=t.length,x=0;for(r=0;r<s;++r)($=t.charCodeAt(r))<128?o[x++]=$:$<2048?(o[x++]=192|$>>6,o[x++]=128|63&$):$<55296||$>=57344?(o[x++]=224|$>>12,o[x++]=128|$>>6&63,o[x++]=128|63&$):($=65536+((1023&$)<<10|1023&t.charCodeAt(++r)),o[x++]=240|$>>18,o[x++]=128|$>>12&63,o[x++]=128|$>>6&63,o[x++]=128|63&$);t=o}else{if("object"!==i||null===t)throw Error(_0x6d9595);if(_0x20d2b2&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||_0x20d2b2&&ArrayBuffer.isView(t)))throw Error(_0x6d9595)}t.length>64&&(t=new _0xce2908(e,!0).update(t).array());var a=[],u=[];for(r=0;r<64;++r){var c=t[r]||0;a[r]=92^c,u[r]=54^c}_0xce2908.call(this,e,n),this.update(u),this.oKeyPad=a,this.inner=!0,this.sharedMemory=n}_0xce2908.prototype.update=function(t){if(!this.finalized){var e,n=typeof t;if("string"!==n){if("object"!==n||null===t)throw Error(_0x6d9595);if(_0x20d2b2&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||_0x20d2b2&&ArrayBuffer.isView(t)))throw Error(_0x6d9595);e=!0}for(var r,i,$=0,o=t.length,s=this.blocks;$<o;){if(this.hashed&&(this.hashed=!1,s[0]=this.block,s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),e)for(i=this.start;$<o&&i<64;++$)s[i>>2]|=t[$]<<_0x3fb27f[3&i++];else for(i=this.start;$<o&&i<64;++$)(r=t.charCodeAt($))<128?s[i>>2]|=r<<_0x3fb27f[3&i++]:r<2048?(s[i>>2]|=(192|r>>6)<<_0x3fb27f[3&i++],s[i>>2]|=(128|63&r)<<_0x3fb27f[3&i++]):r<55296||r>=57344?(s[i>>2]|=(224|r>>12)<<_0x3fb27f[3&i++],s[i>>2]|=(128|r>>6&63)<<_0x3fb27f[3&i++],s[i>>2]|=(128|63&r)<<_0x3fb27f[3&i++]):(r=65536+((1023&r)<<10|1023&t.charCodeAt(++$)),s[i>>2]|=(240|r>>18)<<_0x3fb27f[3&i++],s[i>>2]|=(128|r>>12&63)<<_0x3fb27f[3&i++],s[i>>2]|=(128|r>>6&63)<<_0x3fb27f[3&i++],s[i>>2]|=(128|63&r)<<_0x3fb27f[3&i++]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.block=s[16],this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},_0xce2908.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[16]=this.block,t[e>>2]|=_0x4d5034[3&e],this.block=t[16],e>=56&&(this.hashed||this.hash(),t[0]=this.block,t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.hBytes<<3|this.bytes>>>29,t[15]=this.bytes<<3,this.hash()}},_0xce2908.prototype.hash=function(){var t,e,n,r,i,$,o,s,x,a=this.h0,u=this.h1,c=this.h2,l=this.h3,h=this.h4,f=this.h5,d=this.h6,p=this.h7,y=this.blocks;for(t=16;t<64;++t)e=((i=y[t-15])>>>7|i<<25)^(i>>>18|i<<14)^i>>>3,n=((i=y[t-2])>>>17|i<<15)^(i>>>19|i<<13)^i>>>10,y[t]=y[t-16]+e+y[t-7]+n<<0;for(x=u&c,t=0;t<64;t+=4)this.first?(this.is224?($=300032,p=(i=y[0]-1413257819)-150054599<<0,l=i+24177077<<0):($=704751109,p=(i=y[0]-210244248)-1521486534<<0,l=i+143694565<<0),this.first=!1):(e=(a>>>2|a<<30)^(a>>>13|a<<19)^(a>>>22|a<<10),r=($=a&u)^a&c^x,p=l+(i=p+(n=(h>>>6|h<<26)^(h>>>11|h<<21)^(h>>>25|h<<7))+(h&f^~h&d)+_0x33ccee[t]+y[t])<<0,l=i+(e+r)<<0),e=(l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10),r=(o=l&a)^l&u^$,d=c+(i=d+(n=(p>>>6|p<<26)^(p>>>11|p<<21)^(p>>>25|p<<7))+(p&h^~p&f)+_0x33ccee[t+1]+y[t+1])<<0,e=((c=i+(e+r)<<0)>>>2|c<<30)^(c>>>13|c<<19)^(c>>>22|c<<10),r=(s=c&l)^c&a^o,f=u+(i=f+(n=(d>>>6|d<<26)^(d>>>11|d<<21)^(d>>>25|d<<7))+(d&p^~d&h)+_0x33ccee[t+2]+y[t+2])<<0,e=((u=i+(e+r)<<0)>>>2|u<<30)^(u>>>13|u<<19)^(u>>>22|u<<10),r=(x=u&c)^u&l^s,h=a+(i=h+(n=(f>>>6|f<<26)^(f>>>11|f<<21)^(f>>>25|f<<7))+(f&d^~f&p)+_0x33ccee[t+3]+y[t+3])<<0,a=i+(e+r)<<0;this.h0=this.h0+a<<0,this.h1=this.h1+u<<0,this.h2=this.h2+c<<0,this.h3=this.h3+l<<0,this.h4=this.h4+h<<0,this.h5=this.h5+f<<0,this.h6=this.h6+d<<0,this.h7=this.h7+p<<0},_0xce2908.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,n=this.h2,r=this.h3,i=this.h4,$=this.h5,o=this.h6,s=this.h7,x=_0x331a07[t>>28&15]+_0x331a07[t>>24&15]+_0x331a07[t>>20&15]+_0x331a07[t>>16&15]+_0x331a07[t>>12&15]+_0x331a07[t>>8&15]+_0x331a07[t>>4&15]+_0x331a07[15&t]+_0x331a07[e>>28&15]+_0x331a07[e>>24&15]+_0x331a07[e>>20&15]+_0x331a07[e>>16&15]+_0x331a07[e>>12&15]+_0x331a07[e>>8&15]+_0x331a07[e>>4&15]+_0x331a07[15&e]+_0x331a07[n>>28&15]+_0x331a07[n>>24&15]+_0x331a07[n>>20&15]+_0x331a07[n>>16&15]+_0x331a07[n>>12&15]+_0x331a07[n>>8&15]+_0x331a07[n>>4&15]+_0x331a07[15&n]+_0x331a07[r>>28&15]+_0x331a07[r>>24&15]+_0x331a07[r>>20&15]+_0x331a07[r>>16&15]+_0x331a07[r>>12&15]+_0x331a07[r>>8&15]+_0x331a07[r>>4&15]+_0x331a07[15&r]+_0x331a07[i>>28&15]+_0x331a07[i>>24&15]+_0x331a07[i>>20&15]+_0x331a07[i>>16&15]+_0x331a07[i>>12&15]+_0x331a07[i>>8&15]+_0x331a07[i>>4&15]+_0x331a07[15&i]+_0x331a07[$>>28&15]+_0x331a07[$>>24&15]+_0x331a07[$>>20&15]+_0x331a07[$>>16&15]+_0x331a07[$>>12&15]+_0x331a07[$>>8&15]+_0x331a07[$>>4&15]+_0x331a07[15&$]+_0x331a07[o>>28&15]+_0x331a07[o>>24&15]+_0x331a07[o>>20&15]+_0x331a07[o>>16&15]+_0x331a07[o>>12&15]+_0x331a07[o>>8&15]+_0x331a07[o>>4&15]+_0x331a07[15&o];return this.is224||(x+=_0x331a07[s>>28&15]+_0x331a07[s>>24&15]+_0x331a07[s>>20&15]+_0x331a07[s>>16&15]+_0x331a07[s>>12&15]+_0x331a07[s>>8&15]+_0x331a07[s>>4&15]+_0x331a07[15&s]),x},_0xce2908.prototype.toString=_0xce2908.prototype.hex,_0xce2908.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,n=this.h2,r=this.h3,i=this.h4,$=this.h5,o=this.h6,s=this.h7,x=[t>>24&255,t>>16&255,t>>8&255,255&t,e>>24&255,e>>16&255,e>>8&255,255&e,n>>24&255,n>>16&255,n>>8&255,255&n,r>>24&255,r>>16&255,r>>8&255,255&r,i>>24&255,i>>16&255,i>>8&255,255&i,$>>24&255,$>>16&255,$>>8&255,255&$,o>>24&255,o>>16&255,o>>8&255,255&o];return this.is224||x.push(s>>24&255,s>>16&255,s>>8&255,255&s),x},_0xce2908.prototype.array=_0xce2908.prototype.digest,_0xce2908.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(this.is224?28:32),e=new DataView(t);return e.setUint32(0,this.h0),e.setUint32(4,this.h1),e.setUint32(8,this.h2),e.setUint32(12,this.h3),e.setUint32(16,this.h4),e.setUint32(20,this.h5),e.setUint32(24,this.h6),this.is224||e.setUint32(28,this.h7),t},_0x1847fc.prototype=new _0xce2908,_0x1847fc.prototype.finalize=function(){if(_0xce2908.prototype.finalize.call(this),this.inner){this.inner=!1;var t=this.array();_0xce2908.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(t),_0xce2908.prototype.finalize.call(this)}};var _0x1b628e=_0x29ff5c();_0x1b628e.sha256=_0x1b628e,_0x1b628e.sha224=_0x29ff5c(!0),_0x1b628e.sha256.hmac=_0x1cfdbe(),_0x1b628e.sha224.hmac=_0x1cfdbe(!0),_0xc770a2?_0x1708d9.exports=_0x1b628e:(_0xdd1c38.sha256=_0x1b628e.sha256,_0xdd1c38.sha224=_0x1b628e.sha224,_0x23a0f8&&(void 0===(_0xfca2f2=(function(){return _0x1b628e}).call(_0x1b628e,_0x204202,_0x1b628e,_0x1708d9))||(_0x1708d9.exports=_0xfca2f2)))}()},7398:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.JsonRpcEngine=void 0;let i=r(n(9394)),$=n(9826);class o extends i.default{constructor(){super(),this._middleware=[]}push(t){this._middleware.push(t)}handle(t,e){if(e&&"function"!=typeof e)throw Error('"callback" must be a function if provided.');return Array.isArray(t)?e?this._handleBatch(t,e):this._handleBatch(t):e?this._handle(t,e):this._promiseHandle(t)}asMiddleware(){return async(t,e,n,r)=>{try{let[i,$,s]=await o._runAllMiddleware(t,e,this._middleware);return $?(await o._runReturnHandlers(s),r(i)):n(async t=>{try{await o._runReturnHandlers(s)}catch(e){return t(e)}return t()})}catch(x){return r(x)}}}async _handleBatch(t,e){try{let n=await Promise.all(t.map(this._promiseHandle.bind(this)));return e?e(null,n):n}catch(r){if(e)return e(r);throw r}}_promiseHandle(t){return new Promise(e=>{this._handle(t,(t,n)=>{e(n)})})}async _handle(t,e){if(!t||Array.isArray(t)||"object"!=typeof t){let n=new $.EthereumRpcError($.errorCodes.rpc.invalidRequest,"Requests must be plain objects. Received: "+typeof t,{request:t});return e(n,{id:void 0,jsonrpc:"2.0",error:n})}if("string"!=typeof t.method){let r=new $.EthereumRpcError($.errorCodes.rpc.invalidRequest,"Must specify a string method. Received: "+typeof t.method,{request:t});return e(r,{id:t.id,jsonrpc:"2.0",error:r})}let i=Object.assign({},t),o={id:i.id,jsonrpc:i.jsonrpc},s=null;try{await this._processRequest(i,o)}catch(x){s=x}return s&&(delete o.result,o.error||(o.error=$.serializeError(s))),e(s,o)}async _processRequest(t,e){let[n,r,i]=await o._runAllMiddleware(t,e,this._middleware);if(o._checkForCompletion(t,e,r),await o._runReturnHandlers(i),n)throw n}static async _runAllMiddleware(t,e,n){let r=[],i=null,$=!1;for(let s of n)if([i,$]=await o._runMiddleware(t,e,s,r),$)break;return[i,$,r.reverse()]}static _runMiddleware(t,e,n,r){return new Promise(i=>{let o=t=>{let n=t||e.error;n&&(e.error=$.serializeError(n)),i([n,!0])},x=n=>{e.error?o(e.error):(n&&("function"!=typeof n&&o(new $.EthereumRpcError($.errorCodes.rpc.internal,'JsonRpcEngine: "next" return handlers must be functions. Received "'+typeof n+'" for request:\n'+s(t),{request:t})),r.push(n)),i([null,!1]))};try{n(t,e,x,o)}catch(a){o(a)}})}static async _runReturnHandlers(t){for(let e of t)await new Promise((t,n)=>{e(e=>e?n(e):t())})}static _checkForCompletion(t,e,n){if(!("result"in e)&&!("error"in e))throw new $.EthereumRpcError($.errorCodes.rpc.internal,"JsonRpcEngine: Response has no error or result for request:\n"+s(t),{request:t});if(!n)throw new $.EthereumRpcError($.errorCodes.rpc.internal,"JsonRpcEngine: Nothing ended request:\n"+s(t),{request:t})}}function s(t){return JSON.stringify(t,null,2)}e.JsonRpcEngine=o},1841(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createAsyncMiddleware=void 0,e.createAsyncMiddleware=function(t){return async(e,n,r,i)=>{let $,o=new Promise(t=>{$=t}),s=null,x=!1,a=async()=>{x=!0,r(t=>{s=t,$()}),await o};try{await t(e,n,a),x?(await o,s(null)):i(null)}catch(u){s?s(u):i(u)}}}},8508(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createScaffoldMiddleware=void 0,e.createScaffoldMiddleware=function(t){return(e,n,r,i)=>{let $=t[e.method];return void 0===$?r():"function"==typeof $?$(e,n,r,i):(n.result=$,i())}}},3107(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getUniqueId=void 0;let n=4294967295,r=Math.floor(Math.random()*n);e.getUniqueId=function(){return r=(r+1)%n}},5086(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createIdRemapMiddleware=void 0;let r=n(3107);e.createIdRemapMiddleware=function(){return(t,e,n,i)=>{let $=t.id,o=r.getUniqueId();t.id=o,e.id=o,n(n=>{t.id=$,e.id=$,n()})}}},8625:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||r(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),i(n(5086),e),i(n(1841),e),i(n(8508),e),i(n(3107),e),i(n(7398),e),i(n(9962),e)},9962(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mergeMiddleware=void 0;let r=n(7398);e.mergeMiddleware=function(t){let e=new r.JsonRpcEngine;return t.forEach(t=>e.push(t)),e.asMiddleware()}},3420(t){t.exports=function(t){var e=(t=t||{}).max||Number.MAX_SAFE_INTEGER,n=void 0!==t.start?t.start:Math.floor(Math.random()*e);return function(){return n%=e,n++}}},8598(t,e,n){t.exports=n(6066)(n(9653))},6066(t,e,n){let r=n(7016),i=n(5675);t.exports=function(t){let e=r(t),n=i(t);return function(t,r){switch("string"==typeof t?t.toLowerCase():t){case"keccak224":return new e(1152,448,null,224,r);case"keccak256":return new e(1088,512,null,256,r);case"keccak384":return new e(832,768,null,384,r);case"keccak512":return new e(576,1024,null,512,r);case"sha3-224":return new e(1152,448,6,224,r);case"sha3-256":return new e(1088,512,6,256,r);case"sha3-384":return new e(832,768,6,384,r);case"sha3-512":return new e(576,1024,6,512,r);case"shake128":return new n(1344,256,31,r);case"shake256":return new n(1088,512,31,r);default:throw Error("Invald algorithm: "+t)}}}},7016(t,e,n){var r=n(8764).Buffer;let{Transform:i}=n(8473);t.exports=t=>class e extends i{constructor(e,n,r,i,$){super($),this._rate=e,this._capacity=n,this._delimitedSuffix=r,this._hashBitLength=i,this._options=$,this._state=new t,this._state.initialize(e,n),this._finalized=!1}_transform(t,e,n){let r=null;try{this.update(t,e)}catch(i){r=i}n(r)}_flush(t){let e=null;try{this.push(this.digest())}catch(n){e=n}t(e)}update(t,e){if(!r.isBuffer(t)&&"string"!=typeof t)throw TypeError("Data must be a string or a buffer");if(this._finalized)throw Error("Digest already called");return r.isBuffer(t)||(t=r.from(t,e)),this._state.absorb(t),this}digest(t){if(this._finalized)throw Error("Digest already called");this._finalized=!0,this._delimitedSuffix&&this._state.absorbLastFewBits(this._delimitedSuffix);let e=this._state.squeeze(this._hashBitLength/8);return void 0!==t&&(e=e.toString(t)),this._resetState(),e}_resetState(){return this._state.initialize(this._rate,this._capacity),this}_clone(){let t=new e(this._rate,this._capacity,this._delimitedSuffix,this._hashBitLength,this._options);return this._state.copy(t._state),t._finalized=this._finalized,t}}},5675(t,e,n){var r=n(8764).Buffer;let{Transform:i}=n(8473);t.exports=t=>class e extends i{constructor(e,n,r,i){super(i),this._rate=e,this._capacity=n,this._delimitedSuffix=r,this._options=i,this._state=new t,this._state.initialize(e,n),this._finalized=!1}_transform(t,e,n){let r=null;try{this.update(t,e)}catch(i){r=i}n(r)}_flush(){}_read(t){this.push(this.squeeze(t))}update(t,e){if(!r.isBuffer(t)&&"string"!=typeof t)throw TypeError("Data must be a string or a buffer");if(this._finalized)throw Error("Squeeze already called");return r.isBuffer(t)||(t=r.from(t,e)),this._state.absorb(t),this}squeeze(t,e){this._finalized||(this._finalized=!0,this._state.absorbLastFewBits(this._delimitedSuffix));let n=this._state.squeeze(t);return void 0!==e&&(n=n.toString(e)),n}_resetState(){return this._state.initialize(this._rate,this._capacity),this}_clone(){let t=new e(this._rate,this._capacity,this._delimitedSuffix,this._options);return this._state.copy(t._state),t._finalized=this._finalized,t}}},4040(t,e){let n=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648];e.p1600=function(t){for(let e=0;e<24;++e){let r=t[0]^t[10]^t[20]^t[30]^t[40],i=t[1]^t[11]^t[21]^t[31]^t[41],$=t[2]^t[12]^t[22]^t[32]^t[42],o=t[3]^t[13]^t[23]^t[33]^t[43],s=t[4]^t[14]^t[24]^t[34]^t[44],x=t[5]^t[15]^t[25]^t[35]^t[45],a=t[6]^t[16]^t[26]^t[36]^t[46],u=t[7]^t[17]^t[27]^t[37]^t[47],c=t[8]^t[18]^t[28]^t[38]^t[48],l=t[9]^t[19]^t[29]^t[39]^t[49],h=c^($<<1|o>>>31),f=l^(o<<1|$>>>31),d=t[0]^h,p=t[1]^f,y=t[10]^h,g=t[11]^f,b=t[20]^h,_=t[21]^f,m=t[30]^h,v=t[31]^f,w=t[40]^h,S=t[41]^f;h=r^(s<<1|x>>>31),f=i^(x<<1|s>>>31);let k=t[2]^h,E=t[3]^f,C=t[12]^h,M=t[13]^f,I=t[22]^h,A=t[23]^f,T=t[32]^h,R=t[33]^f,L=t[42]^h,N=t[43]^f;h=$^(a<<1|u>>>31),f=o^(u<<1|a>>>31);let P=t[4]^h,D=t[5]^f,O=t[14]^h,B=t[15]^f,j=t[24]^h,U=t[25]^f,F=t[34]^h,W=t[35]^f,z=t[44]^h,H=t[45]^f;h=s^(c<<1|l>>>31),f=x^(l<<1|c>>>31);let V=t[6]^h,q=t[7]^f,Z=t[16]^h,G=t[17]^f,Y=t[26]^h,K=t[27]^f,Q=t[36]^h,J=t[37]^f,X=t[46]^h,tt=t[47]^f;h=a^(r<<1|i>>>31),f=u^(i<<1|r>>>31);let te=t[8]^h,tn=t[9]^f,tr=t[18]^h,ti=t[19]^f,t$=t[28]^h,to=t[29]^f,ts=t[38]^h,tx=t[39]^f,ta=t[48]^h,tu=t[49]^f,tc=d,tl=p,th=g<<4|y>>>28,tf=y<<4|g>>>28,td=b<<3|_>>>29,tp=_<<3|b>>>29,t0=v<<9|m>>>23,ty=m<<9|v>>>23,tg=w<<18|S>>>14,tb=S<<18|w>>>14,t_=k<<1|E>>>31,tm=E<<1|k>>>31,tv=M<<12|C>>>20,tw=C<<12|M>>>20,t8=I<<10|A>>>22,t1=A<<10|I>>>22,tS=R<<13|T>>>19,t2=T<<13|R>>>19,tk=L<<2|N>>>30,tE=N<<2|L>>>30,t6=D<<30|P>>>2,t3=P<<30|D>>>2,t4=O<<6|B>>>26,tC=B<<6|O>>>26,t5=U<<11|j>>>21,tM=j<<11|U>>>21,tI=F<<15|W>>>17,tA=W<<15|F>>>17,tT=H<<29|z>>>3,tR=z<<29|H>>>3,tL=V<<28|q>>>4,tN=q<<28|V>>>4,t7=G<<23|Z>>>9,tP=Z<<23|G>>>9,tD=Y<<25|K>>>7,tO=K<<25|Y>>>7,tB=Q<<21|J>>>11,tj=J<<21|Q>>>11,tU=tt<<24|X>>>8,tF=X<<24|tt>>>8,tW=te<<27|tn>>>5,tz=tn<<27|te>>>5,tH=tr<<20|ti>>>12,tV=ti<<20|tr>>>12,tq=to<<7|t$>>>25,tZ=t$<<7|to>>>25,tG=ts<<8|tx>>>24,tY=tx<<8|ts>>>24,tK=ta<<14|tu>>>18,tQ=tu<<14|ta>>>18;t[0]=tc^~tv&t5,t[1]=tl^~tw&tM,t[10]=tL^~tH&td,t[11]=tN^~tV&tp,t[20]=t_^~t4&tD,t[21]=tm^~tC&tO,t[30]=tW^~th&t8,t[31]=tz^~tf&t1,t[40]=t6^~t7&tq,t[41]=t3^~tP&tZ,t[2]=tv^~t5&tB,t[3]=tw^~tM&tj,t[12]=tH^~td&tS,t[13]=tV^~tp&t2,t[22]=t4^~tD&tG,t[23]=tC^~tO&tY,t[32]=th^~t8&tI,t[33]=tf^~t1&tA,t[42]=t7^~tq&t0,t[43]=tP^~tZ&ty,t[4]=t5^~tB&tK,t[5]=tM^~tj&tQ,t[14]=td^~tS&tT,t[15]=tp^~t2&tR,t[24]=tD^~tG&tg,t[25]=tO^~tY&tb,t[34]=t8^~tI&tU,t[35]=t1^~tA&tF,t[44]=tq^~t0&tk,t[45]=tZ^~ty&tE,t[6]=tB^~tK&tc,t[7]=tj^~tQ&tl,t[16]=tS^~tT&tL,t[17]=t2^~tR&tN,t[26]=tG^~tg&t_,t[27]=tY^~tb&tm,t[36]=tI^~tU&tW,t[37]=tA^~tF&tz,t[46]=t0^~tk&t6,t[47]=ty^~tE&t3,t[8]=tK^~tc&tv,t[9]=tQ^~tl&tw,t[18]=tT^~tL&tH,t[19]=tR^~tN&tV,t[28]=tg^~t_&t4,t[29]=tb^~tm&tC,t[38]=tU^~tW&th,t[39]=tF^~tz&tf,t[48]=tk^~t6&t7,t[49]=tE^~t3&tP,t[0]^=n[2*e],t[1]^=n[2*e+1]}}},9653(t,e,n){var r=n(8764).Buffer;let i=n(4040);function $(){this.state=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.blockSize=null,this.count=0,this.squeezing=!1}$.prototype.initialize=function(t,e){for(let n=0;n<50;++n)this.state[n]=0;this.blockSize=t/8,this.count=0,this.squeezing=!1},$.prototype.absorb=function(t){for(let e=0;e<t.length;++e)this.state[~~(this.count/4)]^=t[e]<<this.count%4*8,this.count+=1,this.count===this.blockSize&&(i.p1600(this.state),this.count=0)},$.prototype.absorbLastFewBits=function(t){this.state[~~(this.count/4)]^=t<<this.count%4*8,0!=(128&t)&&this.count===this.blockSize-1&&i.p1600(this.state),this.state[~~((this.blockSize-1)/4)]^=128<<(this.blockSize-1)%4*8,i.p1600(this.state),this.count=0,this.squeezing=!0},$.prototype.squeeze=function(t){this.squeezing||this.absorbLastFewBits(1);let e=r.alloc(t);for(let n=0;n<t;++n)e[n]=this.state[~~(this.count/4)]>>>this.count%4*8&255,this.count+=1,this.count===this.blockSize&&(i.p1600(this.state),this.count=0);return e},$.prototype.copy=function(t){for(let e=0;e<50;++e)t.state[e]=this.state[e];t.blockSize=this.blockSize,t.count=this.count,t.squeezing=this.squeezing},t.exports=$},631(t,e,n){var r="function"==typeof Map&&Map.prototype,i=Object.getOwnPropertyDescriptor&&r?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,$=r&&i&&"function"==typeof i.get?i.get:null,o=r&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,x=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,a=s&&x&&"function"==typeof x.get?x.get:null,u=s&&Set.prototype.forEach,c="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,l="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,h="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,f=Boolean.prototype.valueOf,d=Object.prototype.toString,p=Function.prototype.toString,y=String.prototype.match,g=String.prototype.slice,b=String.prototype.replace,_=String.prototype.toUpperCase,m=String.prototype.toLowerCase,v=RegExp.prototype.test,w=Array.prototype.concat,S=Array.prototype.join,k=Array.prototype.slice,E=Math.floor,C="function"==typeof BigInt?BigInt.prototype.valueOf:null,M=Object.getOwnPropertySymbols,I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,A="function"==typeof Symbol&&"object"==typeof Symbol.iterator,T="function"==typeof Symbol&&Symbol.toStringTag?Symbol.toStringTag:null,R=Object.prototype.propertyIsEnumerable,L=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([]["__proto__"]===Array.prototype?function(t){return t.__proto__}:null);function N(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||v.call(/e/,e))return e;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var r=t<0?-E(-t):E(t);if(r!==t){var i=String(r),$=g.call(e,i.length+1);return b.call(i,n,"$&_")+"."+b.call(b.call($,/([0-9]{3})/g,"$&_"),/_$/,"")}}return b.call(e,n,"$&_")}var P=n(4654),D=P.custom,O=W(D)?D:null;function B(t,e,n){var r="double"===(n.quoteStyle||e)?'"':"'";return r+t+r}function j(t){return b.call(String(t),/"/g,"&quot;")}function U(t){return!("[object Array]"!==V(t)||T&&"object"==typeof t&&T in t)}function F(t){return!("[object RegExp]"!==V(t)||T&&"object"==typeof t&&T in t)}function W(t){if(A)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!I)return!1;try{return I.call(t),!0}catch(e){}return!1}t.exports=function t(e,n,r,i){var s,x,d,_,v,E=n||{};if(H(E,"quoteStyle")&&"single"!==E.quoteStyle&&"double"!==E.quoteStyle)throw TypeError('option "quoteStyle" must be "single" or "double"');if(H(E,"maxStringLength")&&("number"==typeof E.maxStringLength?E.maxStringLength<0&&E.maxStringLength!==1/0:null!==E.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var M=!H(E,"customInspect")||E.customInspect;if("boolean"!=typeof M&&"symbol"!==M)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(H(E,"indent")&&null!==E.indent&&"	"!==E.indent&&!(parseInt(E.indent,10)===E.indent&&E.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(H(E,"numericSeparator")&&"boolean"!=typeof E.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var D=E.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return Z(e,E);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var z=String(e);return D?N(e,z):z}if("bigint"==typeof e){var G=String(e)+"n";return D?N(e,G):G}var tt=void 0===E.depth?5:E.depth;if(void 0===r&&(r=0),r>=tt&&tt>0&&"object"==typeof e)return U(e)?"[Array]":"[Object]";var te,tn=function(t,e){var n;if("	"===t.indent)n="	";else{if(!("number"==typeof t.indent&&t.indent>0))return null;n=S.call(Array(t.indent+1)," ")}return{base:n,prev:S.call(Array(e+1),n)}}(E,r);if(void 0===i)i=[];else if(q(i,e)>=0)return"[Circular]";function tr(e,n,$){if(n&&(i=k.call(i)).push(n),$){var o={depth:E.depth};return H(E,"quoteStyle")&&(o.quoteStyle=E.quoteStyle),t(e,o,r+1,i)}return t(e,E,r+1,i)}if("function"==typeof e&&!F(e)){var ti=function(t){if(t.name)return t.name;var e=y.call(p.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(e),t$=X(e,tr);return"[Function"+(ti?": "+ti:" (anonymous)")+"]"+(t$.length>0?" { "+S.call(t$,", ")+" }":"")}if(W(e)){var to=A?b.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):I.call(e);return"object"!=typeof e||A?to:Y(to)}if((te=e)&&"object"==typeof te&&("undefined"!=typeof HTMLElement&&te instanceof HTMLElement||"string"==typeof te.nodeName&&"function"==typeof te.getAttribute)){for(var ts="<"+m.call(String(e.nodeName)),tx=e.attributes||[],ta=0;ta<tx.length;ta++)ts+=" "+tx[ta].name+"="+B(j(tx[ta].value),"double",E);return ts+=">",e.childNodes&&e.childNodes.length&&(ts+="..."),ts+"</"+m.call(String(e.nodeName))+">"}if(U(e)){if(0===e.length)return"[]";var tu=X(e,tr);return tn&&!function(t){for(var e=0;e<t.length;e++)if(q(t[e],"\n")>=0)return!1;return!0}(tu)?"["+J(tu,tn)+"]":"[ "+S.call(tu,", ")+" ]"}if(s=e,!("[object Error]"!==V(s)||T&&"object"==typeof s&&T in s)){var tc=X(e,tr);return"cause"in Error.prototype||!("cause"in e)||R.call(e,"cause")?0===tc.length?"["+String(e)+"]":"{ ["+String(e)+"] "+S.call(tc,", ")+" }":"{ ["+String(e)+"] "+S.call(w.call("[cause]: "+tr(e.cause),tc),", ")+" }"}if("object"==typeof e&&M){if(O&&"function"==typeof e[O]&&P)return P(e,{depth:tt-r});if("symbol"!==M&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!$||!t||"object"!=typeof t)return!1;try{$.call(t);try{a.call(t)}catch(e){return!0}return t instanceof Map}catch(n){}return!1}(e)){var tl=[];return o.call(e,function(t,n){tl.push(tr(n,e,!0)+" => "+tr(t,e))}),Q("Map",$.call(e),tl,tn)}if(function(t){if(!a||!t||"object"!=typeof t)return!1;try{a.call(t);try{$.call(t)}catch(e){return!0}return t instanceof Set}catch(n){}return!1}(e)){var th=[];return u.call(e,function(t){th.push(tr(t,e))}),Q("Set",a.call(e),th,tn)}if(function(t){if(!c||!t||"object"!=typeof t)return!1;try{c.call(t,c);try{l.call(t,l)}catch(e){return!0}return t instanceof WeakMap}catch(n){}return!1}(e))return K("WeakMap");if(function(t){if(!l||!t||"object"!=typeof t)return!1;try{l.call(t,l);try{c.call(t,c)}catch(e){return!0}return t instanceof WeakSet}catch(n){}return!1}(e))return K("WeakSet");if(function(t){if(!h||!t||"object"!=typeof t)return!1;try{return h.call(t),!0}catch(e){}return!1}(e))return K("WeakRef");if(x=e,!("[object Number]"!==V(x)||T&&"object"==typeof x&&T in x))return Y(tr(Number(e)));if(function(t){if(!t||"object"!=typeof t||!C)return!1;try{return C.call(t),!0}catch(e){}return!1}(e))return Y(tr(C.call(e)));if(d=e,!("[object Boolean]"!==V(d)||T&&"object"==typeof d&&T in d))return Y(f.call(e));if(_=e,!("[object String]"!==V(_)||T&&"object"==typeof _&&T in _))return Y(tr(String(e)));if(v=e,("[object Date]"!==V(v)||T&&"object"==typeof v&&T in v)&&!F(e)){var tf=X(e,tr),td=L?L(e)===Object.prototype:e instanceof Object||e.constructor===Object,tp=e instanceof Object?"":"null prototype",t0=!td&&T&&Object(e)===e&&T in e?g.call(V(e),8,-1):tp?"Object":"",ty=(td||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(t0||tp?"["+S.call(w.call([],t0||[],tp||[]),": ")+"] ":"");return 0===tf.length?ty+"{}":tn?ty+"{"+J(tf,tn)+"}":ty+"{ "+S.call(tf,", ")+" }"}return String(e)};var z=Object.prototype.hasOwnProperty||function(t){return t in this};function H(t,e){return z.call(t,e)}function V(t){return d.call(t)}function q(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return -1}function Z(t,e){if(t.length>e.maxStringLength){var n=t.length-e.maxStringLength,r="... "+n+" more character"+(n>1?"s":"");return Z(g.call(t,0,e.maxStringLength),e)+r}return B(b.call(b.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,G),"single",e)}function G(t){var e=t.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return n?"\\"+n:"\\x"+(e<16?"0":"")+_.call(e.toString(16))}function Y(t){return"Object("+t+")"}function K(t){return t+" { ? }"}function Q(t,e,n,r){return t+" ("+e+") {"+(r?J(n,r):S.call(n,", "))+"}"}function J(t,e){if(0===t.length)return"";var n="\n"+e.prev+e.base;return n+S.call(t,","+n)+"\n"+e.prev}function X(t,e){var n=U(t),r=[];if(n){r.length=t.length;for(var i=0;i<t.length;i++)r[i]=H(t,i)?e(t[i],t):""}var $,o="function"==typeof M?M(t):[];if(A){$={};for(var s=0;s<o.length;s++)$["$"+o[s]]=o[s]}for(var x in t)H(t,x)&&(n&&String(Number(x))===x&&x<t.length||A&&$["$"+x]instanceof Symbol||(v.call(/[^\w$]/,x)?r.push(e(x,t)+": "+e(t[x],t)):r.push(x+": "+e(t[x],t))));if("function"==typeof M)for(var a=0;a<o.length;a++)R.call(t,o[a])&&r.push("["+e(o[a])+"]: "+e(t[o[a]],t));return r}},2352(t){"use strict";let e=(t,e)=>function(){let n=e.promiseModule,r=Array(arguments.length);for(let i=0;i<arguments.length;i++)r[i]=arguments[i];return new n((n,i)=>{e.errorFirst?r.push(function(t,r){if(e.multiArgs){let $=Array(arguments.length-1);for(let o=1;o<arguments.length;o++)$[o-1]=arguments[o];t?($.unshift(t),i($)):n($)}else t?i(t):n(r)}):r.push(function(t){if(e.multiArgs){let r=Array(arguments.length-1);for(let i=0;i<arguments.length;i++)r[i]=arguments[i];n(r)}else n(t)}),t.apply(this,r)})};t.exports=(t,n)=>{n=Object.assign({exclude:[/.+(Sync|Stream)$/],errorFirst:!0,promiseModule:Promise},n);let r=t=>{let e=e=>"string"==typeof e?t===e:e.test(t);return n.include?n.include.some(e):!n.exclude.some(e)},i;for(let $ in i="function"==typeof t?function(){return n.excludeMain?t.apply(this,arguments):e(t,n).apply(this,arguments)}:Object.create(Object.getPrototypeOf(t)),t){let o=t[$];i[$]="function"==typeof o&&r($)?e(o,n):o}return i}},6400(t,e,n){"use strict";n.r(e),n.d(e,{Component:()=>_,Fragment:()=>b,cloneElement:()=>F,createContext:()=>W,createElement:()=>p,createRef:()=>g,h:()=>p,hydrate:()=>U,isValidElement:()=>o,options:()=>i,render:()=>j,toChildArray:()=>C});var r,i,$,o,s,x,a,u,c={},l=[],h=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function f(t,e){for(var n in e)t[n]=e[n];return t}function d(t){var e=t.parentNode;e&&e.removeChild(t)}function p(t,e,n){var i,$,o,s={};for(o in e)"key"==o?i=e[o]:"ref"==o?$=e[o]:s[o]=e[o];if(arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===s[o]&&(s[o]=t.defaultProps[o]);return y(t,s,i,$,null)}function y(t,e,n,r,o){var s={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++$:o};return null==o&&null!=i.vnode&&i.vnode(s),s}function g(){return{current:null}}function b(t){return t.children}function _(t,e){this.props=t,this.context=e}function m(t,e){if(null==e)return t.__?m(t.__,t.__.__k.indexOf(t)+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?m(t):null}function v(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return v(t)}}function w(t){(!t.__d&&(t.__d=!0)&&s.push(t)&&!S.__r++||a!==i.debounceRendering)&&((a=i.debounceRendering)||x)(S)}function S(){for(var t;S.__r=s.length;)t=s.sort(function(t,e){return t.__v.__b-e.__v.__b}),s=[],t.some(function(t){var e,n,r,i,$,o;t.__d&&($=(i=(e=t).__v).__e,(o=e.__P)&&(n=[],(r=f({},i)).__v=i.__v+1,L(o,i,r,e.__n,void 0!==o.ownerSVGElement,null!=i.__h?[$]:null,n,null==$?m(i):$,i.__h),N(n,i),i.__e!=$&&v(i)))})}function k(t,e,n,r,i,$,o,s,x,a){var u,h,f,d,p,g,_,v=r&&r.__k||l,w=v.length;for(n.__k=[],u=0;u<e.length;u++)if(null!=(d=n.__k[u]=null==(d=e[u])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?y(null,d,null,null,d):Array.isArray(d)?y(b,{children:d},null,null,null):d.__b>0?y(d.type,d.props,d.key,null,d.__v):d)){if(d.__=n,d.__b=n.__b+1,null===(f=v[u])||f&&d.key==f.key&&d.type===f.type)v[u]=void 0;else for(h=0;h<w;h++){if((f=v[h])&&d.key==f.key&&d.type===f.type){v[h]=void 0;break}f=null}L(t,d,f=f||c,i,$,o,s,x,a),p=d.__e,(h=d.ref)&&f.ref!=h&&(_||(_=[]),f.ref&&_.push(f.ref,null,d),_.push(h,d.__c||p,d)),null!=p?(null==g&&(g=p),"function"==typeof d.type&&d.__k===f.__k?d.__d=x=E(d,x,t):x=M(t,d,f,v,p,x),"function"==typeof n.type&&(n.__d=x)):x&&f.__e==x&&x.parentNode!=t&&(x=m(f))}for(n.__e=g,u=w;u--;)null!=v[u]&&("function"==typeof n.type&&null!=v[u].__e&&v[u].__e==n.__d&&(n.__d=m(r,u+1)),O(v[u],v[u]));if(_)for(u=0;u<_.length;u++)D(_[u],_[++u],_[++u])}function E(t,e,n){for(var r,i=t.__k,$=0;i&&$<i.length;$++)(r=i[$])&&(r.__=t,e="function"==typeof r.type?E(r,e,n):M(n,r,r,i,r.__e,e));return e}function C(t,e){return e=e||[],null==t||"boolean"==typeof t||(Array.isArray(t)?t.some(function(t){C(t,e)}):e.push(t)),e}function M(t,e,n,r,i,$){var o,s,x;if(void 0!==e.__d)o=e.__d,e.__d=void 0;else if(null==n||i!=$||null==i.parentNode)_0x559625:if(null==$||$.parentNode!==t)t.appendChild(i),o=null;else{for(s=$,x=0;(s=s.nextSibling)&&x<r.length;x+=2)if(s==i)break _0x559625;t.insertBefore(i,$),o=$}return void 0!==o?o:i.nextSibling}function I(t,e,n){"-"===e[0]?t.setProperty(e,n):t[e]=null==n?"":"number"!=typeof n||h.test(e)?n:n+"px"}function A(t,e,n,r,i){var $;_0x3d4bc6:if("style"===e){if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||I(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||I(t.style,e,n[e])}}else if("o"===e[0]&&"n"===e[1])$=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase() in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+$]=n,n?r||t.addEventListener(e,$?R:T,$):t.removeEventListener(e,$?R:T,$);else if("dangerouslySetInnerHTML"!==e){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==e&&"list"!==e&&"form"!==e&&"tabIndex"!==e&&"download"!==e&&e in t)try{t[e]=null==n?"":n;break _0x3d4bc6}catch(o){}"function"==typeof n||(null!=n&&(!1!==n||"a"===e[0]&&"r"===e[1])?t.setAttribute(e,n):t.removeAttribute(e))}}function T(t){this.l[t.type+!1](i.event?i.event(t):t)}function R(t){this.l[t.type+!0](i.event?i.event(t):t)}function L(t,e,n,r,$,o,s,x,a){var u,c,l,h,d,p,y,g,m,v,w,S,E,C=e.type;if(void 0!==e.constructor)return null;null!=n.__h&&(a=n.__h,x=e.__e=n.__e,e.__h=null,o=[x]),(u=i.__b)&&u(e);try{_0x4c21d0:if("function"==typeof C){if(g=e.props,m=(u=C.contextType)&&r[u.__c],v=u?m?m.props.value:u.__:r,n.__c?y=(c=e.__c=n.__c).__=c.__E:("prototype"in C&&C.prototype.render?e.__c=c=new C(g,v):(e.__c=c=new _(g,v),c.constructor=C,c.render=B),m&&m.sub(c),c.props=g,c.state||(c.state={}),c.context=v,c.__n=r,l=c.__d=!0,c.__h=[]),null==c.__s&&(c.__s=c.state),null!=C.getDerivedStateFromProps&&(c.__s==c.state&&(c.__s=f({},c.__s)),f(c.__s,C.getDerivedStateFromProps(g,c.__s))),h=c.props,d=c.state,l)null==C.getDerivedStateFromProps&&null!=c.componentWillMount&&c.componentWillMount(),null!=c.componentDidMount&&c.__h.push(c.componentDidMount);else{if(null==C.getDerivedStateFromProps&&g!==h&&null!=c.componentWillReceiveProps&&c.componentWillReceiveProps(g,v),!c.__e&&null!=c.shouldComponentUpdate&&!1===c.shouldComponentUpdate(g,c.__s,v)||e.__v===n.__v){c.props=g,c.state=c.__s,e.__v!==n.__v&&(c.__d=!1),c.__v=e,e.__e=n.__e,e.__k=n.__k,e.__k.forEach(function(t){t&&(t.__=e)}),c.__h.length&&s.push(c);break _0x4c21d0}null!=c.componentWillUpdate&&c.componentWillUpdate(g,c.__s,v),null!=c.componentDidUpdate&&c.__h.push(function(){c.componentDidUpdate(h,d,p)})}if(c.context=v,c.props=g,c.__v=e,c.__P=t,w=i.__r,S=0,"prototype"in C&&C.prototype.render)c.state=c.__s,c.__d=!1,w&&w(e),u=c.render(c.props,c.state,c.context);else do c.__d=!1,w&&w(e),u=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++S<25);c.state=c.__s,null!=c.getChildContext&&(r=f(f({},r),c.getChildContext())),l||null==c.getSnapshotBeforeUpdate||(p=c.getSnapshotBeforeUpdate(h,d)),E=null!=u&&u.type===b&&null==u.key?u.props.children:u,k(t,Array.isArray(E)?E:[E],e,n,r,$,o,s,x,a),c.base=e.__e,e.__h=null,c.__h.length&&s.push(c),y&&(c.__E=c.__=null),c.__e=!1}else null==o&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):e.__e=P(n.__e,e,n,r,$,o,s,a);(u=i.diffed)&&u(e)}catch(M){e.__v=null,(a||null!=o)&&(e.__e=x,e.__h=!!a,o[o.indexOf(x)]=null),i.__e(M,e,n)}}function N(t,e){i.__c&&i.__c(e,t),t.some(function(e){try{t=e.__h,e.__h=[],t.some(function(t){t.call(e)})}catch(n){i.__e(n,e.__v)}})}function P(t,e,n,i,$,o,s,x){var a,u,l,h=n.props,f=e.props,p=e.type,y=0;if("svg"===p&&($=!0),null!=o){for(;y<o.length;y++)if((a=o[y])&&"setAttribute"in a==!!p&&(p?a.localName===p:3===a.nodeType)){t=a,o[y]=null;break}}if(null==t){if(null===p)return document.createTextNode(f);t=$?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,f.is&&f),o=null,x=!1}if(null===p)h===f||x&&t.data===f||(t.data=f);else{if(o=o&&r.call(t.childNodes),u=(h=n.props||c).dangerouslySetInnerHTML,l=f.dangerouslySetInnerHTML,!x){if(null!=o)for(h={},y=0;y<t.attributes.length;y++)h[t.attributes[y].name]=t.attributes[y].value;(l||u)&&(l&&(u&&l.__html==u.__html||l.__html===t.innerHTML)||(t.innerHTML=l&&l.__html||""))}if(function(t,e,n,r,i){var $;for($ in n)"children"===$||"key"===$||$ in e||A(t,$,null,n[$],r);for($ in e)i&&"function"!=typeof e[$]||"children"===$||"key"===$||"value"===$||"checked"===$||n[$]===e[$]||A(t,$,e[$],n[$],r)}(t,f,h,$,x),l)e.__k=[];else if(k(t,Array.isArray(y=e.props.children)?y:[y],e,n,i,$&&"foreignObject"!==p,o,s,o?o[0]:n.__k&&m(n,0),x),null!=o)for(y=o.length;y--;)null!=o[y]&&d(o[y]);x||("value"in f&&void 0!==(y=f.value)&&(y!==t.value||"progress"===p&&!y||"option"===p&&y!==h.value)&&A(t,"value",y,h.value,!1),"checked"in f&&void 0!==(y=f.checked)&&y!==t.checked&&A(t,"checked",y,h.checked,!1))}return t}function D(t,e,n){try{"function"==typeof t?t(e):t.current=e}catch(r){i.__e(r,n)}}function O(t,e,n){var r,$;if(i.unmount&&i.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||D(r,null,e)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(o){i.__e(o,e)}r.base=r.__P=null}if(r=t.__k)for($=0;$<r.length;$++)r[$]&&O(r[$],e,"function"!=typeof t.type);n||null==t.__e||d(t.__e),t.__e=t.__d=void 0}function B(t,e,n){return this.constructor(t,n)}function j(t,e,n){var $,o,s;i.__&&i.__(t,e),o=($="function"==typeof n)?null:n&&n.__k||e.__k,s=[],L(e,t=(!$&&n||e).__k=p(b,null,[t]),o||c,c,void 0!==e.ownerSVGElement,!$&&n?[n]:o?null:e.firstChild?r.call(e.childNodes):null,s,!$&&n?n:o?o.__e:e.firstChild,$),N(s,t)}function U(t,e){j(t,e,U)}function F(t,e,n){var i,$,o,s=f({},t.props);for(o in e)"key"==o?i=e[o]:"ref"==o?$=e[o]:s[o]=e[o];return arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):n),y(t.type,s,i||t.key,$||t.ref,null)}function W(t,e){var n={__c:e="__cC"+u++,__:t,Consumer:function(t,e){return t.children(e)},Provider:function(t){var n,r;return this.getChildContext||(n=[],(r={})[e]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&n.some(w)},this.sub=function(t){n.push(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n.splice(n.indexOf(t),1),e&&e.call(t)}}),t.children}};return n.Provider.__=n.Consumer.contextType=n}r=l.slice,i={__e:function(t,e,n,r){for(var i,$,o;e=e.__;)if((i=e.__c)&&!i.__)try{if(($=i.constructor)&&null!=$.getDerivedStateFromError&&(i.setState($.getDerivedStateFromError(t)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),o=i.__d),o)return i.__E=i}catch(s){t=s}throw t}},$=0,o=function(t){return null!=t&&void 0===t.constructor},_.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=f({},this.state),"function"==typeof t&&(t=t(f({},n),this.props)),t&&f(n,t),null!=t&&this.__v&&(e&&this.__h.push(e),w(this))},_.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),w(this))},_.prototype.render=b,s=[],x="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,S.__r=0,u=0},396(t,e,n){"use strict";n.r(e),n.d(e,{useCallback:()=>w,useContext:()=>S,useDebugValue:()=>k,useEffect:()=>g,useErrorBoundary:()=>E,useImperativeHandle:()=>m,useLayoutEffect:()=>b,useMemo:()=>v,useReducer:()=>y,useRef:()=>_,useState:()=>p});var r,i,$,o,s=n(6400),x=0,a=[],u=s.options.__b,c=s.options.__r,l=s.options.diffed,h=s.options.__c,f=s.options.unmount;function d(t,e){s.options.__h&&s.options.__h(i,t,x||e),x=0;var n=i.__H||(i.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function p(t){return x=1,y(R,t)}function y(t,e,n){var $=d(r++,2);return $.t=t,$.__c||($.__=[n?n(e):R(void 0,e),function(t){var e=$.t($.__[0],t);$.__[0]!==e&&($.__=[e,$.__[1]],$.__c.setState({}))}],$.__c=i),$.__}function g(t,e){var n=d(r++,3);!s.options.__s&&T(n.__H,e)&&(n.__=t,n.u=e,i.__H.__h.push(n))}function b(t,e){var n=d(r++,4);!s.options.__s&&T(n.__H,e)&&(n.__=t,n.u=e,i.__h.push(n))}function _(t){return x=5,v(function(){return{current:t}},[])}function m(t,e,n){x=6,b(function(){return"function"==typeof t?(t(e()),function(){return t(null)}):t?(t.current=e(),function(){return t.current=null}):void 0},null==n?n:n.concat(t))}function v(t,e){var n=d(r++,7);return T(n.__H,e)?(n.o=t(),n.u=e,n.__h=t,n.o):n.__}function w(t,e){return x=8,v(function(){return t},e)}function S(t){var e=i.context[t.__c],n=d(r++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(i)),e.props.value):t.__}function k(t,e){s.options.useDebugValue&&s.options.useDebugValue(e?e(t):t)}function E(t){var e=d(r++,10),n=p();return e.__=t,i.componentDidCatch||(i.componentDidCatch=function(t){e.__&&e.__(t),n[1](t)}),[n[0],function(){n[1](void 0)}]}function C(){for(var t;t=a.shift();)if(t.__P)try{t.__H.__h.forEach(I),t.__H.__h.forEach(A),t.__H.__h=[]}catch(e){t.__H.__h=[],s.options.__e(e,t.__v)}}s.options.__b=function(t){i=null,u&&u(t)},s.options.__r=function(t){c&&c(t),r=0;var e=(i=t.__c).__H;e&&($===i?(e.__h=[],i.__h=[],e.__.forEach(function(t){t.o=t.u=void 0})):(e.__.forEach(function(t){t.u&&(t.__H=t.u),t.o&&(t.__=t.o),t.o=t.u=void 0}),e.__h.forEach(I),e.__h.forEach(A),e.__h=[])),$=i},s.options.diffed=function(t){l&&l(t);var e=t.__c;e&&e.__H&&e.__H.__h.length&&(1!==a.push(e)&&o===s.options.requestAnimationFrame||((o=s.options.requestAnimationFrame)||function(t){var e,n=function(){clearTimeout(r),M&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);M&&(e=requestAnimationFrame(n))})(C)),i=null,$=null},s.options.__c=function(t,e){e.some(function(t){try{t.__H&&t.__H.__.forEach(function(t){t.u&&(t.__H=t.u),t.o&&(t.__=t.o),t.o=t.u=void 0}),t.__h.forEach(I),t.__h=t.__h.filter(function(t){return!t.__||A(t)})}catch(n){e.some(function(t){t.__h&&(t.__h=[])}),e=[],s.options.__e(n,t.__v)}}),h&&h(t,e)},s.options.unmount=function(t){f&&f(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach(function(t){try{I(t)}catch(n){e=n}}),e&&s.options.__e(e,n.__v))};var M="function"==typeof requestAnimationFrame;function I(t){var e=i,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),i=e}function A(t){var e=i;t.__c=t.__(),i=e}function T(t,e){return!t||t.length!==e.length||e.some(function(e,n){return e!==t[n]})}function R(t,e){return"function"==typeof e?e(t):e}},5798(t){"use strict";var e=String.prototype.replace,n=/%20/g,r="RFC3986";t.exports={default:r,formatters:{RFC1738:function(t){return e.call(t,n,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:r}},129(t,e,n){"use strict";var r=n(8261),i=n(5235),$=n(5798);t.exports={formats:$,parse:i,stringify:r}},5235(t,e,n){"use strict";var r=n(2769),i=Object.prototype.hasOwnProperty,$=Array.isArray,o={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:r.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},s=function(t){return t.replace(/&#(\d+);/g,function(t,e){return String.fromCharCode(parseInt(e,10))})},x=function(t,e){return t&&"string"==typeof t&&e.comma&&t.indexOf(",")>-1?t.split(","):t},a=function(t,e,n,r){if(t){var $=n.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,o=/(\[[^[\]]*])/g,s=n.depth>0&&/(\[[^[\]]*])/.exec($),a=s?$.slice(0,s.index):$,u=[];if(a){if(!n.plainObjects&&i.call(Object.prototype,a)&&!n.allowPrototypes)return;u.push(a)}for(var c=0;n.depth>0&&null!==(s=o.exec($))&&c<n.depth;){if(c+=1,!n.plainObjects&&i.call(Object.prototype,s[1].slice(1,-1))&&!n.allowPrototypes)return;u.push(s[1])}return s&&u.push("["+$.slice(s.index)+"]"),function(t,e,n,r){for(var i=r?e:x(e,n),$=t.length-1;$>=0;--$){var o,s=t[$];if("[]"===s&&n.parseArrays)o=[].concat(i);else{o=n.plainObjects?Object.create(null):{};var a="["===s.charAt(0)&&"]"===s.charAt(s.length-1)?s.slice(1,-1):s,u=parseInt(a,10);n.parseArrays||""!==a?!isNaN(u)&&s!==a&&String(u)===a&&u>=0&&n.parseArrays&&u<=n.arrayLimit?(o=[])[u]=i:"__proto__"!==a&&(o[a]=i):o={0:i}}i=o}return i}(u,e,n,r)}};t.exports=function(t,e){var n=function(t){if(!t)return o;if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var e=void 0===t.charset?o.charset:t.charset;return{allowDots:void 0===t.allowDots?o.allowDots:!!t.allowDots,allowPrototypes:"boolean"==typeof t.allowPrototypes?t.allowPrototypes:o.allowPrototypes,allowSparse:"boolean"==typeof t.allowSparse?t.allowSparse:o.allowSparse,arrayLimit:"number"==typeof t.arrayLimit?t.arrayLimit:o.arrayLimit,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:o.charsetSentinel,comma:"boolean"==typeof t.comma?t.comma:o.comma,decoder:"function"==typeof t.decoder?t.decoder:o.decoder,delimiter:"string"==typeof t.delimiter||r.isRegExp(t.delimiter)?t.delimiter:o.delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:o.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof t.interpretNumericEntities?t.interpretNumericEntities:o.interpretNumericEntities,parameterLimit:"number"==typeof t.parameterLimit?t.parameterLimit:o.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"==typeof t.plainObjects?t.plainObjects:o.plainObjects,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:o.strictNullHandling}}(e);if(""===t||null==t)return n.plainObjects?Object.create(null):{};for(var u="string"==typeof t?function(t,e){var n,a={},u=e.ignoreQueryPrefix?t.replace(/^\?/,""):t,c=e.parameterLimit===1/0?void 0:e.parameterLimit,l=u.split(e.delimiter,c),h=-1,f=e.charset;if(e.charsetSentinel)for(n=0;n<l.length;++n)0===l[n].indexOf("utf8=")&&("utf8=%E2%9C%93"===l[n]?f="utf-8":"utf8=%26%2310003%3B"===l[n]&&(f="iso-8859-1"),h=n,n=l.length);for(n=0;n<l.length;++n)if(n!==h){var d,p,y=l[n],g=y.indexOf("]="),b=-1===g?y.indexOf("="):g+1;-1===b?(d=e.decoder(y,o.decoder,f,"key"),p=e.strictNullHandling?null:""):(d=e.decoder(y.slice(0,b),o.decoder,f,"key"),p=r.maybeMap(x(y.slice(b+1),e),function(t){return e.decoder(t,o.decoder,f,"value")})),p&&e.interpretNumericEntities&&"iso-8859-1"===f&&(p=s(p)),y.indexOf("[]=")>-1&&(p=$(p)?[p]:p),i.call(a,d)?a[d]=r.combine(a[d],p):a[d]=p}return a}(t,n):t,c=n.plainObjects?Object.create(null):{},l=Object.keys(u),h=0;h<l.length;++h){var f=l[h],d=a(f,u[f],n,"string"==typeof t);c=r.merge(c,d,n)}return!0===n.allowSparse?c:r.compact(c)}},8261(t,e,n){"use strict";var r=n(7478),i=n(2769),$=n(5798),o=Object.prototype.hasOwnProperty,s={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},x=Array.isArray,a=String.prototype.split,u=Array.prototype.push,c=function(t,e){u.apply(t,x(e)?e:[e])},l=Date.prototype.toISOString,h=$.default,f={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:i.encode,encodeValuesOnly:!1,format:h,formatter:$.formatters[h],indices:!1,serializeDate:function(t){return l.call(t)},skipNulls:!1,strictNullHandling:!1},d={},p=function t(e,n,$,o,s,u,l,h,p,y,g,b,_,m,v){for(var w,S=e,k=v,E=0,C=!1;void 0!==(k=k.get(d))&&!C;){var M=k.get(e);if(E+=1,void 0!==M){if(M===E)throw RangeError("Cyclic object value");C=!0}void 0===k.get(d)&&(E=0)}if("function"==typeof l?S=l(n,S):S instanceof Date?S=y(S):"comma"===$&&x(S)&&(S=i.maybeMap(S,function(t){return t instanceof Date?y(t):t})),null===S){if(o)return u&&!_?u(n,f.encoder,m,"key",g):n;S=""}if("string"==typeof(w=S)||"number"==typeof w||"boolean"==typeof w||"symbol"==typeof w||"bigint"==typeof w||i.isBuffer(S)){if(u){var I=_?n:u(n,f.encoder,m,"key",g);if("comma"===$&&_){for(var A=a.call(String(S),","),T="",R=0;R<A.length;++R)T+=(0===R?"":",")+b(u(A[R],f.encoder,m,"value",g));return[b(I)+(x(S)&&1===A.length?"[]":"")+"="+T]}return[b(I)+"="+b(u(S,f.encoder,m,"value",g))]}return[b(n)+"="+b(String(S))]}var L,N=[];if(void 0===S)return N;if("comma"===$&&x(S))L=[{value:S.length>0?S.join(",")||null:void 0}];else if(x(l))L=l;else{var P=Object.keys(S);L=h?P.sort(h):P}for(var D="comma"===$&&x(S)&&1===S.length?n+"[]":n,O=0;O<L.length;++O){var B=L[O],j="object"==typeof B&&void 0!==B.value?B.value:S[B];if(!s||null!==j){var U=x(S)?"function"==typeof $?$(D,B):D:D+(p?"."+B:"["+B+"]");v.set(e,E);var F=r();F.set(d,v),c(N,t(j,U,$,o,s,u,l,h,p,y,g,b,_,m,F))}}return N};t.exports=function(t,e){var n,i=t,a=function(t){if(!t)return f;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw TypeError("Encoder has to be a function.");var e=t.charset||f.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=$.default;if(void 0!==t.format){if(!o.call($.formatters,t.format))throw TypeError("Unknown format option provided.");n=t.format}var r=$.formatters[n],i=f.filter;return("function"==typeof t.filter||x(t.filter))&&(i=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:f.addQueryPrefix,allowDots:void 0===t.allowDots?f.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:f.charsetSentinel,delimiter:void 0===t.delimiter?f.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:f.encode,encoder:"function"==typeof t.encoder?t.encoder:f.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:f.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:f.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:f.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:f.strictNullHandling}}(e);"function"==typeof a.filter?i=(0,a.filter)("",i):x(a.filter)&&(n=a.filter);var u,l=[];if("object"!=typeof i||null===i)return"";var h=s[u=e&&e.arrayFormat in s?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices"];n||(n=Object.keys(i)),a.sort&&n.sort(a.sort);for(var d=r(),y=0;y<n.length;++y){var g=n[y];a.skipNulls&&null===i[g]||c(l,p(i[g],g,h,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,d))}var b=l.join(a.delimiter),_=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?_+="utf8=%26%2310003%3B&":_+="utf8=%E2%9C%93&"),b.length>0?_+b:""}},2769(t,e,n){"use strict";var r=n(5798),i=Object.prototype.hasOwnProperty,$=Array.isArray,o=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),s=function(t,e){for(var n=e&&e.plainObjects?Object.create(null):{},r=0;r<t.length;++r)void 0!==t[r]&&(n[r]=t[r]);return n};t.exports={arrayToObject:s,assign:function(t,e){return Object.keys(e).reduce(function(t,n){return t[n]=e[n],t},t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],n=[],r=0;r<e.length;++r)for(var i=e[r],o=i.obj[i.prop],s=Object.keys(o),x=0;x<s.length;++x){var a=s[x],u=o[a];"object"==typeof u&&null!==u&&-1===n.indexOf(u)&&(e.push({obj:o,prop:a}),n.push(u))}return function(t){for(;t.length>1;){var e=t.pop(),n=e.obj[e.prop];if($(n)){for(var r=[],i=0;i<n.length;++i)void 0!==n[i]&&r.push(n[i]);e.obj[e.prop]=r}}}(e),t},decode:function(t,e,n){var r=t.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(i){return r}},encode:function(t,e,n,i,$){if(0===t.length)return t;var s=t;if("symbol"==typeof t?s=Symbol.prototype.toString.call(t):"string"!=typeof t&&(s=String(t)),"iso-8859-1"===n)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"});for(var x="",a=0;a<s.length;++a){var u=s.charCodeAt(a);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||$===r.RFC1738&&(40===u||41===u)?x+=s.charAt(a):u<128?x+=o[u]:u<2048?x+=o[192|u>>6]+o[128|63&u]:u<55296||u>=57344?x+=o[224|u>>12]+o[128|u>>6&63]+o[128|63&u]:(a+=1,x+=o[240|(u=65536+((1023&u)<<10|1023&s.charCodeAt(a)))>>18]+o[128|u>>12&63]+o[128|u>>6&63]+o[128|63&u])}return x},isBuffer:function(t){return!(!t||"object"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if($(t)){for(var n=[],r=0;r<t.length;r+=1)n.push(e(t[r]));return n}return e(t)},merge:function t(e,n,r){if(!n)return e;if("object"!=typeof n){if($(e))e.push(n);else{if(!e||"object"!=typeof e)return[e,n];(r&&(r.plainObjects||r.allowPrototypes)||!i.call(Object.prototype,n))&&(e[n]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(n);var o=e;return $(e)&&!$(n)&&(o=s(e,r)),$(e)&&$(n)?(n.forEach(function(n,$){if(i.call(e,$)){var o=e[$];o&&"object"==typeof o&&n&&"object"==typeof n?e[$]=t(o,n,r):e.push(n)}else e[$]=n}),e):Object.keys(n).reduce(function(e,$){var o=n[$];return i.call(e,$)?e[$]=t(e[$],o,r):e[$]=o,e},o)}}},4281(t){"use strict";var e={};function n(t,n,r){r||(r=Error);var i=function(t){var e,r;function i(e,r,i){var $,o,s;return t.call(this,($=e,o=r,s=i,"string"==typeof n?n:n($,o,s)))||this}return r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,e.__proto__=r,i}(r);i.prototype.name=r.name,i.prototype.code=t,e[t]=i}function r(t,e){if(Array.isArray(t)){var n=t.length;return t=t.map(function(t){return String(t)}),n>2?"one of ".concat(e," ").concat(t.slice(0,n-1).join(", "),", or ")+t[n-1]:2===n?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}n("ERR_INVALID_OPT_VALUE",function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'},TypeError),n("ERR_INVALID_ARG_TYPE",function(t,e,n){var i,$,o,s,x,a,u,c=" argument";if("string"==typeof e&&($="not ",e.substr(0,$.length)===$)?(i="must not be",e=e.replace(/^not /,"")):i="must be",a=t,(void 0===u||u>a.length)&&(u=a.length),a.substring(u-c.length,u)===c)o="The ".concat(t," ").concat(i," ").concat(r(e,"type"));else{var l=("number"!=typeof x&&(x=0),x+1>(s=t).length||-1===s.indexOf(".",x)?"argument":"property");o='The "'.concat(t,'" ').concat(l," ").concat(i," ").concat(r(e,"type"))}return o+". Received type ".concat(typeof n)},TypeError),n("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),n("ERR_METHOD_NOT_IMPLEMENTED",function(t){return"The "+t+" method is not implemented"}),n("ERR_STREAM_PREMATURE_CLOSE","Premature close"),n("ERR_STREAM_DESTROYED",function(t){return"Cannot call "+t+" after a stream was destroyed"}),n("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),n("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),n("ERR_STREAM_WRITE_AFTER_END","write after end"),n("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),n("ERR_UNKNOWN_ENCODING",function(t){return"Unknown encoding: "+t},TypeError),n("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.q=e},6753(t,e,n){"use strict";var r=Object.keys||function(t){var e=[];for(var n in t)e.push(n);return e};t.exports=a;var i=n(9481),$=n(4229);n(5717)(a,i);for(var o=r($.prototype),s=0;s<o.length;s++){var x=o[s];a.prototype[x]||(a.prototype[x]=$.prototype[x])}function a(t){if(!(this instanceof a))return new a(t);i.call(this,t),$.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",u)))}function u(){this._writableState.ended||process.nextTick(c,this)}function c(t){t.end()}Object.defineProperty(a.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(a.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(a.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(a.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})},2725(t,e,n){"use strict";t.exports=i;var r=n(4605);function i(t){if(!(this instanceof i))return new i(t);r.call(this,t)}n(5717)(i,r),i.prototype._transform=function(t,e,n){n(null,t)}},9481(t,e,n){"use strict";t.exports=S,S.ReadableState=w,n(7187).EventEmitter;var r,i,$=function(t,e){return t.listeners(e).length},o=n(2503),s=n(8764).Buffer,x=n.g.Uint8Array||function(){},a=n(4616);i=a&&a.debuglog?a.debuglog("stream"):function(){};var u,c,l,h=n(7327),f=n(1195),d=n(2457).getHighWaterMark,p=n(4281).q,y=p.ERR_INVALID_ARG_TYPE,g=p.ERR_STREAM_PUSH_AFTER_EOF,b=p.ERR_METHOD_NOT_IMPLEMENTED,_=p.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;n(5717)(S,o);var m=f.errorOrDestroy,v=["error","close","destroy","pause","resume"];function w(t,e,i){r=r||n(6753),t=t||{},"boolean"!=typeof i&&(i=e instanceof r),this.objectMode=!!t.objectMode,i&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=d(this,t,"readableHighWaterMark",i),this.buffer=new h,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(u||(u=n(2553).s),this.decoder=new u(t.encoding),this.encoding=t.encoding)}function S(t){if(r=r||n(6753),!(this instanceof S))return new S(t);var e=this instanceof r;this._readableState=new w(t,this,e),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),o.call(this)}function k(t,e,n,r,$){i("readableAddChunk",e);var o,a,u,c,l,h,f=t._readableState;if(null===e)f.reading=!1,function(t,e){if(i("onEofChunk"),!e.ended){if(e.decoder){var n=e.decoder.end();n&&n.length&&(e.buffer.push(n),e.length+=e.objectMode?1:n.length)}e.ended=!0,e.sync?I(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,A(t)))}}(t,f);else if($||(h=(o=f,c=a=e,s.isBuffer(c)||c instanceof x||"string"==typeof a||void 0===a||o.objectMode||(u=new y("chunk",["string","Buffer","Uint8Array"],a)),u)),h)m(t,h);else if(f.objectMode||e&&e.length>0){if("string"==typeof e||f.objectMode||Object.getPrototypeOf(e)===s.prototype||(e=(l=e,s.from(l))),r)f.endEmitted?m(t,new _):E(t,f,e,!0);else if(f.ended)m(t,new g);else{if(f.destroyed)return!1;f.reading=!1,f.decoder&&!n?(e=f.decoder.write(e),f.objectMode||0!==e.length?E(t,f,e,!1):T(t,f)):E(t,f,e,!1)}}else r||(f.reading=!1,T(t,f));return!f.ended&&(f.length<f.highWaterMark||0===f.length)}function E(t,e,n,r){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",n)):(e.length+=e.objectMode?1:n.length,r?e.buffer.unshift(n):e.buffer.push(n),e.needReadable&&I(t)),T(t,e)}Object.defineProperty(S.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),S.prototype.destroy=f.destroy,S.prototype._undestroy=f.undestroy,S.prototype._destroy=function(t,e){e(t)},S.prototype.push=function(t,e){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof t&&((e=e||r.defaultEncoding)!==r.encoding&&(t=s.from(t,e),e=""),n=!0),k(this,t,e,!1,n)},S.prototype.unshift=function(t){return k(this,t,null,!0,!1)},S.prototype.isPaused=function(){return!1===this._readableState.flowing},S.prototype.setEncoding=function(t){u||(u=n(2553).s);var e=new u(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,i="";null!==r;)i+=e.write(r.data),r=r.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var C=1073741824;function M(t,e){var n;return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!=t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=((n=t)>=C?n=C:(n--,n|=n>>>1,n|=n>>>2,n|=n>>>4,n|=n>>>8,n|=n>>>16,n++),n)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function I(t){var e=t._readableState;i("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(i("emitReadable",e.flowing),e.emittedReadable=!0,process.nextTick(A,t))}function A(t){var e=t._readableState;i("emitReadable_",e.destroyed,e.length,e.ended),!e.destroyed&&(e.length||e.ended)&&(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,D(t)}function T(t,e){e.readingMore||(e.readingMore=!0,process.nextTick(R,t,e))}function R(t,e){for(;!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length);){var n=e.length;if(i("maybeReadMore read 0"),t.read(0),n===e.length)break}e.readingMore=!1}function L(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function N(t){i("readable nexttick read 0"),t.read(0)}function P(t,e){i("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),D(t),e.flowing&&!e.reading&&t.read(0)}function D(t){var e=t._readableState;for(i("flow",e.flowing);e.flowing&&null!==t.read(););}function O(t,e){var n;return 0===e.length?null:(e.objectMode?n=e.buffer.shift():!t||t>=e.length?(n=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):n=e.buffer.consume(t,e.decoder),n)}function B(t){var e=t._readableState;i("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,process.nextTick(j,e,t))}function j(t,e){if(i("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var n=e._writableState;(!n||n.autoDestroy&&n.finished)&&e.destroy()}}function U(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return -1}S.prototype.read=function(t){i("read",t),t=parseInt(t,10);var e=this._readableState,n=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return i("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?B(this):I(this),null;if(0===(t=M(t,e))&&e.ended)return 0===e.length&&B(this),null;var r,$=e.needReadable;return i("need readable",$),(0===e.length||e.length-t<e.highWaterMark)&&i("length less than watermark",$=!0),e.ended||e.reading?i("reading or ended",$=!1):$&&(i("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=M(n,e))),null===(r=t>0?O(t,e):null)?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),n!==t&&e.ended&&B(this)),null!==r&&this.emit("data",r),r},S.prototype._read=function(t){m(this,new b("_read()"))},S.prototype.pipe=function(t,e){var n,r=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=t;break;case 1:o.pipes=[o.pipes,t];break;default:o.pipes.push(t)}o.pipesCount+=1,i("pipe count=%d opts=%j",o.pipesCount,e);var s=e&&!1===e.end||t===process.stdout||t===process.stderr?d:x;function x(){i("onend"),t.end()}o.endEmitted?process.nextTick(s):r.once("end",s),t.on("unpipe",function e(n,$){i("onunpipe"),n===r&&$&&!1===$.hasUnpiped&&($.hasUnpiped=!0,i("cleanup"),t.removeListener("close",h),t.removeListener("finish",f),t.removeListener("drain",a),t.removeListener("error",l),t.removeListener("unpipe",e),r.removeListener("end",x),r.removeListener("end",d),r.removeListener("data",c),u=!0,o.awaitDrain&&(!t._writableState||t._writableState.needDrain)&&a())});var a=(n=r,function(){var t=n._readableState;i("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&$(n,"data")&&(t.flowing=!0,D(n))});t.on("drain",a);var u=!1;function c(e){i("ondata");var n=t.write(e);i("dest.write",n),!1===n&&((1===o.pipesCount&&o.pipes===t||o.pipesCount>1&&-1!==U(o.pipes,t))&&!u&&(i("false write response, pause",o.awaitDrain),o.awaitDrain++),r.pause())}function l(e){i("onerror",e),d(),t.removeListener("error",l),0===$(t,"error")&&m(t,e)}function h(){t.removeListener("finish",f),d()}function f(){i("onfinish"),t.removeListener("close",h),d()}function d(){i("unpipe"),r.unpipe(t)}return r.on("data",c),function(t,e,n){if("function"==typeof t.prependListener)return t.prependListener(e,n);t._events&&t._events.error?Array.isArray(t._events.error)?t._events.error.unshift(n):t._events.error=[n,t._events.error]:t.on(e,n)}(t,"error",l),t.once("close",h),t.once("finish",f),t.emit("pipe",r),o.flowing||(i("pipe resume"),r.resume()),t},S.prototype.unpipe=function(t){var e=this._readableState,n={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,n)),this;if(!t){var r=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var $=0;$<i;$++)r[$].emit("unpipe",this,{hasUnpiped:!1});return this}var o=U(e.pipes,t);return -1===o||(e.pipes.splice(o,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,n)),this},S.prototype.on=function(t,e){var n=o.prototype.on.call(this,t,e),r=this._readableState;return"data"===t?(r.readableListening=this.listenerCount("readable")>0,!1!==r.flowing&&this.resume()):"readable"===t&&(r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.flowing=!1,r.emittedReadable=!1,i("on readable",r.length,r.reading),r.length?I(this):r.reading||process.nextTick(N,this))),n},S.prototype.addListener=S.prototype.on,S.prototype.removeListener=function(t,e){var n=o.prototype.removeListener.call(this,t,e);return"readable"===t&&process.nextTick(L,this),n},S.prototype.removeAllListeners=function(t){var e=o.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||process.nextTick(L,this),e},S.prototype.resume=function(){var t,e,n=this._readableState;return n.flowing||(i("resume"),n.flowing=!n.readableListening,t=this,(e=n).resumeScheduled||(e.resumeScheduled=!0,process.nextTick(P,t,e))),n.paused=!1,this},S.prototype.pause=function(){return i("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(i("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},S.prototype.wrap=function(t){var e=this,n=this._readableState,r=!1;for(var $ in t.on("end",function(){if(i("wrapped end"),n.decoder&&!n.ended){var t=n.decoder.end();t&&t.length&&e.push(t)}e.push(null)}),t.on("data",function($){i("wrapped data"),n.decoder&&($=n.decoder.write($)),n.objectMode&&null==$||(n.objectMode||$&&$.length)&&(e.push($)||(r=!0,t.pause()))}),t)void 0===this[$]&&"function"==typeof t[$]&&(this[$]=function(e){return function(){return t[e].apply(t,arguments)}}($));for(var o=0;o<v.length;o++)t.on(v[o],this.emit.bind(this,v[o]));return this._read=function(e){i("wrapped _read",e),r&&(r=!1,t.resume())},this},"function"==typeof Symbol&&(S.prototype[Symbol.asyncIterator]=function(){return void 0===c&&(c=n(5850)),c(this)}),Object.defineProperty(S.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(S.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(S.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),S._fromList=O,Object.defineProperty(S.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(S.from=function(t,e){return void 0===l&&(l=n(5167)),l(S,t,e)})},4605(t,e,n){"use strict";t.exports=u;var r=n(4281).q,i=r.ERR_METHOD_NOT_IMPLEMENTED,$=r.ERR_MULTIPLE_CALLBACK,o=r.ERR_TRANSFORM_ALREADY_TRANSFORMING,s=r.ERR_TRANSFORM_WITH_LENGTH_0,x=n(6753);function a(t,e){var n=this._transformState;n.transforming=!1;var r=n.writecb;if(null===r)return this.emit("error",new $);n.writechunk=null,n.writecb=null,null!=e&&this.push(e),r(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function u(t){if(!(this instanceof u))return new u(t);x.call(this,t),this._transformState={afterTransform:a.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",c)}function c(){var t=this;"function"!=typeof this._flush||this._readableState.destroyed?l(this,null,null):this._flush(function(e,n){l(t,e,n)})}function l(t,e,n){if(e)return t.emit("error",e);if(null!=n&&t.push(n),t._writableState.length)throw new s;if(t._transformState.transforming)throw new o;return t.push(null)}n(5717)(u,x),u.prototype.push=function(t,e){return this._transformState.needTransform=!1,x.prototype.push.call(this,t,e)},u.prototype._transform=function(t,e,n){n(new i("_transform()"))},u.prototype._write=function(t,e,n){var r=this._transformState;if(r.writecb=n,r.writechunk=t,r.writeencoding=e,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},u.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},u.prototype._destroy=function(t,e){x.prototype._destroy.call(this,t,function(t){e(t)})}},4229(t,e,n){"use strict";function r(t){var e=this;this.next=null,this.entry=null,this.finish=function(){!function(t,e,n){var r=t.entry;for(t.entry=null;r;){var i=r.callback;e.pendingcb--,i(void 0),r=r.next}e.corkedRequestsFree.next=t}(e,t)}}t.exports=S,S.WritableState=w;var i,$,o={deprecate:n(4927)},s=n(2503),x=n(8764).Buffer,a=n.g.Uint8Array||function(){},u=n(1195),c=n(2457).getHighWaterMark,l=n(4281).q,h=l.ERR_INVALID_ARG_TYPE,f=l.ERR_METHOD_NOT_IMPLEMENTED,d=l.ERR_MULTIPLE_CALLBACK,p=l.ERR_STREAM_CANNOT_PIPE,y=l.ERR_STREAM_DESTROYED,g=l.ERR_STREAM_NULL_VALUES,b=l.ERR_STREAM_WRITE_AFTER_END,_=l.ERR_UNKNOWN_ENCODING,m=u.errorOrDestroy;function v(){}function w(t,e,$){i=i||n(6753),t=t||{},"boolean"!=typeof $&&($=e instanceof i),this.objectMode=!!t.objectMode,$&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=c(this,t,"writableHighWaterMark",$),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===t.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,e){var n,r,i,$,o,s,x=t._writableState,a=x.sync,u=x.writecb;if("function"!=typeof u)throw new d;if((n=x).writing=!1,n.writecb=null,n.length-=n.writelen,n.writelen=0,e)r=t,i=x,$=a,o=e,s=u,--i.pendingcb,$?(process.nextTick(s,o),process.nextTick(A,r,i),r._writableState.errorEmitted=!0,m(r,o)):(s(o),r._writableState.errorEmitted=!0,m(r,o),A(r,i));else{var c=M(x)||t.destroyed;c||x.corked||x.bufferProcessing||!x.bufferedRequest||C(t,x),a?process.nextTick(E,t,x,c,u):E(t,x,c,u)}}(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new r(this)}function S(t){var e=this instanceof(i=i||n(6753));if(!e&&!$.call(S,this))return new S(t);this._writableState=new w(t,this,e),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),s.call(this)}function k(t,e,n,r,i,$,o){e.writelen=r,e.writecb=o,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new y("write")):n?t._writev(i,e.onwrite):t._write(i,$,e.onwrite),e.sync=!1}function E(t,e,n,r){var i,$;n||(i=t,0===($=e).length&&$.needDrain&&($.needDrain=!1,i.emit("drain"))),e.pendingcb--,r(),A(t,e)}function C(t,e){e.bufferProcessing=!0;var n=e.bufferedRequest;if(t._writev&&n&&n.next){var i=Array(e.bufferedRequestCount),$=e.corkedRequestsFree;$.entry=n;for(var o=0,s=!0;n;)i[o]=n,n.isBuf||(s=!1),n=n.next,o+=1;i.allBuffers=s,k(t,e,!0,e.length,i,"",$.finish),e.pendingcb++,e.lastBufferedRequest=null,$.next?(e.corkedRequestsFree=$.next,$.next=null):e.corkedRequestsFree=new r(e),e.bufferedRequestCount=0}else{for(;n;){var x=n.chunk,a=n.encoding,u=n.callback;if(k(t,e,!1,e.objectMode?1:x.length,x,a,u),n=n.next,e.bufferedRequestCount--,e.writing)break}null===n&&(e.lastBufferedRequest=null)}e.bufferedRequest=n,e.bufferProcessing=!1}function M(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function I(t,e){t._final(function(n){e.pendingcb--,n&&m(t,n),e.prefinished=!0,t.emit("prefinish"),A(t,e)})}function A(t,e){var n,r,i=M(e);if(i&&(n=t,(r=e).prefinished||r.finalCalled||("function"!=typeof n._final||r.destroyed?(r.prefinished=!0,n.emit("prefinish")):(r.pendingcb++,r.finalCalled=!0,process.nextTick(I,n,r))),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var $=t._readableState;(!$||$.autoDestroy&&$.endEmitted)&&t.destroy()}return i}n(5717)(S,s),w.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(w.prototype,"buffer",{get:o.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?($=Function.prototype[Symbol.hasInstance],Object.defineProperty(S,Symbol.hasInstance,{value:function(t){return!!$.call(this,t)||this===S&&t&&t._writableState instanceof w}})):$=function(t){return t instanceof this},S.prototype.pipe=function(){m(this,new p)},S.prototype.write=function(t,e,n){var r,i,$,o,s,u,c,l,f,d,p=this._writableState,y=!1,_=!p.objectMode&&(r=t,x.isBuffer(r)||r instanceof a);return _&&!x.isBuffer(t)&&(t=(i=t,x.from(i))),"function"==typeof e&&(n=e,e=null),_?e="buffer":e||(e=p.defaultEncoding),"function"!=typeof n&&(n=v),p.ending?($=this,o=n,m($,s=new b),process.nextTick(o,s)):!_&&(u=this,c=p,l=t,f=n,null===l?d=new g:"string"==typeof l||c.objectMode||(d=new h("chunk",["string","Buffer"],l)),d&&(m(u,d),process.nextTick(f,d),1))||(p.pendingcb++,y=function(t,e,n,r,i,$){if(!n){var o,s,a,u=(o=e,s=r,a=i,o.objectMode||!1===o.decodeStrings||"string"!=typeof s||(s=x.from(s,a)),s);r!==u&&(n=!0,i="buffer",r=u)}var c=e.objectMode?1:r.length;e.length+=c;var l=e.length<e.highWaterMark;if(l||(e.needDrain=!0),e.writing||e.corked){var h=e.lastBufferedRequest;e.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:$,next:null},h?h.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else k(t,e,!1,c,r,i,$);return l}(this,p,_,t,e,n)),y},S.prototype.cork=function(){this._writableState.corked++},S.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||C(this,t))},S.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new _(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(S.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(S.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),S.prototype._write=function(t,e,n){n(new f("_write()"))},S.prototype._writev=null,S.prototype.end=function(t,e,n){var r,i,$,o=this._writableState;return"function"==typeof t?(n=t,t=null,e=null):"function"==typeof e&&(n=e,e=null),null!=t&&this.write(t,e),o.corked&&(o.corked=1,this.uncork()),o.ending||(r=this,i=o,$=n,i.ending=!0,A(r,i),$&&(i.finished?process.nextTick($):r.once("finish",$)),i.ended=!0,r.writable=!1),this},Object.defineProperty(S.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(S.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),S.prototype.destroy=u.destroy,S.prototype._undestroy=u.undestroy,S.prototype._destroy=function(t,e){e(t)}},5850(t,e,n){"use strict";function r(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var i,$=n(8610),o=Symbol("lastResolve"),s=Symbol("lastReject"),x=Symbol("error"),a=Symbol("ended"),u=Symbol("lastPromise"),c=Symbol("handlePromise"),l=Symbol("stream");function h(t,e){return{value:t,done:e}}function f(t){var e=t[o];if(null!==e){var n=t[l].read();null!==n&&(t[u]=null,t[o]=null,t[s]=null,e(h(n,!1)))}}function d(t){process.nextTick(f,t)}var p=Object.getPrototypeOf(function(){}),y=Object.setPrototypeOf((r(i={get stream(){return this[l]},next:function(){var t,e,n=this,r=this[x];if(null!==r)return Promise.reject(r);if(this[a])return Promise.resolve(h(void 0,!0));if(this[l].destroyed)return new Promise(function(t,e){process.nextTick(function(){n[x]?e(n[x]):t(h(void 0,!0))})});var i,$=this[u];if($)i=new Promise((t=$,e=this,function(n,r){t.then(function(){e[a]?n(h(void 0,!0)):e[c](n,r)},r)}));else{var o=this[l].read();if(null!==o)return Promise.resolve(h(o,!1));i=new Promise(this[c])}return this[u]=i,i}},Symbol.asyncIterator,function(){return this}),r(i,"return",function(){var t=this;return new Promise(function(e,n){t[l].destroy(null,function(t){t?n(t):e(h(void 0,!0))})})}),i),p);t.exports=function(t){var e,n=Object.create(y,(r(e={},l,{value:t,writable:!0}),r(e,o,{value:null,writable:!0}),r(e,s,{value:null,writable:!0}),r(e,x,{value:null,writable:!0}),r(e,a,{value:t._readableState.endEmitted,writable:!0}),r(e,c,{value:function(t,e){var r=n[l].read();r?(n[u]=null,n[o]=null,n[s]=null,t(h(r,!1))):(n[o]=t,n[s]=e)},writable:!0}),e));return n[u]=null,$(t,function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=n[s];return null!==e&&(n[u]=null,n[o]=null,n[s]=null,e(t)),void(n[x]=t)}var r=n[o];null!==r&&(n[u]=null,n[o]=null,n[s]=null,r(h(void 0,!0))),n[a]=!0}),t.on("readable",d.bind(null,n)),n}},7327(t,e,n){"use strict";function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function $(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var o=n(8764).Buffer,s=n(2361).inspect,x=s&&s.custom||"inspect";t.exports=function(){var t,e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.head=null,this.tail=null,this.length=0}return t=n,e=[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";for(var e=this.head,n=""+e.data;e=e.next;)n+=t+e.data;return n}},{key:"concat",value:function(t){if(0===this.length)return o.alloc(0);for(var e,n,r,i=o.allocUnsafe(t>>>0),$=this.head,s=0;$;)e=$.data,n=i,r=s,o.prototype.copy.call(e,n,r),s+=$.data.length,$=$.next;return i}},{key:"consume",value:function(t,e){var n;return t<this.head.data.length?(n=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):n=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),n}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,n=1,r=e.data;for(t-=r.length;e=e.next;){var i=e.data,$=t>i.length?i.length:t;if($===i.length?r+=i:r+=i.slice(0,t),0==(t-=$)){$===i.length?(++n,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice($));break}++n}return this.length-=n,r}},{key:"_getBuffer",value:function(t){var e=o.allocUnsafe(t),n=this.head,r=1;for(n.data.copy(e),t-=n.data.length;n=n.next;){var i=n.data,$=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,$),0==(t-=$)){$===i.length?(++r,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=i.slice($));break}++r}return this.length-=r,e}},{key:x,value:function(t,e){return s(this,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach(function(e){i(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},e,{depth:0,customInspect:!1}))}}],$(t.prototype,e),n}()},1195(t){"use strict";function e(t,e){r(t,e),n(t)}function n(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function r(t,e){t.emit("error",e)}t.exports={destroy:function(t,i){var $=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(i?i(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(r,this,t)):process.nextTick(r,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,function(t){!i&&t?$._writableState?$._writableState.errorEmitted?process.nextTick(n,$):($._writableState.errorEmitted=!0,process.nextTick(e,$,t)):process.nextTick(e,$,t):i?(process.nextTick(n,$),i(t)):process.nextTick(n,$)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(t,e){var n=t._readableState,r=t._writableState;n&&n.autoDestroy||r&&r.autoDestroy?t.destroy(e):t.emit("error",e)}}},8610(t,e,n){"use strict";var r=n(4281).q.ERR_STREAM_PREMATURE_CLOSE;function i(){}t.exports=function t(e,n,$){if("function"==typeof n)return t(e,null,n);n||(n={}),$=(o=$||i,s=!1,function(){if(!s){s=!0;for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];o.apply(this,e)}});var o,s,x,a=n.readable||!1!==n.readable&&e.readable,u=n.writable||!1!==n.writable&&e.writable,c=function(){e.writable||h()},l=e._writableState&&e._writableState.finished,h=function(){u=!1,l=!0,a||$.call(e)},f=e._readableState&&e._readableState.endEmitted,d=function(){a=!1,f=!0,u||$.call(e)},p=function(t){$.call(e,t)},y=function(){var t;return a&&!f?(e._readableState&&e._readableState.ended||(t=new r),$.call(e,t)):u&&!l?(e._writableState&&e._writableState.ended||(t=new r),$.call(e,t)):void 0},g=function(){e.req.on("finish",h)};return(x=e).setHeader&&"function"==typeof x.abort?(e.on("complete",h),e.on("abort",y),e.req?g():e.on("request",g)):u&&!e._writableState&&(e.on("end",c),e.on("close",c)),e.on("end",d),e.on("finish",h),!1!==n.error&&e.on("error",p),e.on("close",y),function(){e.removeListener("complete",h),e.removeListener("abort",y),e.removeListener("request",g),e.req&&e.req.removeListener("finish",h),e.removeListener("end",c),e.removeListener("close",c),e.removeListener("finish",h),e.removeListener("end",d),e.removeListener("error",p),e.removeListener("close",y)}}},5167(t){t.exports=function(){throw Error("Readable.from is not available in the browser")}},9946(t,e,n){"use strict";var r,i=n(4281).q,$=i.ERR_MISSING_ARGS,o=i.ERR_STREAM_DESTROYED;function s(t){if(t)throw t}function x(t,e,i,$){$=(s=$,x=!1,function(){x||(x=!0,s.apply(void 0,arguments))});var s,x,a=!1;t.on("close",function(){a=!0}),void 0===r&&(r=n(8610)),r(t,{readable:e,writable:i},function(t){if(t)return $(t);a=!0,$()});var u=!1;return function(e){if(!a&&!u){var n;return u=!0,(n=t).setHeader&&"function"==typeof n.abort?t.abort():"function"==typeof t.destroy?t.destroy():void $(e||new o("pipe"))}}}function a(t){t()}function u(t,e){return t.pipe(e)}function c(t){return t.length?"function"!=typeof t[t.length-1]?s:t.pop():s}t.exports=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var r,i=c(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new $("streams");var o=e.map(function(t,n){var $=n<e.length-1;return x(t,$,n>0,function(t){r||(r=t),t&&o.forEach(a),$||(o.forEach(a),i(r))})});return e.reduce(u)}},2457(t,e,n){"use strict";var r=n(4281).q.ERR_INVALID_OPT_VALUE;t.exports={getHighWaterMark:function(t,e,n,i){var $,o,s,x=($=e,o=i,s=n,null!=$.highWaterMark?$.highWaterMark:o?$[s]:null);if(null!=x){if(!isFinite(x)||Math.floor(x)!==x||x<0)throw new r(i?n:"highWaterMark",x);return Math.floor(x)}return t.objectMode?16:16384}}},2503(t,e,n){t.exports=n(7187).EventEmitter},8473(t,e,n){(e=t.exports=n(9481)).Stream=e,e.Readable=e,e.Writable=n(4229),e.Duplex=n(6753),e.Transform=n(4605),e.PassThrough=n(2725),e.finished=n(8610),e.pipeline=n(9946)},4143(t,e,n){"use strict";n.r(e),n.d(e,{ArgumentOutOfRangeError:()=>A.W,AsyncSubject:()=>u.c,BehaviorSubject:()=>x.X,ConnectableObservable:()=>i.c,EMPTY:()=>Y.E,EmptyError:()=>T.K,GroupedObservable:()=>$.T,NEVER:()=>tc,Notification:()=>k.P,NotificationKind:()=>k.W,ObjectUnsubscribedError:()=>R.N,Observable:()=>r.y,ReplaySubject:()=>a.t,Scheduler:()=>v.b,Subject:()=>s.xQ,Subscriber:()=>S.L,Subscription:()=>w.w,TimeoutError:()=>N.W,UnsubscriptionError:()=>L.B,VirtualAction:()=>m,VirtualTimeScheduler:()=>_,animationFrame:()=>b,animationFrameScheduler:()=>g,asap:()=>c.e,asapScheduler:()=>c.E,async:()=>l.P,asyncScheduler:()=>l.z,bindCallback:()=>j,bindNodeCallback:()=>W,combineLatest:()=>q.aj,concat:()=>Z.z,config:()=>tk.v,defer:()=>G.P,empty:()=>Y.c,forkJoin:()=>J,from:()=>Q.D,fromEvent:()=>te,fromEventPattern:()=>tr,generate:()=>ti,identity:()=>M.y,iif:()=>to,interval:()=>tx,isObservable:()=>I,merge:()=>tu.T,never:()=>tl,noop:()=>C.Z,observable:()=>o.L,of:()=>th.of,onErrorResumeNext:()=>tf,pairs:()=>td,partition:()=>tb,pipe:()=>E.z,queue:()=>h.c,queueScheduler:()=>h.N,race:()=>t_.S3,range:()=>tm,scheduled:()=>t2.x,throwError:()=>tw._,timer:()=>t8.H,using:()=>t1,zip:()=>tS.$R});var r=n(2772),i=n(3140),$=n(1120),o=n(5050),s=n(211),x=n(9233),a=n(2630),u=n(364),c=n(6650),l=n(964),h=n(2546),f=n(5987),d=n(6114),p=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.scheduler=e,r.work=n,r}return f.ZT(e,t),e.prototype.requestAsyncId=function(e,n,r){return void 0===r&&(r=0),null!==r&&r>0?t.prototype.requestAsyncId.call(this,e,n,r):(e.actions.push(this),e.scheduled||(e.scheduled=requestAnimationFrame(function(){return e.flush(null)})))},e.prototype.recycleAsyncId=function(e,n,r){if(void 0===r&&(r=0),null!==r&&r>0||null===r&&this.delay>0)return t.prototype.recycleAsyncId.call(this,e,n,r);0===e.actions.length&&(cancelAnimationFrame(n),e.scheduled=void 0)},e}(d.o),y=n(8399),g=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f.ZT(e,t),e.prototype.flush=function(t){this.active=!0,this.scheduled=void 0;var e,n=this.actions,r=-1,i=n.length;t=t||n.shift();do if(e=t.execute(t.state,t.delay))break;while(++r<i&&(t=n.shift()));if(this.active=!1,e){for(;++r<i&&(t=n.shift());)t.unsubscribe();throw e}},e}(y.v))(p),b=g,_=function(t){function e(e,n){void 0===e&&(e=m),void 0===n&&(n=Number.POSITIVE_INFINITY);var r=t.call(this,e,function(){return r.frame})||this;return r.maxFrames=n,r.frame=0,r.index=-1,r}return f.ZT(e,t),e.prototype.flush=function(){for(var t,e,n=this.actions,r=this.maxFrames;(e=n[0])&&e.delay<=r&&(n.shift(),this.frame=e.delay,!(t=e.execute(e.state,e.delay))););if(t){for(;e=n.shift();)e.unsubscribe();throw t}},e.frameTimeFactor=10,e}(y.v),m=function(t){function e(e,n,r){void 0===r&&(r=e.index+=1);var i=t.call(this,e,n)||this;return i.scheduler=e,i.work=n,i.index=r,i.active=!0,i.index=e.index=r,i}return f.ZT(e,t),e.prototype.schedule=function(n,r){if(void 0===r&&(r=0),!this.id)return t.prototype.schedule.call(this,n,r);this.active=!1;var i=new e(this.scheduler,this.work);return this.add(i),i.schedule(n,r)},e.prototype.requestAsyncId=function(t,n,r){void 0===r&&(r=0),this.delay=t.frame+r;var i=t.actions;return i.push(this),i.sort(e.sortActions),!0},e.prototype.recycleAsyncId=function(t,e,n){void 0===n&&(n=0)},e.prototype._execute=function(e,n){if(!0===this.active)return t.prototype._execute.call(this,e,n)},e.sortActions=function(t,e){return t.delay===e.delay?t.index===e.index?0:t.index>e.index?1:-1:t.delay>e.delay?1:-1},e}(d.o),v=n(8725),w=n(8760),S=n(979),k=n(2632),E=n(2561),C=n(3306),M=n(3608);function I(t){return!!t&&(t instanceof r.y||"function"==typeof t.lift&&"function"==typeof t.subscribe)}var A=n(6565),T=n(6929),R=n(1016),L=n(8782),N=n(1462),P=n(5709),D=n(3642),O=n(9026),B=n(7507);function j(t,e,n){if(e){if(!(0,B.K)(e))return function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return j(t,n).apply(void 0,r).pipe((0,P.U)(function(t){return(0,O.k)(t)?e.apply(void 0,t):e(t)}))};n=e}return function(){for(var e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];var $,o=this,s={context:o,subject:$,callbackFunc:t,scheduler:n};return new r.y(function(r){if(n){var i={args:e,subscriber:r,params:s};return n.schedule(U,0,i)}if(!$){$=new u.c;try{t.apply(o,e.concat([function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];$.next(t.length<=1?t[0]:t),$.complete()}]))}catch(x){(0,D._)($)?$.error(x):console.warn(x)}}return $.subscribe(r)})}}function U(t){var e=this,n=t.args,r=t.subscriber,i=t.params,$=i.callbackFunc,o=i.context,s=i.scheduler,x=i.subject;if(!x){x=i.subject=new u.c;try{$.apply(o,n.concat([function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=t.length<=1?t[0]:t;e.add(s.schedule(F,0,{value:r,subject:x}))}]))}catch(a){x.error(a)}}this.add(x.subscribe(r))}function F(t){var e=t.value,n=t.subject;n.next(e),n.complete()}function W(t,e,n){if(e){if(!(0,B.K)(e))return function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return W(t,n).apply(void 0,r).pipe((0,P.U)(function(t){return(0,O.k)(t)?e.apply(void 0,t):e(t)}))};n=e}return function(){for(var e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];var $={subject:void 0,args:e,callbackFunc:t,scheduler:n,context:this};return new r.y(function(r){var i=$.context,o=$.subject;if(n)return n.schedule(z,0,{params:$,subscriber:r,context:i});if(!o){o=$.subject=new u.c;try{t.apply(i,e.concat([function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t.shift();n?o.error(n):(o.next(t.length<=1?t[0]:t),o.complete())}]))}catch(s){(0,D._)(o)?o.error(s):console.warn(s)}}return o.subscribe(r)})}}function z(t){var e=this,n=t.params,r=t.subscriber,i=t.context,$=n.callbackFunc,o=n.args,s=n.scheduler,x=n.subject;if(!x){x=n.subject=new u.c;try{$.apply(i,o.concat([function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=t.shift();if(r)e.add(s.schedule(V,0,{err:r,subject:x}));else{var i=t.length<=1?t[0]:t;e.add(s.schedule(H,0,{value:i,subject:x}))}}]))}catch(a){this.add(s.schedule(V,0,{err:a,subject:x}))}}this.add(x.subscribe(r))}function H(t){var e=t.value,n=t.subject;n.next(e),n.complete()}function V(t){var e=t.err;t.subject.error(e)}var q=n(5142),Z=n(9795),G=n(1410),Y=n(5631),K=n(2009),Q=n(5760);function J(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(1===t.length){var n=t[0];if((0,O.k)(n))return X(n,null);if((0,K.K)(n)&&Object.getPrototypeOf(n)===Object.prototype){var r=Object.keys(n);return X(r.map(function(t){return n[t]}),r)}}if("function"==typeof t[t.length-1]){var i=t.pop();return X(t=1===t.length&&(0,O.k)(t[0])?t[0]:t,null).pipe((0,P.U)(function(t){return i.apply(void 0,t)}))}return X(t,null)}function X(t,e){return new r.y(function(n){var r=t.length;if(0!==r)for(var i=Array(r),$=0,o=0,s=function(s){var x=(0,Q.D)(t[s]),a=!1;n.add(x.subscribe({next:function(t){a||(a=!0,o++),i[s]=t},error:function(t){return n.error(t)},complete:function(){++$!==r&&a||(o===r&&n.next(e?e.reduce(function(t,e,n){return t[e]=i[n],t},{}):i),n.complete())}}))},x=0;x<r;x++)s(x);else n.complete()})}var tt=n(4156);function te(t,e,n,i){return(0,tt.m)(n)&&(i=n,n=void 0),i?te(t,e,n).pipe((0,P.U)(function(t){return(0,O.k)(t)?i.apply(void 0,t):i(t)})):new r.y(function(r){tn(t,e,function(t){arguments.length>1?r.next(Array.prototype.slice.call(arguments)):r.next(t)},r,n)})}function tn(t,e,n,r,i){var $,o;if((o=t)&&"function"==typeof o.addEventListener&&"function"==typeof o.removeEventListener){var s=t;t.addEventListener(e,n,i),$=function(){return s.removeEventListener(e,n,i)}}else if((x=t)&&"function"==typeof x.on&&"function"==typeof x.off){var x,a,u=t;t.on(e,n),$=function(){return u.off(e,n)}}else if((a=t)&&"function"==typeof a.addListener&&"function"==typeof a.removeListener){var c=t;t.addListener(e,n),$=function(){return c.removeListener(e,n)}}else{if(!t||!t.length)throw TypeError("Invalid event target");for(var l=0,h=t.length;l<h;l++)tn(t[l],e,n,r,i)}r.add($)}function tr(t,e,n){return n?tr(t,e).pipe((0,P.U)(function(t){return(0,O.k)(t)?n.apply(void 0,t):n(t)})):new r.y(function(n){var r,i=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n.next(1===t.length?t[0]:t)};try{r=t(i)}catch($){return void n.error($)}if((0,tt.m)(e))return function(){return e(i,r)}})}function ti(t,e,n,i,$){var o,s;if(1==arguments.length){var x=t;s=x.initialState,e=x.condition,n=x.iterate,o=x.resultSelector||M.y,$=x.scheduler}else void 0===i||(0,B.K)(i)?(s=t,o=M.y,$=i):(s=t,o=i);return new r.y(function(t){var r=s;if($)return $.schedule(t$,0,{subscriber:t,iterate:n,condition:e,resultSelector:o,state:r});for(;;){if(e){var i=void 0;try{i=e(r)}catch(x){return void t.error(x)}if(!i){t.complete();break}}var a=void 0;try{a=o(r)}catch(u){return void t.error(u)}if(t.next(a),t.closed)break;try{r=n(r)}catch(c){return void t.error(c)}}})}function t$(t){var e=t.subscriber,n=t.condition;if(!e.closed){if(t.needIterate)try{t.state=t.iterate(t.state)}catch(r){return void e.error(r)}else t.needIterate=!0;if(n){var i,$=void 0;try{$=n(t.state)}catch(o){return void e.error(o)}if(!$)return void e.complete();if(e.closed)return}try{i=t.resultSelector(t.state)}catch(s){return void e.error(s)}if(!e.closed&&(e.next(i),!e.closed))return this.schedule(t)}}function to(t,e,n){return void 0===e&&(e=Y.E),void 0===n&&(n=Y.E),(0,G.P)(function(){return t()?e:n})}var ts=n(5812);function tx(t,e){return void 0===t&&(t=0),void 0===e&&(e=l.P),(!(0,ts.k)(t)||t<0)&&(t=0),e&&"function"==typeof e.schedule||(e=l.P),new r.y(function(n){return n.add(e.schedule(ta,t,{subscriber:n,counter:0,period:t})),n})}function ta(t){var e=t.subscriber,n=t.counter,r=t.period;e.next(n),this.schedule({subscriber:e,counter:n+1,period:r},r)}var tu=n(4370),tc=new r.y(C.Z);function tl(){return tc}var th=n(8170);function tf(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(0===t.length)return Y.E;var n=t[0],i=t.slice(1);return 1===t.length&&(0,O.k)(n)?tf.apply(void 0,n):new r.y(function(t){var e=function(){return t.add(tf.apply(void 0,i).subscribe(t))};return(0,Q.D)(n).subscribe({next:function(e){t.next(e)},error:e,complete:e})})}function td(t,e){return new r.y(e?function(n){var r=Object.keys(t),i=new w.w;return i.add(e.schedule(tp,0,{keys:r,index:0,subscriber:n,subscription:i,obj:t})),i}:function(e){for(var n=Object.keys(t),r=0;r<n.length&&!e.closed;r++){var i=n[r];t.hasOwnProperty(i)&&e.next([i,t[i]])}e.complete()})}function tp(t){var e=t.keys,n=t.index,r=t.subscriber,i=t.subscription,$=t.obj;if(!r.closed){if(n<e.length){var o=e[n];r.next([o,$[o]]),i.add(this.schedule({keys:e,index:n+1,subscriber:r,subscription:i,obj:$}))}else r.complete()}}var t0=n(8463),ty=n(7843),tg=n(6008);function tb(t,e,n){return[(0,tg.h)(e,n)(new r.y((0,ty.s)(t))),(0,tg.h)((0,t0.f)(e,n))(new r.y((0,ty.s)(t)))]}var t_=n(8821);function tm(t,e,n){return void 0===t&&(t=0),new r.y(function(r){void 0===e&&(e=t,t=0);var i=0,$=t;if(n)return n.schedule(tv,0,{index:i,count:e,start:t,subscriber:r});for(;;){if(i++>=e){r.complete();break}if(r.next($++),r.closed)break}})}function tv(t){var e=t.start,n=t.index,r=t.count,i=t.subscriber;n>=r?i.complete():(i.next(e),i.closed||(t.index=n+1,t.start=e+1,this.schedule(t)))}var tw=n(4944),t8=n(9604);function t1(t,e){return new r.y(function(n){try{$=t()}catch(r){return void n.error(r)}try{o=e($)}catch(i){return void n.error(i)}var $,o,s=(o?(0,Q.D)(o):Y.E).subscribe(n);return function(){s.unsubscribe(),$&&$.unsubscribe()}})}var tS=n(5080),t2=n(8107),tk=n(150)},364(t,e,n){"use strict";n.d(e,{c:()=>o});var r=n(5987),i=n(211),$=n(8760),o=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.value=null,e.hasNext=!1,e.hasCompleted=!1,e}return r.ZT(e,t),e.prototype._subscribe=function(e){return this.hasError?(e.error(this.thrownError),$.w.EMPTY):this.hasCompleted&&this.hasNext?(e.next(this.value),e.complete(),$.w.EMPTY):t.prototype._subscribe.call(this,e)},e.prototype.next=function(t){this.hasCompleted||(this.value=t,this.hasNext=!0)},e.prototype.error=function(e){this.hasCompleted||t.prototype.error.call(this,e)},e.prototype.complete=function(){this.hasCompleted=!0,this.hasNext&&t.prototype.next.call(this,this.value),t.prototype.complete.call(this)},e}(i.xQ)},9233(t,e,n){"use strict";n.d(e,{X:()=>o});var r=n(5987),i=n(211),$=n(1016),o=function(t){function e(e){var n=t.call(this)||this;return n._value=e,n}return r.ZT(e,t),Object.defineProperty(e.prototype,"value",{get:function(){return this.getValue()},enumerable:!0,configurable:!0}),e.prototype._subscribe=function(e){var n=t.prototype._subscribe.call(this,e);return n&&!n.closed&&e.next(this._value),n},e.prototype.getValue=function(){if(this.hasError)throw this.thrownError;if(this.closed)throw new $.N;return this._value},e.prototype.next=function(e){t.prototype.next.call(this,this._value=e)},e}(i.xQ)},2632(t,e,n){"use strict";n.d(e,{P:()=>s,W:()=>r});var r,i=n(5631),$=n(8170),o=n(4944);r||(r={});var s=function(){function t(t,e,n){this.kind=t,this.value=e,this.error=n,this.hasValue="N"===t}return t.prototype.observe=function(t){switch(this.kind){case"N":return t.next&&t.next(this.value);case"E":return t.error&&t.error(this.error);case"C":return t.complete&&t.complete()}},t.prototype.do=function(t,e,n){switch(this.kind){case"N":return t&&t(this.value);case"E":return e&&e(this.error);case"C":return n&&n()}},t.prototype.accept=function(t,e,n){return t&&"function"==typeof t.next?this.observe(t):this.do(t,e,n)},t.prototype.toObservable=function(){switch(this.kind){case"N":return(0,$.of)(this.value);case"E":return(0,o._)(this.error);case"C":return(0,i.c)()}throw Error("unexpected notification kind value")},t.createNext=function(e){return void 0!==e?new t("N",e):t.undefinedValueNotification},t.createError=function(e){return new t("E",void 0,e)},t.createComplete=function(){return t.completeNotification},t.completeNotification=new t("C"),t.undefinedValueNotification=new t("N",void 0),t}()},2772(t,e,n){"use strict";n.d(e,{y:()=>u});var r=n(3642),i=n(979),$=n(3142),o=n(2174),s=n(5050),x=n(2561),a=n(150),u=function(){function t(t){this._isScalar=!1,t&&(this._subscribe=t)}return t.prototype.lift=function(e){var n=new t;return n.source=this,n.operator=e,n},t.prototype.subscribe=function(t,e,n){var r=this.operator,s=function(t,e,n){if(t){if(t instanceof i.L)return t;if(t[$.b])return t[$.b]()}return t||e||n?new i.L(t,e,n):new i.L(o.c)}(t,e,n);if(r?s.add(r.call(s,this.source)):s.add(this.source||a.v.useDeprecatedSynchronousErrorHandling&&!s.syncErrorThrowable?this._subscribe(s):this._trySubscribe(s)),a.v.useDeprecatedSynchronousErrorHandling&&s.syncErrorThrowable&&(s.syncErrorThrowable=!1,s.syncErrorThrown))throw s.syncErrorValue;return s},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(e){a.v.useDeprecatedSynchronousErrorHandling&&(t.syncErrorThrown=!0,t.syncErrorValue=e),(0,r._)(t)?t.error(e):console.warn(e)}},t.prototype.forEach=function(t,e){var n=this;return new(e=c(e))(function(e,r){var i;i=n.subscribe(function(e){try{t(e)}catch(n){r(n),i&&i.unsubscribe()}},r,e)})},t.prototype._subscribe=function(t){var e=this.source;return e&&e.subscribe(t)},t.prototype[s.L]=function(){return this},t.prototype.pipe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return 0===t.length?this:(0,x.U)(t)(this)},t.prototype.toPromise=function(t){var e=this;return new(t=c(t))(function(t,n){var r;e.subscribe(function(t){return r=t},function(t){return n(t)},function(){return t(r)})})},t.create=function(e){return new t(e)},t}();function c(t){if(t||(t=a.v.Promise||Promise),!t)throw Error("no Promise impl found");return t}},2174(t,e,n){"use strict";n.d(e,{c:()=>$});var r=n(150),i=n(1644),$={closed:!0,next:function(t){},error:function(t){if(r.v.useDeprecatedSynchronousErrorHandling)throw t;(0,i.z)(t)},complete:function(){}}},2039(t,e,n){"use strict";n.d(e,{L:()=>i});var r=n(5987),i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.ZT(e,t),e.prototype.notifyNext=function(t,e,n,r,i){this.destination.next(e)},e.prototype.notifyError=function(t,e){this.destination.error(t)},e.prototype.notifyComplete=function(t){this.destination.complete()},e}(n(979).L)},2630(t,e,n){"use strict";n.d(e,{t:()=>u});var r=n(5987),i=n(211),$=n(2546),o=n(8760),s=n(9276),x=n(1016),a=n(8253),u=function(t){function e(e,n,r){void 0===e&&(e=Number.POSITIVE_INFINITY),void 0===n&&(n=Number.POSITIVE_INFINITY);var i=t.call(this)||this;return i.scheduler=r,i._events=[],i._infiniteTimeWindow=!1,i._bufferSize=e<1?1:e,i._windowTime=n<1?1:n,n===Number.POSITIVE_INFINITY?(i._infiniteTimeWindow=!0,i.next=i.nextInfiniteTimeWindow):i.next=i.nextTimeWindow,i}return r.ZT(e,t),e.prototype.nextInfiniteTimeWindow=function(e){if(!this.isStopped){var n=this._events;n.push(e),n.length>this._bufferSize&&n.shift()}t.prototype.next.call(this,e)},e.prototype.nextTimeWindow=function(e){this.isStopped||(this._events.push(new c(this._getNow(),e)),this._trimBufferThenGetEvents()),t.prototype.next.call(this,e)},e.prototype._subscribe=function(t){var e,n=this._infiniteTimeWindow,r=n?this._events:this._trimBufferThenGetEvents(),i=this.scheduler,$=r.length;if(this.closed)throw new x.N;if(this.isStopped||this.hasError?e=o.w.EMPTY:(this.observers.push(t),e=new a.W(this,t)),i&&t.add(t=new s.ht(t,i)),n)for(var u=0;u<$&&!t.closed;u++)t.next(r[u]);else for(u=0;u<$&&!t.closed;u++)t.next(r[u].value);return this.hasError?t.error(this.thrownError):this.isStopped&&t.complete(),e},e.prototype._getNow=function(){return(this.scheduler||$.c).now()},e.prototype._trimBufferThenGetEvents=function(){for(var t=this._getNow(),e=this._bufferSize,n=this._windowTime,r=this._events,i=r.length,$=0;$<i&&!(t-r[$].time<n);)$++;return i>e&&($=Math.max($,i-e)),$>0&&r.splice(0,$),r},e}(i.xQ),c=function(t,e){this.time=t,this.value=e}},8725(t,e,n){"use strict";n.d(e,{b:()=>r});var r=function(){function t(e,n){void 0===n&&(n=t.now),this.SchedulerAction=e,this.now=n}return t.prototype.schedule=function(t,e,n){return void 0===e&&(e=0),new this.SchedulerAction(this,t).schedule(n,e)},t.now=function(){return Date.now()},t}()},211(t,e,n){"use strict";n.d(e,{Yc:()=>u,xQ:()=>c});var r=n(5987),i=n(2772),$=n(979),o=n(8760),s=n(1016),x=n(8253),a=n(3142),u=function(t){function e(e){var n=t.call(this,e)||this;return n.destination=e,n}return r.ZT(e,t),e}($.L),c=function(t){function e(){var e=t.call(this)||this;return e.observers=[],e.closed=!1,e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return r.ZT(e,t),e.prototype[a.b]=function(){return new u(this)},e.prototype.lift=function(t){var e=new l(this,this);return e.operator=t,e},e.prototype.next=function(t){if(this.closed)throw new s.N;if(!this.isStopped)for(var e=this.observers,n=e.length,r=e.slice(),i=0;i<n;i++)r[i].next(t)},e.prototype.error=function(t){if(this.closed)throw new s.N;this.hasError=!0,this.thrownError=t,this.isStopped=!0;for(var e=this.observers,n=e.length,r=e.slice(),i=0;i<n;i++)r[i].error(t);this.observers.length=0},e.prototype.complete=function(){if(this.closed)throw new s.N;this.isStopped=!0;for(var t=this.observers,e=t.length,n=t.slice(),r=0;r<e;r++)n[r].complete();this.observers.length=0},e.prototype.unsubscribe=function(){this.isStopped=!0,this.closed=!0,this.observers=null},e.prototype._trySubscribe=function(e){if(this.closed)throw new s.N;return t.prototype._trySubscribe.call(this,e)},e.prototype._subscribe=function(t){if(this.closed)throw new s.N;return this.hasError?(t.error(this.thrownError),o.w.EMPTY):this.isStopped?(t.complete(),o.w.EMPTY):(this.observers.push(t),new x.W(this,t))},e.prototype.asObservable=function(){var t=new i.y;return t.source=this,t},e.create=function(t,e){return new l(t,e)},e}(i.y),l=function(t){function e(e,n){var r=t.call(this)||this;return r.destination=e,r.source=n,r}return r.ZT(e,t),e.prototype.next=function(t){var e=this.destination;e&&e.next&&e.next(t)},e.prototype.error=function(t){var e=this.destination;e&&e.error&&this.destination.error(t)},e.prototype.complete=function(){var t=this.destination;t&&t.complete&&this.destination.complete()},e.prototype._subscribe=function(t){return this.source?this.source.subscribe(t):o.w.EMPTY},e}(c)},8253(t,e,n){"use strict";n.d(e,{W:()=>i});var r=n(5987),i=function(t){function e(e,n){var r=t.call(this)||this;return r.subject=e,r.subscriber=n,r.closed=!1,r}return r.ZT(e,t),e.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var t=this.subject,e=t.observers;if(this.subject=null,e&&0!==e.length&&!t.isStopped&&!t.closed){var n=e.indexOf(this.subscriber);-1!==n&&e.splice(n,1)}}},e}(n(8760).w)},979(t,e,n){"use strict";n.d(e,{L:()=>u});var r=n(5987),i=n(4156),$=n(2174),o=n(8760),s=n(3142),x=n(150),a=n(1644),u=function(t){function e(n,r,i){var o=t.call(this)||this;switch(o.syncErrorValue=null,o.syncErrorThrown=!1,o.syncErrorThrowable=!1,o.isStopped=!1,arguments.length){case 0:o.destination=$.c;break;case 1:if(!n){o.destination=$.c;break}if("object"==typeof n){n instanceof e?(o.syncErrorThrowable=n.syncErrorThrowable,o.destination=n,n.add(o)):(o.syncErrorThrowable=!0,o.destination=new c(o,n));break}default:o.syncErrorThrowable=!0,o.destination=new c(o,n,r,i)}return o}return r.ZT(e,t),e.prototype[s.b]=function(){return this},e.create=function(t,n,r){var i=new e(t,n,r);return i.syncErrorThrowable=!1,i},e.prototype.next=function(t){this.isStopped||this._next(t)},e.prototype.error=function(t){this.isStopped||(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this))},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){this.destination.error(t),this.unsubscribe()},e.prototype._complete=function(){this.destination.complete(),this.unsubscribe()},e.prototype._unsubscribeAndRecycle=function(){var t=this._parentOrParents;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=t,this},e}(o.w),c=function(t){function e(e,n,r,o){var s,x=t.call(this)||this;x._parentSubscriber=e;var a=x;return(0,i.m)(n)?s=n:n&&(s=n.next,r=n.error,o=n.complete,n!==$.c&&(a=Object.create(n),(0,i.m)(a.unsubscribe)&&x.add(a.unsubscribe.bind(a)),a.unsubscribe=x.unsubscribe.bind(x))),x._context=a,x._next=s,x._error=r,x._complete=o,x}return r.ZT(e,t),e.prototype.next=function(t){if(!this.isStopped&&this._next){var e=this._parentSubscriber;x.v.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?this.__tryOrSetError(e,this._next,t)&&this.unsubscribe():this.__tryOrUnsub(this._next,t)}},e.prototype.error=function(t){if(!this.isStopped){var e=this._parentSubscriber,n=x.v.useDeprecatedSynchronousErrorHandling;if(this._error)n&&e.syncErrorThrowable?(this.__tryOrSetError(e,this._error,t),this.unsubscribe()):(this.__tryOrUnsub(this._error,t),this.unsubscribe());else if(e.syncErrorThrowable)n?(e.syncErrorValue=t,e.syncErrorThrown=!0):(0,a.z)(t),this.unsubscribe();else{if(this.unsubscribe(),n)throw t;(0,a.z)(t)}}},e.prototype.complete=function(){var t=this;if(!this.isStopped){var e=this._parentSubscriber;if(this._complete){var n=function(){return t._complete.call(t._context)};x.v.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?(this.__tryOrSetError(e,n),this.unsubscribe()):(this.__tryOrUnsub(n),this.unsubscribe())}else this.unsubscribe()}},e.prototype.__tryOrUnsub=function(t,e){try{t.call(this._context,e)}catch(n){if(this.unsubscribe(),x.v.useDeprecatedSynchronousErrorHandling)throw n;(0,a.z)(n)}},e.prototype.__tryOrSetError=function(t,e,n){if(!x.v.useDeprecatedSynchronousErrorHandling)throw Error("bad call");try{e.call(this._context,n)}catch(r){return x.v.useDeprecatedSynchronousErrorHandling?(t.syncErrorValue=r,t.syncErrorThrown=!0,!0):((0,a.z)(r),!0)}return!1},e.prototype._unsubscribe=function(){var t=this._parentSubscriber;this._context=null,this._parentSubscriber=null,t.unsubscribe()},e}(u)},8760(t,e,n){"use strict";n.d(e,{w:()=>s});var r=n(9026),i=n(2009),$=n(4156),o=n(8782),s=function(){var t;function e(t){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,t&&(this._ctorUnsubscribe=!0,this._unsubscribe=t)}return e.prototype.unsubscribe=function(){var t;if(!this.closed){var n=this,s=n._parentOrParents,a=n._ctorUnsubscribe,u=n._unsubscribe,c=n._subscriptions;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,s instanceof e)s.remove(this);else if(null!==s)for(var l=0;l<s.length;++l)s[l].remove(this);if((0,$.m)(u)){a&&(this._unsubscribe=void 0);try{u.call(this)}catch(h){t=h instanceof o.B?x(h.errors):[h]}}if((0,r.k)(c)){l=-1;for(var f=c.length;++l<f;){var d=c[l];if((0,i.K)(d))try{d.unsubscribe()}catch(p){t=t||[],p instanceof o.B?t=t.concat(x(p.errors)):t.push(p)}}}if(t)throw new o.B(t)}},e.prototype.add=function(t){var n=t;if(!t)return e.EMPTY;switch(typeof t){case"function":n=new e(t);case"object":if(n===this||n.closed||"function"!=typeof n.unsubscribe)return n;if(this.closed)return n.unsubscribe(),n;if(!(n instanceof e)){var r=n;(n=new e)._subscriptions=[r]}break;default:throw Error("unrecognized teardown "+t+" added to Subscription.")}var i=n._parentOrParents;if(null===i)n._parentOrParents=this;else if(i instanceof e){if(i===this)return n;n._parentOrParents=[i,this]}else{if(-1!==i.indexOf(this))return n;i.push(this)}var $=this._subscriptions;return null===$?this._subscriptions=[n]:$.push(n),n},e.prototype.remove=function(t){var e=this._subscriptions;if(e){var n=e.indexOf(t);-1!==n&&e.splice(n,1)}},e.EMPTY=((t=new e).closed=!0,t),e}();function x(t){return t.reduce(function(t,e){return t.concat(e instanceof o.B?e.errors:e)},[])}},150(t,e,n){"use strict";n.d(e,{v:()=>i});var r=!1,i={Promise:void 0,set useDeprecatedSynchronousErrorHandling(_0x1825a5){_0x1825a5&&Error().stack,r=_0x1825a5},get useDeprecatedSynchronousErrorHandling(){return r}}},7604(t,e,n){"use strict";n.d(e,{Ds:()=>x,IY:()=>s,ft:()=>a});var r=n(5987),i=n(979),$=n(2772),o=n(7843),s=function(t){function e(e){var n=t.call(this)||this;return n.parent=e,n}return r.ZT(e,t),e.prototype._next=function(t){this.parent.notifyNext(t)},e.prototype._error=function(t){this.parent.notifyError(t),this.unsubscribe()},e.prototype._complete=function(){this.parent.notifyComplete(),this.unsubscribe()},e}(i.L),x=(i.L,function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.ZT(e,t),e.prototype.notifyNext=function(t){this.destination.next(t)},e.prototype.notifyError=function(t){this.destination.error(t)},e.prototype.notifyComplete=function(){this.destination.complete()},e}(i.L));function a(t,e){if(!e.closed){var n;if(t instanceof $.y)return t.subscribe(e);try{n=(0,o.s)(t)(e)}catch(r){e.error(r)}return n}}i.L},3140(t,e,n){"use strict";n.d(e,{N:()=>c,c:()=>u});var r,i=n(5987),$=n(211),o=n(2772),s=n(979),x=n(8760),a=n(3018),u=function(t){function e(e,n){var r=t.call(this)||this;return r.source=e,r.subjectFactory=n,r._refCount=0,r._isComplete=!1,r}return i.ZT(e,t),e.prototype._subscribe=function(t){return this.getSubject().subscribe(t)},e.prototype.getSubject=function(){var t=this._subject;return t&&!t.isStopped||(this._subject=this.subjectFactory()),this._subject},e.prototype.connect=function(){var t=this._connection;return t||(this._isComplete=!1,(t=this._connection=new x.w).add(this.source.subscribe(new l(this.getSubject(),this))),t.closed&&(this._connection=null,t=x.w.EMPTY)),t},e.prototype.refCount=function(){return(0,a.x)()(this)},e}(o.y),c={operator:{value:null},_refCount:{value:0,writable:!0},_subject:{value:null,writable:!0},_connection:{value:null,writable:!0},_subscribe:{value:(r=u.prototype)._subscribe},_isComplete:{value:r._isComplete,writable:!0},getSubject:{value:r.getSubject},connect:{value:r.connect},refCount:{value:r.refCount}},l=function(t){function e(e,n){var r=t.call(this,e)||this;return r.connectable=n,r}return i.ZT(e,t),e.prototype._error=function(e){this._unsubscribe(),t.prototype._error.call(this,e)},e.prototype._complete=function(){this.connectable._isComplete=!0,this._unsubscribe(),t.prototype._complete.call(this)},e.prototype._unsubscribe=function(){var t=this.connectable;if(t){this.connectable=null;var e=t._connection;t._refCount=0,t._subject=null,t._connection=null,e&&e.unsubscribe()}},e}($.Yc);s.L},5142(t,e,n){"use strict";n.d(e,{Ms:()=>c,aj:()=>u});var r=n(5987),i=n(7507),$=n(9026),o=n(2039),s=n(2080),x=n(3375),a={};function u(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=void 0,r=void 0;return(0,i.K)(t[t.length-1])&&(r=t.pop()),"function"==typeof t[t.length-1]&&(n=t.pop()),1===t.length&&(0,$.k)(t[0])&&(t=t[0]),(0,x.n)(t,r).lift(new c(n))}var c=function(){function t(t){this.resultSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new l(t,this.resultSelector))},t}(),l=function(t){function e(e,n){var r=t.call(this,e)||this;return r.resultSelector=n,r.active=0,r.values=[],r.observables=[],r}return r.ZT(e,t),e.prototype._next=function(t){this.values.push(a),this.observables.push(t)},e.prototype._complete=function(){var t=this.observables,e=t.length;if(0===e)this.destination.complete();else{this.active=e,this.toRespond=e;for(var n=0;n<e;n++){var r=t[n];this.add((0,s.D)(this,r,void 0,n))}}},e.prototype.notifyComplete=function(t){0==(this.active-=1)&&this.destination.complete()},e.prototype.notifyNext=function(t,e,n){var r=this.values,i=r[n],$=this.toRespond?i===a?--this.toRespond:this.toRespond:0;r[n]=e,0===$&&(this.resultSelector?this._tryResultSelector(r):this.destination.next(r.slice()))},e.prototype._tryResultSelector=function(t){var e;try{e=this.resultSelector.apply(this,t)}catch(n){return void this.destination.error(n)}this.destination.next(e)},e}(o.L)},9795(t,e,n){"use strict";n.d(e,{z:()=>$});var r=n(8170),i=n(2257);function $(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,i.u)()(r.of.apply(void 0,t))}},1410(t,e,n){"use strict";n.d(e,{P:()=>o});var r=n(2772),i=n(5760),$=n(5631);function o(t){return new r.y(function(e){var n;try{n=t()}catch(r){return void e.error(r)}return(n?(0,i.D)(n):(0,$.c)()).subscribe(e)})}},5631(t,e,n){"use strict";n.d(e,{E:()=>i,c:()=>$});var r=n(2772),i=new r.y(function(t){return t.complete()});function $(t){var e;return t?(e=t,new r.y(function(t){return e.schedule(function(){return t.complete()})})):i}},5760(t,e,n){"use strict";n.d(e,{D:()=>o});var r=n(2772),i=n(7843),$=n(8107);function o(t,e){return e?(0,$.x)(t,e):t instanceof r.y?t:new r.y((0,i.s)(t))}},3375(t,e,n){"use strict";n.d(e,{n:()=>o});var r=n(2772),i=n(6900),$=n(3109);function o(t,e){return e?(0,$.r)(t,e):new r.y((0,i.V)(t))}},4370(t,e,n){"use strict";n.d(e,{T:()=>s});var r=n(2772),i=n(7507),$=n(2556),o=n(3375);function s(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Number.POSITIVE_INFINITY,s=null,x=t[t.length-1];return(0,i.K)(x)?(s=t.pop(),t.length>1&&"number"==typeof t[t.length-1]&&(n=t.pop())):"number"==typeof x&&(n=t.pop()),null===s&&1===t.length&&t[0]instanceof r.y?t[0]:(0,$.J)(n)((0,o.n)(t,s))}},8170(t,e,n){"use strict";n.d(e,{of:()=>o});var r=n(7507),i=n(3375),$=n(3109);function o(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[t.length-1];return(0,r.K)(n)?(t.pop(),(0,$.r)(t,n)):(0,i.n)(t)}},8821(t,e,n){"use strict";n.d(e,{S3:()=>x});var r=n(5987),i=n(9026),$=n(3375),o=n(2039),s=n(2080);function x(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(1===t.length){if(!(0,i.k)(t[0]))return t[0];t=t[0]}return(0,$.n)(t,void 0).lift(new a)}var a=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new u(t))},t}(),u=function(t){function e(e){var n=t.call(this,e)||this;return n.hasFirst=!1,n.observables=[],n.subscriptions=[],n}return r.ZT(e,t),e.prototype._next=function(t){this.observables.push(t)},e.prototype._complete=function(){var t=this.observables,e=t.length;if(0===e)this.destination.complete();else{for(var n=0;n<e&&!this.hasFirst;n++){var r=t[n],i=(0,s.D)(this,r,void 0,n);this.subscriptions&&this.subscriptions.push(i),this.add(i)}this.observables=null}},e.prototype.notifyNext=function(t,e,n){if(!this.hasFirst){this.hasFirst=!0;for(var r=0;r<this.subscriptions.length;r++)if(r!==n){var i=this.subscriptions[r];i.unsubscribe(),this.remove(i)}this.subscriptions=null}this.destination.next(e)},e}(o.L)},4944(t,e,n){"use strict";n.d(e,{_:()=>i});var r=n(2772);function i(t,e){return new r.y(e?function(n){return e.schedule($,0,{error:t,subscriber:n})}:function(e){return e.error(t)})}function $(t){var e=t.error;t.subscriber.error(e)}},9604(t,e,n){"use strict";n.d(e,{H:()=>s});var r=n(2772),i=n(964),$=n(5812),o=n(7507);function s(t,e,n){void 0===t&&(t=0);var s=-1;return(0,$.k)(e)?s=1>Number(e)?1:Number(e):(0,o.K)(e)&&(n=e),(0,o.K)(n)||(n=i.P),new r.y(function(e){var r=(0,$.k)(t)?t:+t-n.now();return n.schedule(x,r,{index:0,period:s,subscriber:e})})}function x(t){var e=t.index,n=t.period,r=t.subscriber;if(r.next(e),!r.closed){if(-1===n)return r.complete();t.index=e+1,this.schedule(t,n)}}},5080(t,e,n){"use strict";n.d(e,{$R:()=>a,mx:()=>u});var r=n(5987),i=n(3375),$=n(9026),o=n(979),s=n(999),x=n(7604);function a(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[t.length-1];return"function"==typeof n&&t.pop(),(0,i.n)(t,void 0).lift(new u(n))}var u=function(){function t(t){this.resultSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new c(t,this.resultSelector))},t}(),c=function(t){function e(e,n,r){void 0===r&&(r=Object.create(null));var i=t.call(this,e)||this;return i.resultSelector=n,i.iterators=[],i.active=0,i.resultSelector="function"==typeof n?n:void 0,i}return r.ZT(e,t),e.prototype._next=function(t){var e=this.iterators;(0,$.k)(t)?e.push(new h(t)):"function"==typeof t[s.hZ]?e.push(new l(t[s.hZ]())):e.push(new f(this.destination,this,t))},e.prototype._complete=function(){var t=this.iterators,e=t.length;if(this.unsubscribe(),0!==e){this.active=e;for(var n=0;n<e;n++){var r=t[n];r.stillUnsubscribed?this.destination.add(r.subscribe()):this.active--}}else this.destination.complete()},e.prototype.notifyInactive=function(){this.active--,0===this.active&&this.destination.complete()},e.prototype.checkIterators=function(){for(var t=this.iterators,e=t.length,n=this.destination,r=0;r<e;r++)if("function"==typeof(o=t[r]).hasValue&&!o.hasValue())return;var i=!1,$=[];for(r=0;r<e;r++){var o,s=(o=t[r]).next();if(o.hasCompleted()&&(i=!0),s.done)return void n.complete();$.push(s.value)}this.resultSelector?this._tryresultSelector($):n.next($),i&&n.complete()},e.prototype._tryresultSelector=function(t){var e;try{e=this.resultSelector.apply(this,t)}catch(n){return void this.destination.error(n)}this.destination.next(e)},e}(o.L),l=function(){function t(t){this.iterator=t,this.nextResult=t.next()}return t.prototype.hasValue=function(){return!0},t.prototype.next=function(){var t=this.nextResult;return this.nextResult=this.iterator.next(),t},t.prototype.hasCompleted=function(){var t=this.nextResult;return Boolean(t&&t.done)},t}(),h=function(){function t(t){this.array=t,this.index=0,this.length=0,this.length=t.length}return t.prototype[s.hZ]=function(){return this},t.prototype.next=function(t){var e=this.index++,n=this.array;return e<this.length?{value:n[e],done:!1}:{value:null,done:!0}},t.prototype.hasValue=function(){return this.array.length>this.index},t.prototype.hasCompleted=function(){return this.array.length===this.index},t}(),f=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.parent=n,i.observable=r,i.stillUnsubscribed=!0,i.buffer=[],i.isComplete=!1,i}return r.ZT(e,t),e.prototype[s.hZ]=function(){return this},e.prototype.next=function(){var t=this.buffer;return 0===t.length&&this.isComplete?{value:null,done:!0}:{value:t.shift(),done:!1}},e.prototype.hasValue=function(){return this.buffer.length>0},e.prototype.hasCompleted=function(){return 0===this.buffer.length&&this.isComplete},e.prototype.notifyComplete=function(){this.buffer.length>0?(this.isComplete=!0,this.parent.notifyInactive()):this.destination.complete()},e.prototype.notifyNext=function(t){this.buffer.push(t),this.parent.checkIterators()},e.prototype.subscribe=function(){return(0,x.ft)(this.observable,new x.IY(this))},e}(x.Ds)},2257(t,e,n){"use strict";n.d(e,{u:()=>i});var r=n(2556);function i(){return(0,r.J)(1)}},6008(t,e,n){"use strict";n.d(e,{h:()=>$});var r=n(5987),i=n(979);function $(t,e){return function(n){return n.lift(new o(t,e))}}var o=function(){function t(t,e){this.predicate=t,this.thisArg=e}return t.prototype.call=function(t,e){return e.subscribe(new s(t,this.predicate,this.thisArg))},t}(),s=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.predicate=n,i.thisArg=r,i.count=0,i}return r.ZT(e,t),e.prototype._next=function(t){var e;try{e=this.predicate.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}e&&this.destination.next(t)},e}(i.L)},1120(t,e,n){"use strict";n.d(e,{T:()=>l,v:()=>x});var r=n(5987),i=n(979),$=n(8760),o=n(2772),s=n(211);function x(t,e,n,r){return function(i){return i.lift(new a(t,e,n,r))}}var a=function(){function t(t,e,n,r){this.keySelector=t,this.elementSelector=e,this.durationSelector=n,this.subjectSelector=r}return t.prototype.call=function(t,e){return e.subscribe(new u(t,this.keySelector,this.elementSelector,this.durationSelector,this.subjectSelector))},t}(),u=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;return o.keySelector=n,o.elementSelector=r,o.durationSelector=i,o.subjectSelector=$,o.groups=null,o.attemptedToUnsubscribe=!1,o.count=0,o}return r.ZT(e,t),e.prototype._next=function(t){var e;try{e=this.keySelector(t)}catch(n){return void this.error(n)}this._group(t,e)},e.prototype._group=function(t,e){var n=this.groups;n||(n=this.groups=new Map);var r,i=n.get(e);if(this.elementSelector)try{r=this.elementSelector(t)}catch($){this.error($)}else r=t;if(!i){i=this.subjectSelector?this.subjectSelector():new s.xQ,n.set(e,i);var o=new l(e,i,this);if(this.destination.next(o),this.durationSelector){var x=void 0;try{x=this.durationSelector(new l(e,i))}catch(a){return void this.error(a)}this.add(x.subscribe(new c(e,i,this)))}}i.closed||i.next(r)},e.prototype._error=function(t){var e=this.groups;e&&(e.forEach(function(e,n){e.error(t)}),e.clear()),this.destination.error(t)},e.prototype._complete=function(){var t=this.groups;t&&(t.forEach(function(t,e){t.complete()}),t.clear()),this.destination.complete()},e.prototype.removeGroup=function(t){this.groups.delete(t)},e.prototype.unsubscribe=function(){this.closed||(this.attemptedToUnsubscribe=!0,0===this.count&&t.prototype.unsubscribe.call(this))},e}(i.L),c=function(t){function e(e,n,r){var i=t.call(this,n)||this;return i.key=e,i.group=n,i.parent=r,i}return r.ZT(e,t),e.prototype._next=function(t){this.complete()},e.prototype._unsubscribe=function(){var t=this.parent,e=this.key;this.key=this.parent=null,t&&t.removeGroup(e)},e}(i.L),l=function(t){function e(e,n,r){var i=t.call(this)||this;return i.key=e,i.groupSubject=n,i.refCountSubscription=r,i}return r.ZT(e,t),e.prototype._subscribe=function(t){var e=new $.w,n=this.refCountSubscription,r=this.groupSubject;return n&&!n.closed&&e.add(new h(n)),e.add(r.subscribe(t)),e},e}(o.y),h=function(t){function e(e){var n=t.call(this)||this;return n.parent=e,e.count++,n}return r.ZT(e,t),e.prototype.unsubscribe=function(){var e=this.parent;e.closed||this.closed||(t.prototype.unsubscribe.call(this),e.count-=1,0===e.count&&e.attemptedToUnsubscribe&&e.unsubscribe())},e}($.w)},5709(t,e,n){"use strict";n.d(e,{U:()=>$});var r=n(5987),i=n(979);function $(t,e){return function(n){if("function"!=typeof t)throw TypeError("argument is not a function. Are you looking for `mapTo()`?");return n.lift(new o(t,e))}}var o=function(){function t(t,e){this.project=t,this.thisArg=e}return t.prototype.call=function(t,e){return e.subscribe(new s(t,this.project,this.thisArg))},t}(),s=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.project=n,i.count=0,i.thisArg=r||i,i}return r.ZT(e,t),e.prototype._next=function(t){var e;try{e=this.project.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}this.destination.next(e)},e}(i.L)},2556(t,e,n){"use strict";n.d(e,{J:()=>$});var r=n(7746),i=n(3608);function $(t){return void 0===t&&(t=Number.POSITIVE_INFINITY),(0,r.zg)(i.y,t)}},7746(t,e,n){"use strict";n.d(e,{VS:()=>u,zg:()=>s});var r=n(5987),i=n(5709),$=n(5760),o=n(7604);function s(t,e,n){return void 0===n&&(n=Number.POSITIVE_INFINITY),"function"==typeof e?function(r){return r.pipe(s(function(n,r){return(0,$.D)(t(n,r)).pipe((0,i.U)(function(t,i){return e(n,t,r,i)}))},n))}:("number"==typeof e&&(n=e),function(e){return e.lift(new x(t,n))})}var x=function(){function t(t,e){void 0===e&&(e=Number.POSITIVE_INFINITY),this.project=t,this.concurrent=e}return t.prototype.call=function(t,e){return e.subscribe(new a(t,this.project,this.concurrent))},t}(),a=function(t){function e(e,n,r){void 0===r&&(r=Number.POSITIVE_INFINITY);var i=t.call(this,e)||this;return i.project=n,i.concurrent=r,i.hasCompleted=!1,i.buffer=[],i.active=0,i.index=0,i}return r.ZT(e,t),e.prototype._next=function(t){this.active<this.concurrent?this._tryNext(t):this.buffer.push(t)},e.prototype._tryNext=function(t){var e,n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this.active++,this._innerSub(e)},e.prototype._innerSub=function(t){var e=new o.IY(this),n=this.destination;n.add(e);var r=(0,o.ft)(t,e);r!==e&&n.add(r)},e.prototype._complete=function(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&this.destination.complete(),this.unsubscribe()},e.prototype.notifyNext=function(t){this.destination.next(t)},e.prototype.notifyComplete=function(){var t=this.buffer;this.active--,t.length>0?this._next(t.shift()):0===this.active&&this.hasCompleted&&this.destination.complete()},e}(o.Ds),u=s},9276(t,e,n){"use strict";n.d(e,{QV:()=>o,ht:()=>x});var r=n(5987),i=n(979),$=n(2632);function o(t,e){return void 0===e&&(e=0),function(n){return n.lift(new s(t,e))}}var s=function(){function t(t,e){void 0===e&&(e=0),this.scheduler=t,this.delay=e}return t.prototype.call=function(t,e){return e.subscribe(new x(t,this.scheduler,this.delay))},t}(),x=function(t){function e(e,n,r){void 0===r&&(r=0);var i=t.call(this,e)||this;return i.scheduler=n,i.delay=r,i}return r.ZT(e,t),e.dispatch=function(t){var e=t.notification,n=t.destination;e.observe(n),this.unsubscribe()},e.prototype.scheduleMessage=function(t){this.destination.add(this.scheduler.schedule(e.dispatch,this.delay,new a(t,this.destination)))},e.prototype._next=function(t){this.scheduleMessage($.P.createNext(t))},e.prototype._error=function(t){this.scheduleMessage($.P.createError(t)),this.unsubscribe()},e.prototype._complete=function(){this.scheduleMessage($.P.createComplete()),this.unsubscribe()},e}(i.L),a=function(t,e){this.notification=t,this.destination=e}},3018(t,e,n){"use strict";n.d(e,{x:()=>$});var r=n(5987),i=n(979);function $(){return function(t){return t.lift(new o(t))}}var o=function(){function t(t){this.connectable=t}return t.prototype.call=function(t,e){var n=this.connectable;n._refCount++;var r=new s(t,n),i=e.subscribe(r);return r.closed||(r.connection=n.connect()),i},t}(),s=function(t){function e(e,n){var r=t.call(this,e)||this;return r.connectable=n,r}return r.ZT(e,t),e.prototype._unsubscribe=function(){var t=this.connectable;if(t){this.connectable=null;var e=t._refCount;if(e<=0)this.connection=null;else if(t._refCount=e-1,e>1)this.connection=null;else{var n=this.connection,r=t._connection;this.connection=null,!r||n&&r!==n||r.unsubscribe()}}else this.connection=null},e}(i.L)},3109(t,e,n){"use strict";n.d(e,{r:()=>$});var r=n(2772),i=n(8760);function $(t,e){return new r.y(function(n){var r=new i.w,$=0;return r.add(e.schedule(function(){$!==t.length?(n.next(t[$++]),n.closed||r.add(this.schedule())):n.complete()})),r})}},8107(t,e,n){"use strict";n.d(e,{x:()=>u});var r=n(2772),i=n(8760),$=n(5050),o=n(3109),s=n(999),x=n(336),a=n(9217);function u(t,e){if(null!=t){var n,u,c,l,h,f;if((n=t)&&"function"==typeof n[$.L])return u=t,c=e,new r.y(function(t){var e=new i.w;return e.add(c.schedule(function(){var n=u[$.L]();e.add(n.subscribe({next:function(n){e.add(c.schedule(function(){return t.next(n)}))},error:function(n){e.add(c.schedule(function(){return t.error(n)}))},complete:function(){e.add(c.schedule(function(){return t.complete()}))}}))})),e});if((0,x.t)(t))return l=t,h=e,new r.y(function(t){var e=new i.w;return e.add(h.schedule(function(){return l.then(function(n){e.add(h.schedule(function(){t.next(n),e.add(h.schedule(function(){return t.complete()}))}))},function(n){e.add(h.schedule(function(){return t.error(n)}))})})),e});if((0,a.z)(t))return(0,o.r)(t,e);if((f=t)&&"function"==typeof f[s.hZ]||"string"==typeof t)return function(t,e){if(!t)throw Error("Iterable cannot be null");return new r.y(function(n){var r,$=new i.w;return $.add(function(){r&&"function"==typeof r.return&&r.return()}),$.add(e.schedule(function(){r=t[s.hZ](),$.add(e.schedule(function(){if(!n.closed){var t,e;try{var i=r.next();t=i.value,e=i.done}catch($){return void n.error($)}e?n.complete():(n.next(t),this.schedule())}}))})),$})}(t,e)}throw TypeError((null!==t&&typeof t||t)+" is not observable")}},6114(t,e,n){"use strict";n.d(e,{o:()=>i});var r=n(5987),i=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.scheduler=e,r.work=n,r.pending=!1,r}return r.ZT(e,t),e.prototype.schedule=function(t,e){if(void 0===e&&(e=0),this.closed)return this;this.state=t;var n=this.id,r=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(r,n,e)),this.pending=!0,this.delay=e,this.id=this.id||this.requestAsyncId(r,this.id,e),this},e.prototype.requestAsyncId=function(t,e,n){return void 0===n&&(n=0),setInterval(t.flush.bind(t,this),n)},e.prototype.recycleAsyncId=function(t,e,n){if(void 0===n&&(n=0),null!==n&&this.delay===n&&!1===this.pending)return e;clearInterval(e)},e.prototype.execute=function(t,e){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var n=this._execute(t,e);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(t,e){var n=!1,r=void 0;try{this.work(t)}catch(i){n=!0,r=!!i&&i||Error(i)}if(n)return this.unsubscribe(),r},e.prototype._unsubscribe=function(){var t=this.id,e=this.scheduler,n=e.actions,r=n.indexOf(this);this.work=null,this.state=null,this.pending=!1,this.scheduler=null,-1!==r&&n.splice(r,1),null!=t&&(this.id=this.recycleAsyncId(e,t,null)),this.delay=null},e}(function(t){function e(e,n){return t.call(this)||this}return r.ZT(e,t),e.prototype.schedule=function(t,e){return void 0===e&&(e=0),this},e}(n(8760).w))},8399(t,e,n){"use strict";n.d(e,{v:()=>$});var r=n(5987),i=n(8725),$=function(t){function e(n,r){void 0===r&&(r=i.b.now);var $=t.call(this,n,function(){return e.delegate&&e.delegate!==$?e.delegate.now():r()})||this;return $.actions=[],$.active=!1,$.scheduled=void 0,$}return r.ZT(e,t),e.prototype.schedule=function(n,r,i){return void 0===r&&(r=0),e.delegate&&e.delegate!==this?e.delegate.schedule(n,r,i):t.prototype.schedule.call(this,n,r,i)},e.prototype.flush=function(t){var e,n=this.actions;if(this.active)n.push(t);else{this.active=!0;do if(e=t.execute(t.state,t.delay))break;while(t=n.shift());if(this.active=!1,e){for(;t=n.shift();)t.unsubscribe();throw e}}},e}(i.b)},6650(t,e,n){"use strict";n.d(e,{e:()=>u,E:()=>a});var r=n(5987),i=1,$=Promise.resolve(),o={};function s(t){return t in o&&(delete o[t],!0)}var x=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.scheduler=e,r.work=n,r}return r.ZT(e,t),e.prototype.requestAsyncId=function(e,n,r){var x,a;return void 0===r&&(r=0),null!==r&&r>0?t.prototype.requestAsyncId.call(this,e,n,r):(e.actions.push(this),e.scheduled||(e.scheduled=(x=e.flush.bind(e,null),o[a=i++]=!0,$.then(function(){return s(a)&&x()}),a)))},e.prototype.recycleAsyncId=function(e,n,r){if(void 0===r&&(r=0),null!==r&&r>0||null===r&&this.delay>0)return t.prototype.recycleAsyncId.call(this,e,n,r);0===e.actions.length&&(s(n),e.scheduled=void 0)},e}(n(6114).o),a=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.ZT(e,t),e.prototype.flush=function(t){this.active=!0,this.scheduled=void 0;var e,n=this.actions,r=-1,i=n.length;t=t||n.shift();do if(e=t.execute(t.state,t.delay))break;while(++r<i&&(t=n.shift()));if(this.active=!1,e){for(;++r<i&&(t=n.shift());)t.unsubscribe();throw e}},e}(n(8399).v))(x),u=a},964(t,e,n){"use strict";n.d(e,{P:()=>$,z:()=>i});var r=n(6114),i=new(n(8399)).v(r.o),$=i},2546(t,e,n){"use strict";n.d(e,{c:()=>o,N:()=>$});var r=n(5987),i=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.scheduler=e,r.work=n,r}return r.ZT(e,t),e.prototype.schedule=function(e,n){return void 0===n&&(n=0),n>0?t.prototype.schedule.call(this,e,n):(this.delay=n,this.state=e,this.scheduler.flush(this),this)},e.prototype.execute=function(e,n){return n>0||this.closed?t.prototype.execute.call(this,e,n):this._execute(e,n)},e.prototype.requestAsyncId=function(e,n,r){return void 0===r&&(r=0),null!==r&&r>0||null===r&&this.delay>0?t.prototype.requestAsyncId.call(this,e,n,r):e.flush(this)},e}(n(6114).o),$=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.ZT(e,t),e}(n(8399).v))(i),o=$},999(t,e,n){"use strict";function r(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}n.d(e,{hZ:()=>i});var i=r()},5050(t,e,n){"use strict";n.d(e,{L:()=>r});var r="function"==typeof Symbol&&Symbol.observable||"@@observable"},3142(t,e,n){"use strict";n.d(e,{b:()=>r});var r="function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random()},6565(t,e,n){"use strict";n.d(e,{W:()=>r});var r=function(){function t(){return Error.call(this),this.message="argument out of range",this.name="ArgumentOutOfRangeError",this}return t.prototype=Object.create(Error.prototype),t}()},6929(t,e,n){"use strict";n.d(e,{K:()=>r});var r=function(){function t(){return Error.call(this),this.message="no elements in sequence",this.name="EmptyError",this}return t.prototype=Object.create(Error.prototype),t}()},1016(t,e,n){"use strict";n.d(e,{N:()=>r});var r=function(){function t(){return Error.call(this),this.message="object unsubscribed",this.name="ObjectUnsubscribedError",this}return t.prototype=Object.create(Error.prototype),t}()},1462(t,e,n){"use strict";n.d(e,{W:()=>r});var r=function(){function t(){return Error.call(this),this.message="Timeout has occurred",this.name="TimeoutError",this}return t.prototype=Object.create(Error.prototype),t}()},8782(t,e,n){"use strict";n.d(e,{B:()=>r});var r=function(){function t(t){return Error.call(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(t,e){return e+1+") "+t.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t,this}return t.prototype=Object.create(Error.prototype),t}()},3642(t,e,n){"use strict";n.d(e,{_:()=>i});var r=n(979);function i(t){for(;t;){var e=t,n=e.closed,i=e.destination,$=e.isStopped;if(n||$)return!1;t=i&&i instanceof r.L?i:null}return!0}},1644(t,e,n){"use strict";function r(t){setTimeout(function(){throw t},0)}n.d(e,{z:()=>r})},3608(t,e,n){"use strict";function r(t){return t}n.d(e,{y:()=>r})},9026(t,e,n){"use strict";n.d(e,{k:()=>r});var r=Array.isArray||function(t){return t&&"number"==typeof t.length}},9217(t,e,n){"use strict";n.d(e,{z:()=>r});var r=function(t){return t&&"number"==typeof t.length&&"function"!=typeof t}},4156(t,e,n){"use strict";function r(t){return"function"==typeof t}n.d(e,{m:()=>r})},5812(t,e,n){"use strict";n.d(e,{k:()=>i});var r=n(9026);function i(t){return!(0,r.k)(t)&&t-parseFloat(t)+1>=0}},2009(t,e,n){"use strict";function r(t){return null!==t&&"object"==typeof t}n.d(e,{K:()=>r})},336(t,e,n){"use strict";function r(t){return!!t&&"function"!=typeof t.subscribe&&"function"==typeof t.then}n.d(e,{t:()=>r})},7507(t,e,n){"use strict";function r(t){return t&&"function"==typeof t.schedule}n.d(e,{K:()=>r})},3306(t,e,n){"use strict";function r(){}n.d(e,{Z:()=>r})},8463(t,e,n){"use strict";function r(t,e){function n(){return!n.pred.apply(n.thisArg,arguments)}return n.pred=t,n.thisArg=e,n}n.d(e,{f:()=>r})},2561(t,e,n){"use strict";n.d(e,{U:()=>$,z:()=>i});var r=n(3608);function i(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return $(t)}function $(t){return 0===t.length?r.y:1===t.length?t[0]:function(e){return t.reduce(function(t,e){return e(t)},e)}}},7843(t,e,n){"use strict";n.d(e,{s:()=>u});var r=n(6900),i=n(1644),$=n(999),o=n(5050),s=n(9217),x=n(336),a=n(2009),u=function(t){var e,n,u;if(t&&"function"==typeof t[o.L])return u=t,function(t){var e=u[o.L]();if("function"!=typeof e.subscribe)throw TypeError("Provided object does not correctly implement Symbol.observable");return e.subscribe(t)};if((0,s.z)(t))return(0,r.V)(t);if((0,x.t)(t))return n=t,function(t){return n.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,i.z),t};if(t&&"function"==typeof t[$.hZ])return e=t,function(t){for(var n=e[$.hZ]();;){var r=void 0;try{r=n.next()}catch(i){return t.error(i),t}if(r.done){t.complete();break}if(t.next(r.value),t.closed)break}return"function"==typeof n.return&&t.add(function(){n.return&&n.return()}),t};throw TypeError("You provided "+((0,a.K)(t)?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, Array, or Iterable.")}},6900(t,e,n){"use strict";n.d(e,{V:()=>r});var r=function(t){return function(e){for(var n=0,r=t.length;n<r&&!e.closed;n++)e.next(t[n]);e.complete()}}},2080(t,e,n){"use strict";n.d(e,{D:()=>s});var r=n(5987),i=function(t){function e(e,n,r){var i=t.call(this)||this;return i.parent=e,i.outerValue=n,i.outerIndex=r,i.index=0,i}return r.ZT(e,t),e.prototype._next=function(t){this.parent.notifyNext(this.outerValue,t,this.outerIndex,this.index++,this)},e.prototype._error=function(t){this.parent.notifyError(t,this),this.unsubscribe()},e.prototype._complete=function(){this.parent.notifyComplete(this),this.unsubscribe()},e}(n(979).L),$=n(7843),o=n(2772);function s(t,e,n,r,s){if(void 0===s&&(s=new i(t,n,r)),!s.closed)return e instanceof o.y?e.subscribe(s):(0,$.s)(e)(s)}},1717(t,e,n){"use strict";n.r(e),n.d(e,{audit:()=>$,auditTime:()=>u,buffer:()=>c,bufferCount:()=>d,bufferTime:()=>_,bufferToggle:()=>A,bufferWhen:()=>L,catchError:()=>D,combineAll:()=>U,combineLatest:()=>z,concat:()=>V,concatAll:()=>q.u,concatMap:()=>G,concatMapTo:()=>Y,count:()=>K,debounce:()=>X,debounceTime:()=>tn,defaultIfEmpty:()=>to,delay:()=>tc,delayWhen:()=>tp,dematerialize:()=>t_,distinct:()=>tw,distinctUntilChanged:()=>tS,distinctUntilKeyChanged:()=>tE,elementAt:()=>tN,endWith:()=>tP,every:()=>tD,exhaust:()=>tj,exhaustMap:()=>tz,expand:()=>tq,filter:()=>t3.h,finalize:()=>tY,find:()=>tJ,findIndex:()=>et,first:()=>en,flatMap:()=>Z.VS,groupBy:()=>er.v,ignoreElements:()=>ei,isEmpty:()=>es,last:()=>eh,map:()=>tW.U,mapTo:()=>ef,materialize:()=>e0,max:()=>e8,merge:()=>eS,mergeAll:()=>e2.J,mergeMap:()=>Z.zg,mergeMapTo:()=>ek,mergeScan:()=>eE,min:()=>e4,multicast:()=>e5,observeOn:()=>eI.QV,onErrorResumeNext:()=>eA,pairwise:()=>eL,partition:()=>eD,pluck:()=>eO,publish:()=>eU,publishBehavior:()=>eW,publishLast:()=>eH,publishReplay:()=>eq,race:()=>eG,reduce:()=>ew,refCount:()=>no.x,repeat:()=>eY,repeatWhen:()=>eJ,retry:()=>nt,retryWhen:()=>nr,sample:()=>ns,sampleTime:()=>nu,scan:()=>eb,sequenceEqual:()=>nf,share:()=>ng,shareReplay:()=>nb,single:()=>n_,skip:()=>nw,skipLast:()=>nS,skipUntil:()=>nE,skipWhile:()=>n4,startWith:()=>nM,subscribeOn:()=>nR,switchAll:()=>nD,switchMap:()=>nN,switchMapTo:()=>nO,take:()=>tT,takeLast:()=>eu,takeUntil:()=>nB,takeWhile:()=>nF,tap:()=>nq,throttle:()=>nK,throttleTime:()=>n9,throwIfEmpty:()=>tC,timeInterval:()=>rr,timeout:()=>ru,timeoutWith:()=>ro,timestamp:()=>rc,toArray:()=>rf,window:()=>rd,windowCount:()=>ry,windowTime:()=>r_,windowToggle:()=>r2,windowWhen:()=>r6,withLatestFrom:()=>rC,zip:()=>rA,zipAll:()=>rT});var r=n(5987),i=n(7604);function $(t){return function(e){return e.lift(new o(t))}}var o=function(){function t(t){this.durationSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new s(t,this.durationSelector))},t}(),s=function(t){function e(e,n){var r=t.call(this,e)||this;return r.durationSelector=n,r.hasValue=!1,r}return r.ZT(e,t),e.prototype._next=function(t){if(this.value=t,this.hasValue=!0,!this.throttled){var e=void 0;try{e=(0,this.durationSelector)(t)}catch(n){return this.destination.error(n)}var r=(0,i.ft)(e,new i.IY(this));!r||r.closed?this.clearThrottle():this.add(this.throttled=r)}},e.prototype.clearThrottle=function(){var t=this,e=t.value,n=t.hasValue,r=t.throttled;r&&(this.remove(r),this.throttled=void 0,r.unsubscribe()),n&&(this.value=void 0,this.hasValue=!1,this.destination.next(e))},e.prototype.notifyNext=function(){this.clearThrottle()},e.prototype.notifyComplete=function(){this.clearThrottle()},e}(i.Ds),x=n(964),a=n(9604);function u(t,e){return void 0===e&&(e=x.P),$(function(){return(0,a.H)(t,e)})}function c(t){return function(e){return e.lift(new l(t))}}var l=function(){function t(t){this.closingNotifier=t}return t.prototype.call=function(t,e){return e.subscribe(new h(t,this.closingNotifier))},t}(),h=function(t){function e(e,n){var r=t.call(this,e)||this;return r.buffer=[],r.add((0,i.ft)(n,new i.IY(r))),r}return r.ZT(e,t),e.prototype._next=function(t){this.buffer.push(t)},e.prototype.notifyNext=function(){var t=this.buffer;this.buffer=[],this.destination.next(t)},e}(i.Ds),f=n(979);function d(t,e){return void 0===e&&(e=null),function(n){return n.lift(new p(t,e))}}var p=function(){function t(t,e){this.bufferSize=t,this.startBufferEvery=e,this.subscriberClass=e&&t!==e?g:y}return t.prototype.call=function(t,e){return e.subscribe(new this.subscriberClass(t,this.bufferSize,this.startBufferEvery))},t}(),y=function(t){function e(e,n){var r=t.call(this,e)||this;return r.bufferSize=n,r.buffer=[],r}return r.ZT(e,t),e.prototype._next=function(t){var e=this.buffer;e.push(t),e.length==this.bufferSize&&(this.destination.next(e),this.buffer=[])},e.prototype._complete=function(){var e=this.buffer;e.length>0&&this.destination.next(e),t.prototype._complete.call(this)},e}(f.L),g=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.bufferSize=n,i.startBufferEvery=r,i.buffers=[],i.count=0,i}return r.ZT(e,t),e.prototype._next=function(t){var e=this,n=e.bufferSize,r=e.startBufferEvery,i=e.buffers,$=e.count;this.count++,$%r==0&&i.push([]);for(var o=i.length;o--;){var s=i[o];s.push(t),s.length===n&&(i.splice(o,1),this.destination.next(s))}},e.prototype._complete=function(){for(var e=this.buffers,n=this.destination;e.length>0;){var r=e.shift();r.length>0&&n.next(r)}t.prototype._complete.call(this)},e}(f.L),b=n(7507);function _(t){var e=arguments.length,n=x.P;(0,b.K)(arguments[arguments.length-1])&&(n=arguments[arguments.length-1],e--);var r=null;e>=2&&(r=arguments[1]);var i=Number.POSITIVE_INFINITY;return e>=3&&(i=arguments[2]),function(e){return e.lift(new m(t,r,i,n))}}var m=function(){function t(t,e,n,r){this.bufferTimeSpan=t,this.bufferCreationInterval=e,this.maxBufferSize=n,this.scheduler=r}return t.prototype.call=function(t,e){return e.subscribe(new w(t,this.bufferTimeSpan,this.bufferCreationInterval,this.maxBufferSize,this.scheduler))},t}(),v=function(){this.buffer=[]},w=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;o.bufferTimeSpan=n,o.bufferCreationInterval=r,o.maxBufferSize=i,o.scheduler=$,o.contexts=[];var s=o.openContext();if(o.timespanOnly=null==r||r<0,o.timespanOnly){var x={subscriber:o,context:s,bufferTimeSpan:n};o.add(s.closeAction=$.schedule(S,n,x))}else{var a={subscriber:o,context:s},u={bufferTimeSpan:n,bufferCreationInterval:r,subscriber:o,scheduler:$};o.add(s.closeAction=$.schedule(E,n,a)),o.add($.schedule(k,r,u))}return o}return r.ZT(e,t),e.prototype._next=function(t){for(var e,n=this.contexts,r=n.length,i=0;i<r;i++){var $=n[i],o=$.buffer;o.push(t),o.length==this.maxBufferSize&&(e=$)}e&&this.onBufferFull(e)},e.prototype._error=function(e){this.contexts.length=0,t.prototype._error.call(this,e)},e.prototype._complete=function(){for(var e=this.contexts,n=this.destination;e.length>0;){var r=e.shift();n.next(r.buffer)}t.prototype._complete.call(this)},e.prototype._unsubscribe=function(){this.contexts=null},e.prototype.onBufferFull=function(t){this.closeContext(t);var e=t.closeAction;if(e.unsubscribe(),this.remove(e),!this.closed&&this.timespanOnly){t=this.openContext();var n=this.bufferTimeSpan,r={subscriber:this,context:t,bufferTimeSpan:n};this.add(t.closeAction=this.scheduler.schedule(S,n,r))}},e.prototype.openContext=function(){var t=new v;return this.contexts.push(t),t},e.prototype.closeContext=function(t){this.destination.next(t.buffer);var e=this.contexts;(e?e.indexOf(t):-1)>=0&&e.splice(e.indexOf(t),1)},e}(f.L);function S(t){var e=t.subscriber,n=t.context;n&&e.closeContext(n),e.closed||(t.context=e.openContext(),t.context.closeAction=this.schedule(t,t.bufferTimeSpan))}function k(t){var e=t.bufferCreationInterval,n=t.bufferTimeSpan,r=t.subscriber,i=t.scheduler,$=r.openContext();r.closed||(r.add($.closeAction=i.schedule(E,n,{subscriber:r,context:$})),this.schedule(t,e))}function E(t){var e=t.subscriber,n=t.context;e.closeContext(n)}var C=n(8760),M=n(2080),I=n(2039);function A(t,e){return function(n){return n.lift(new T(t,e))}}var T=function(){function t(t,e){this.openings=t,this.closingSelector=e}return t.prototype.call=function(t,e){return e.subscribe(new R(t,this.openings,this.closingSelector))},t}(),R=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.closingSelector=r,i.contexts=[],i.add((0,M.D)(i,n)),i}return r.ZT(e,t),e.prototype._next=function(t){for(var e=this.contexts,n=e.length,r=0;r<n;r++)e[r].buffer.push(t)},e.prototype._error=function(e){for(var n=this.contexts;n.length>0;){var r=n.shift();r.subscription.unsubscribe(),r.buffer=null,r.subscription=null}this.contexts=null,t.prototype._error.call(this,e)},e.prototype._complete=function(){for(var e=this.contexts;e.length>0;){var n=e.shift();this.destination.next(n.buffer),n.subscription.unsubscribe(),n.buffer=null,n.subscription=null}this.contexts=null,t.prototype._complete.call(this)},e.prototype.notifyNext=function(t,e){t?this.closeBuffer(t):this.openBuffer(e)},e.prototype.notifyComplete=function(t){this.closeBuffer(t.context)},e.prototype.openBuffer=function(t){try{var e=this.closingSelector.call(this,t);e&&this.trySubscribe(e)}catch(n){this._error(n)}},e.prototype.closeBuffer=function(t){var e=this.contexts;if(e&&t){var n=t.buffer,r=t.subscription;this.destination.next(n),e.splice(e.indexOf(t),1),this.remove(r),r.unsubscribe()}},e.prototype.trySubscribe=function(t){var e=this.contexts,n=new C.w,r={buffer:[],subscription:n};e.push(r);var i=(0,M.D)(this,t,r);!i||i.closed?this.closeBuffer(r):(i.context=r,this.add(i),n.add(i))},e}(I.L);function L(t){return function(e){return e.lift(new N(t))}}var N=function(){function t(t){this.closingSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new P(t,this.closingSelector))},t}(),P=function(t){function e(e,n){var r=t.call(this,e)||this;return r.closingSelector=n,r.subscribing=!1,r.openBuffer(),r}return r.ZT(e,t),e.prototype._next=function(t){this.buffer.push(t)},e.prototype._complete=function(){var e=this.buffer;e&&this.destination.next(e),t.prototype._complete.call(this)},e.prototype._unsubscribe=function(){this.buffer=void 0,this.subscribing=!1},e.prototype.notifyNext=function(){this.openBuffer()},e.prototype.notifyComplete=function(){this.subscribing?this.complete():this.openBuffer()},e.prototype.openBuffer=function(){var t=this.closingSubscription;t&&(this.remove(t),t.unsubscribe());var e,n=this.buffer;this.buffer&&this.destination.next(n),this.buffer=[];try{e=(0,this.closingSelector)()}catch(r){return this.error(r)}t=new C.w,this.closingSubscription=t,this.add(t),this.subscribing=!0,t.add((0,i.ft)(e,new i.IY(this))),this.subscribing=!1},e}(i.Ds);function D(t){return function(e){var n=new O(t),r=e.lift(n);return n.caught=r}}var O=function(){function t(t){this.selector=t}return t.prototype.call=function(t,e){return e.subscribe(new B(t,this.selector,this.caught))},t}(),B=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.selector=n,i.caught=r,i}return r.ZT(e,t),e.prototype.error=function(e){if(!this.isStopped){var n=void 0;try{n=this.selector(e,this.caught)}catch(r){return void t.prototype.error.call(this,r)}this._unsubscribeAndRecycle();var $=new i.IY(this);this.add($);var o=(0,i.ft)(n,$);o!==$&&this.add(o)}},e}(i.Ds),j=n(5142);function U(t){return function(e){return e.lift(new j.Ms(t))}}var F=n(9026),W=n(5760);function z(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=null;return"function"==typeof t[t.length-1]&&(n=t.pop()),1===t.length&&(0,F.k)(t[0])&&(t=t[0].slice()),function(e){return e.lift.call((0,W.D)([e].concat(t)),new j.Ms(n))}}var H=n(9795);function V(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){return e.lift.call(H.z.apply(void 0,[e].concat(t)))}}var q=n(2257),Z=n(7746);function G(t,e){return(0,Z.zg)(t,e,1)}function Y(t,e){return G(function(){return t},e)}function K(t){return function(e){return e.lift(new Q(t,e))}}var Q=function(){function t(t,e){this.predicate=t,this.source=e}return t.prototype.call=function(t,e){return e.subscribe(new J(t,this.predicate,this.source))},t}(),J=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.predicate=n,i.source=r,i.count=0,i.index=0,i}return r.ZT(e,t),e.prototype._next=function(t){this.predicate?this._tryPredicate(t):this.count++},e.prototype._tryPredicate=function(t){var e;try{e=this.predicate(t,this.index++,this.source)}catch(n){return void this.destination.error(n)}e&&this.count++},e.prototype._complete=function(){this.destination.next(this.count),this.destination.complete()},e}(f.L);function X(t){return function(e){return e.lift(new tt(t))}}var tt=function(){function t(t){this.durationSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new te(t,this.durationSelector))},t}(),te=function(t){function e(e,n){var r=t.call(this,e)||this;return r.durationSelector=n,r.hasValue=!1,r}return r.ZT(e,t),e.prototype._next=function(t){try{var e=this.durationSelector.call(this,t);e&&this._tryNext(t,e)}catch(n){this.destination.error(n)}},e.prototype._complete=function(){this.emitValue(),this.destination.complete()},e.prototype._tryNext=function(t,e){var n=this.durationSubscription;this.value=t,this.hasValue=!0,n&&(n.unsubscribe(),this.remove(n)),(n=(0,i.ft)(e,new i.IY(this)))&&!n.closed&&this.add(this.durationSubscription=n)},e.prototype.notifyNext=function(){this.emitValue()},e.prototype.notifyComplete=function(){this.emitValue()},e.prototype.emitValue=function(){if(this.hasValue){var e=this.value,n=this.durationSubscription;n&&(this.durationSubscription=void 0,n.unsubscribe(),this.remove(n)),this.value=void 0,this.hasValue=!1,t.prototype._next.call(this,e)}},e}(i.Ds);function tn(t,e){return void 0===e&&(e=x.P),function(n){return n.lift(new tr(t,e))}}var tr=function(){function t(t,e){this.dueTime=t,this.scheduler=e}return t.prototype.call=function(t,e){return e.subscribe(new ti(t,this.dueTime,this.scheduler))},t}(),ti=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.dueTime=n,i.scheduler=r,i.debouncedSubscription=null,i.lastValue=null,i.hasValue=!1,i}return r.ZT(e,t),e.prototype._next=function(t){this.clearDebounce(),this.lastValue=t,this.hasValue=!0,this.add(this.debouncedSubscription=this.scheduler.schedule(t$,this.dueTime,this))},e.prototype._complete=function(){this.debouncedNext(),this.destination.complete()},e.prototype.debouncedNext=function(){if(this.clearDebounce(),this.hasValue){var t=this.lastValue;this.lastValue=null,this.hasValue=!1,this.destination.next(t)}},e.prototype.clearDebounce=function(){var t=this.debouncedSubscription;null!==t&&(this.remove(t),t.unsubscribe(),this.debouncedSubscription=null)},e}(f.L);function t$(t){t.debouncedNext()}function to(t){return void 0===t&&(t=null),function(e){return e.lift(new ts(t))}}var ts=function(){function t(t){this.defaultValue=t}return t.prototype.call=function(t,e){return e.subscribe(new tx(t,this.defaultValue))},t}(),tx=function(t){function e(e,n){var r=t.call(this,e)||this;return r.defaultValue=n,r.isEmpty=!0,r}return r.ZT(e,t),e.prototype._next=function(t){this.isEmpty=!1,this.destination.next(t)},e.prototype._complete=function(){this.isEmpty&&this.destination.next(this.defaultValue),this.destination.complete()},e}(f.L);function ta(t){return t instanceof Date&&!isNaN(+t)}var tu=n(2632);function tc(t,e){void 0===e&&(e=x.P);var n=ta(t)?+t-e.now():Math.abs(t);return function(t){return t.lift(new tl(n,e))}}var tl=function(){function t(t,e){this.delay=t,this.scheduler=e}return t.prototype.call=function(t,e){return e.subscribe(new th(t,this.delay,this.scheduler))},t}(),th=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.delay=n,i.scheduler=r,i.queue=[],i.active=!1,i.errored=!1,i}return r.ZT(e,t),e.dispatch=function(t){for(var e=t.source,n=e.queue,r=t.scheduler,i=t.destination;n.length>0&&n[0].time-r.now()<=0;)n.shift().notification.observe(i);if(n.length>0){var $=Math.max(0,n[0].time-r.now());this.schedule(t,$)}else this.unsubscribe(),e.active=!1},e.prototype._schedule=function(t){this.active=!0,this.destination.add(t.schedule(e.dispatch,this.delay,{source:this,destination:this.destination,scheduler:t}))},e.prototype.scheduleNotification=function(t){if(!0!==this.errored){var e=this.scheduler,n=new tf(e.now()+this.delay,t);this.queue.push(n),!1===this.active&&this._schedule(e)}},e.prototype._next=function(t){this.scheduleNotification(tu.P.createNext(t))},e.prototype._error=function(t){this.errored=!0,this.queue=[],this.destination.error(t),this.unsubscribe()},e.prototype._complete=function(){this.scheduleNotification(tu.P.createComplete()),this.unsubscribe()},e}(f.L),tf=function(t,e){this.time=t,this.notification=e},td=n(2772);function tp(t,e){return e?function(n){return new tg(n,e).lift(new t0(t))}:function(e){return e.lift(new t0(t))}}var t0=function(){function t(t){this.delayDurationSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new ty(t,this.delayDurationSelector))},t}(),ty=function(t){function e(e,n){var r=t.call(this,e)||this;return r.delayDurationSelector=n,r.completed=!1,r.delayNotifierSubscriptions=[],r.index=0,r}return r.ZT(e,t),e.prototype.notifyNext=function(t,e,n,r,i){this.destination.next(t),this.removeSubscription(i),this.tryComplete()},e.prototype.notifyError=function(t,e){this._error(t)},e.prototype.notifyComplete=function(t){var e=this.removeSubscription(t);e&&this.destination.next(e),this.tryComplete()},e.prototype._next=function(t){var e=this.index++;try{var n=this.delayDurationSelector(t,e);n&&this.tryDelay(n,t)}catch(r){this.destination.error(r)}},e.prototype._complete=function(){this.completed=!0,this.tryComplete(),this.unsubscribe()},e.prototype.removeSubscription=function(t){t.unsubscribe();var e=this.delayNotifierSubscriptions.indexOf(t);return -1!==e&&this.delayNotifierSubscriptions.splice(e,1),t.outerValue},e.prototype.tryDelay=function(t,e){var n=(0,M.D)(this,t,e);n&&!n.closed&&(this.destination.add(n),this.delayNotifierSubscriptions.push(n))},e.prototype.tryComplete=function(){this.completed&&0===this.delayNotifierSubscriptions.length&&this.destination.complete()},e}(I.L),tg=function(t){function e(e,n){var r=t.call(this)||this;return r.source=e,r.subscriptionDelay=n,r}return r.ZT(e,t),e.prototype._subscribe=function(t){this.subscriptionDelay.subscribe(new tb(t,this.source))},e}(td.y),tb=function(t){function e(e,n){var r=t.call(this)||this;return r.parent=e,r.source=n,r.sourceSubscribed=!1,r}return r.ZT(e,t),e.prototype._next=function(t){this.subscribeToSource()},e.prototype._error=function(t){this.unsubscribe(),this.parent.error(t)},e.prototype._complete=function(){this.unsubscribe(),this.subscribeToSource()},e.prototype.subscribeToSource=function(){this.sourceSubscribed||(this.sourceSubscribed=!0,this.unsubscribe(),this.source.subscribe(this.parent))},e}(f.L);function t_(){return function(t){return t.lift(new tm)}}var tm=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new tv(t))},t}(),tv=function(t){function e(e){return t.call(this,e)||this}return r.ZT(e,t),e.prototype._next=function(t){t.observe(this.destination)},e}(f.L);function tw(t,e){return function(n){return n.lift(new t8(t,e))}}var t8=function(){function t(t,e){this.keySelector=t,this.flushes=e}return t.prototype.call=function(t,e){return e.subscribe(new t1(t,this.keySelector,this.flushes))},t}(),t1=function(t){function e(e,n,r){var $=t.call(this,e)||this;return $.keySelector=n,$.values=new Set,r&&$.add((0,i.ft)(r,new i.IY($))),$}return r.ZT(e,t),e.prototype.notifyNext=function(){this.values.clear()},e.prototype.notifyError=function(t){this._error(t)},e.prototype._next=function(t){this.keySelector?this._useKeySelector(t):this._finalizeNext(t,t)},e.prototype._useKeySelector=function(t){var e,n=this.destination;try{e=this.keySelector(t)}catch(r){return void n.error(r)}this._finalizeNext(e,t)},e.prototype._finalizeNext=function(t,e){var n=this.values;n.has(t)||(n.add(t),this.destination.next(e))},e}(i.Ds);function tS(t,e){return function(n){return n.lift(new t2(t,e))}}var t2=function(){function t(t,e){this.compare=t,this.keySelector=e}return t.prototype.call=function(t,e){return e.subscribe(new tk(t,this.compare,this.keySelector))},t}(),tk=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.keySelector=r,i.hasKey=!1,"function"==typeof n&&(i.compare=n),i}return r.ZT(e,t),e.prototype.compare=function(t,e){return t===e},e.prototype._next=function(t){try{var e,n=this.keySelector;e=n?n(t):t}catch(r){return this.destination.error(r)}var i=!1;if(this.hasKey)try{i=(0,this.compare)(this.key,e)}catch($){return this.destination.error($)}else this.hasKey=!0;i||(this.key=e,this.destination.next(t))},e}(f.L);function tE(t,e){return tS(function(n,r){return e?e(n[t],r[t]):n[t]===r[t]})}var t6=n(6565),t3=n(6008),t4=n(6929);function tC(t){return void 0===t&&(t=tI),function(e){return e.lift(new t5(t))}}var t5=function(){function t(t){this.errorFactory=t}return t.prototype.call=function(t,e){return e.subscribe(new tM(t,this.errorFactory))},t}(),tM=function(t){function e(e,n){var r=t.call(this,e)||this;return r.errorFactory=n,r.hasValue=!1,r}return r.ZT(e,t),e.prototype._next=function(t){this.hasValue=!0,this.destination.next(t)},e.prototype._complete=function(){if(this.hasValue)return this.destination.complete();var t=void 0;try{t=this.errorFactory()}catch(e){t=e}this.destination.error(t)},e}(f.L);function tI(){return new t4.K}var tA=n(5631);function tT(t){return function(e){return 0===t?(0,tA.c)():e.lift(new tR(t))}}var tR=function(){function t(t){if(this.total=t,this.total<0)throw new t6.W}return t.prototype.call=function(t,e){return e.subscribe(new tL(t,this.total))},t}(),tL=function(t){function e(e,n){var r=t.call(this,e)||this;return r.total=n,r.count=0,r}return r.ZT(e,t),e.prototype._next=function(t){var e=this.total,n=++this.count;n<=e&&(this.destination.next(t),n===e&&(this.destination.complete(),this.unsubscribe()))},e}(f.L);function tN(t,e){if(t<0)throw new t6.W;var n=arguments.length>=2;return function(r){return r.pipe((0,t3.h)(function(e,n){return n===t}),tT(1),n?to(e):tC(function(){return new t6.W}))}}var t7=n(8170);function tP(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){return(0,H.z)(e,t7.of.apply(void 0,t))}}function tD(t,e){return function(n){return n.lift(new tO(t,e,n))}}var tO=function(){function t(t,e,n){this.predicate=t,this.thisArg=e,this.source=n}return t.prototype.call=function(t,e){return e.subscribe(new tB(t,this.predicate,this.thisArg,this.source))},t}(),tB=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $.predicate=n,$.thisArg=r,$.source=i,$.index=0,$.thisArg=r||$,$}return r.ZT(e,t),e.prototype.notifyComplete=function(t){this.destination.next(t),this.destination.complete()},e.prototype._next=function(t){var e=!1;try{e=this.predicate.call(this.thisArg,t,this.index++,this.source)}catch(n){return void this.destination.error(n)}e||this.notifyComplete(!1)},e.prototype._complete=function(){this.notifyComplete(!0)},e}(f.L);function tj(){return function(t){return t.lift(new tU)}}var tU=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new tF(t))},t}(),tF=function(t){function e(e){var n=t.call(this,e)||this;return n.hasCompleted=!1,n.hasSubscription=!1,n}return r.ZT(e,t),e.prototype._next=function(t){this.hasSubscription||(this.hasSubscription=!0,this.add((0,i.ft)(t,new i.IY(this))))},e.prototype._complete=function(){this.hasCompleted=!0,this.hasSubscription||this.destination.complete()},e.prototype.notifyComplete=function(){this.hasSubscription=!1,this.hasCompleted&&this.destination.complete()},e}(i.Ds),tW=n(5709);function tz(t,e){return e?function(n){return n.pipe(tz(function(n,r){return(0,W.D)(t(n,r)).pipe((0,tW.U)(function(t,i){return e(n,t,r,i)}))}))}:function(e){return e.lift(new tH(t))}}var tH=function(){function t(t){this.project=t}return t.prototype.call=function(t,e){return e.subscribe(new tV(t,this.project))},t}(),tV=function(t){function e(e,n){var r=t.call(this,e)||this;return r.project=n,r.hasSubscription=!1,r.hasCompleted=!1,r.index=0,r}return r.ZT(e,t),e.prototype._next=function(t){this.hasSubscription||this.tryNext(t)},e.prototype.tryNext=function(t){var e,n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this.hasSubscription=!0,this._innerSub(e)},e.prototype._innerSub=function(t){var e=new i.IY(this),n=this.destination;n.add(e);var r=(0,i.ft)(t,e);r!==e&&n.add(r)},e.prototype._complete=function(){this.hasCompleted=!0,this.hasSubscription||this.destination.complete(),this.unsubscribe()},e.prototype.notifyNext=function(t){this.destination.next(t)},e.prototype.notifyError=function(t){this.destination.error(t)},e.prototype.notifyComplete=function(){this.hasSubscription=!1,this.hasCompleted&&this.destination.complete()},e}(i.Ds);function tq(t,e,n){return void 0===e&&(e=Number.POSITIVE_INFINITY),e=1>(e||0)?Number.POSITIVE_INFINITY:e,function(r){return r.lift(new tZ(t,e,n))}}var tZ=function(){function t(t,e,n){this.project=t,this.concurrent=e,this.scheduler=n}return t.prototype.call=function(t,e){return e.subscribe(new tG(t,this.project,this.concurrent,this.scheduler))},t}(),tG=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $.project=n,$.concurrent=r,$.scheduler=i,$.index=0,$.active=0,$.hasCompleted=!1,r<Number.POSITIVE_INFINITY&&($.buffer=[]),$}return r.ZT(e,t),e.dispatch=function(t){var e=t.subscriber,n=t.result,r=t.value,i=t.index;e.subscribeToProjection(n,r,i)},e.prototype._next=function(t){var n=this.destination;if(n.closed)this._complete();else{var r=this.index++;if(this.active<this.concurrent){n.next(t);try{var i=(0,this.project)(t,r);if(this.scheduler){var $={subscriber:this,result:i,value:t,index:r};this.destination.add(this.scheduler.schedule(e.dispatch,0,$))}else this.subscribeToProjection(i,t,r)}catch(o){n.error(o)}}else this.buffer.push(t)}},e.prototype.subscribeToProjection=function(t,e,n){this.active++,this.destination.add((0,i.ft)(t,new i.IY(this)))},e.prototype._complete=function(){this.hasCompleted=!0,this.hasCompleted&&0===this.active&&this.destination.complete(),this.unsubscribe()},e.prototype.notifyNext=function(t){this._next(t)},e.prototype.notifyComplete=function(){var t=this.buffer;this.active--,t&&t.length>0&&this._next(t.shift()),this.hasCompleted&&0===this.active&&this.destination.complete()},e}(i.Ds);function tY(t){return function(e){return e.lift(new tK(t))}}var tK=function(){function t(t){this.callback=t}return t.prototype.call=function(t,e){return e.subscribe(new tQ(t,this.callback))},t}(),tQ=function(t){function e(e,n){var r=t.call(this,e)||this;return r.add(new C.w(n)),r}return r.ZT(e,t),e}(f.L);function tJ(t,e){if("function"!=typeof t)throw TypeError("predicate is not a function");return function(n){return n.lift(new t9(t,n,!1,e))}}var t9=function(){function t(t,e,n,r){this.predicate=t,this.source=e,this.yieldIndex=n,this.thisArg=r}return t.prototype.call=function(t,e){return e.subscribe(new tX(t,this.predicate,this.source,this.yieldIndex,this.thisArg))},t}(),tX=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;return o.predicate=n,o.source=r,o.yieldIndex=i,o.thisArg=$,o.index=0,o}return r.ZT(e,t),e.prototype.notifyComplete=function(t){var e=this.destination;e.next(t),e.complete(),this.unsubscribe()},e.prototype._next=function(t){var e=this.predicate,n=this.thisArg,r=this.index++;try{e.call(n||this,t,r,this.source)&&this.notifyComplete(this.yieldIndex?r:t)}catch(i){this.destination.error(i)}},e.prototype._complete=function(){this.notifyComplete(this.yieldIndex?-1:void 0)},e}(f.L);function et(t,e){return function(n){return n.lift(new t9(t,n,!0,e))}}var ee=n(3608);function en(t,e){var n=arguments.length>=2;return function(r){return r.pipe(t?(0,t3.h)(function(e,n){return t(e,n,r)}):ee.y,tT(1),n?to(e):tC(function(){return new t4.K}))}}var er=n(1120);function ei(){return function(t){return t.lift(new e$)}}var e$=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new eo(t))},t}(),eo=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.ZT(e,t),e.prototype._next=function(t){},e}(f.L);function es(){return function(t){return t.lift(new ex)}}var ex=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new ea(t))},t}(),ea=function(t){function e(e){return t.call(this,e)||this}return r.ZT(e,t),e.prototype.notifyComplete=function(t){var e=this.destination;e.next(t),e.complete()},e.prototype._next=function(t){this.notifyComplete(!1)},e.prototype._complete=function(){this.notifyComplete(!0)},e}(f.L);function eu(t){return function(e){return 0===t?(0,tA.c)():e.lift(new ec(t))}}var ec=function(){function t(t){if(this.total=t,this.total<0)throw new t6.W}return t.prototype.call=function(t,e){return e.subscribe(new el(t,this.total))},t}(),el=function(t){function e(e,n){var r=t.call(this,e)||this;return r.total=n,r.ring=[],r.count=0,r}return r.ZT(e,t),e.prototype._next=function(t){var e=this.ring,n=this.total,r=this.count++;e.length<n?e.push(t):e[r%n]=t},e.prototype._complete=function(){var t=this.destination,e=this.count;if(e>0)for(var n=this.count>=this.total?this.total:this.count,r=this.ring,i=0;i<n;i++){var $=e++%n;t.next(r[$])}t.complete()},e}(f.L);function eh(t,e){var n=arguments.length>=2;return function(r){return r.pipe(t?(0,t3.h)(function(e,n){return t(e,n,r)}):ee.y,eu(1),n?to(e):tC(function(){return new t4.K}))}}function ef(t){return function(e){return e.lift(new ed(t))}}var ed=function(){function t(t){this.value=t}return t.prototype.call=function(t,e){return e.subscribe(new ep(t,this.value))},t}(),ep=function(t){function e(e,n){var r=t.call(this,e)||this;return r.value=n,r}return r.ZT(e,t),e.prototype._next=function(t){this.destination.next(this.value)},e}(f.L);function e0(){return function(t){return t.lift(new ey)}}var ey=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new eg(t))},t}(),eg=function(t){function e(e){return t.call(this,e)||this}return r.ZT(e,t),e.prototype._next=function(t){this.destination.next(tu.P.createNext(t))},e.prototype._error=function(t){var e=this.destination;e.next(tu.P.createError(t)),e.complete()},e.prototype._complete=function(){var t=this.destination;t.next(tu.P.createComplete()),t.complete()},e}(f.L);function eb(t,e){var n=!1;return arguments.length>=2&&(n=!0),function(r){return r.lift(new e_(t,e,n))}}var e_=function(){function t(t,e,n){void 0===n&&(n=!1),this.accumulator=t,this.seed=e,this.hasSeed=n}return t.prototype.call=function(t,e){return e.subscribe(new em(t,this.accumulator,this.seed,this.hasSeed))},t}(),em=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $.accumulator=n,$._seed=r,$.hasSeed=i,$.index=0,$}return r.ZT(e,t),Object.defineProperty(e.prototype,"seed",{get:function(){return this._seed},set:function(t){this.hasSeed=!0,this._seed=t},enumerable:!0,configurable:!0}),e.prototype._next=function(t){if(this.hasSeed)return this._tryNext(t);this.seed=t,this.destination.next(t)},e.prototype._tryNext=function(t){var e,n=this.index++;try{e=this.accumulator(this.seed,t,n)}catch(r){this.destination.error(r)}this.seed=e,this.destination.next(e)},e}(f.L),ev=n(2561);function ew(t,e){return arguments.length>=2?function(n){return(0,ev.z)(eb(t,e),eu(1),to(e))(n)}:function(e){return(0,ev.z)(eb(function(e,n,r){return t(e,n,r+1)}),eu(1))(e)}}function e8(t){return ew("function"==typeof t?function(e,n){return t(e,n)>0?e:n}:function(t,e){return t>e?t:e})}var e1=n(4370);function eS(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){return e.lift.call(e1.T.apply(void 0,[e].concat(t)))}}var e2=n(2556);function ek(t,e,n){return void 0===n&&(n=Number.POSITIVE_INFINITY),"function"==typeof e?(0,Z.zg)(function(){return t},e,n):("number"==typeof e&&(n=e),(0,Z.zg)(function(){return t},n))}function eE(t,e,n){return void 0===n&&(n=Number.POSITIVE_INFINITY),function(r){return r.lift(new e6(t,e,n))}}var e6=function(){function t(t,e,n){this.accumulator=t,this.seed=e,this.concurrent=n}return t.prototype.call=function(t,e){return e.subscribe(new e3(t,this.accumulator,this.seed,this.concurrent))},t}(),e3=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $.accumulator=n,$.acc=r,$.concurrent=i,$.hasValue=!1,$.hasCompleted=!1,$.buffer=[],$.active=0,$.index=0,$}return r.ZT(e,t),e.prototype._next=function(t){if(this.active<this.concurrent){var e=this.index++,n=this.destination,r=void 0;try{r=(0,this.accumulator)(this.acc,t,e)}catch(i){return n.error(i)}this.active++,this._innerSub(r)}else this.buffer.push(t)},e.prototype._innerSub=function(t){var e=new i.IY(this),n=this.destination;n.add(e);var r=(0,i.ft)(t,e);r!==e&&n.add(r)},e.prototype._complete=function(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&(!1===this.hasValue&&this.destination.next(this.acc),this.destination.complete()),this.unsubscribe()},e.prototype.notifyNext=function(t){var e=this.destination;this.acc=t,this.hasValue=!0,e.next(t)},e.prototype.notifyComplete=function(){var t=this.buffer;this.active--,t.length>0?this._next(t.shift()):0===this.active&&this.hasCompleted&&(!1===this.hasValue&&this.destination.next(this.acc),this.destination.complete())},e}(i.Ds);function e4(t){return ew("function"==typeof t?function(e,n){return 0>t(e,n)?e:n}:function(t,e){return t<e?t:e})}var eC=n(3140);function e5(t,e){return function(n){if(r="function"==typeof t?t:function(){return t},"function"==typeof e)return n.lift(new eM(r,e));var r,i=Object.create(n,eC.N);return i.source=n,i.subjectFactory=r,i}}var eM=function(){function t(t,e){this.subjectFactory=t,this.selector=e}return t.prototype.call=function(t,e){var n=this.selector,r=this.subjectFactory(),i=n(r).subscribe(t);return i.add(e.subscribe(r)),i},t}(),eI=n(9276);function eA(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return 1===t.length&&(0,F.k)(t[0])&&(t=t[0]),function(e){return e.lift(new eT(t))}}var eT=function(){function t(t){this.nextSources=t}return t.prototype.call=function(t,e){return e.subscribe(new eR(t,this.nextSources))},t}(),eR=function(t){function e(e,n){var r=t.call(this,e)||this;return r.destination=e,r.nextSources=n,r}return r.ZT(e,t),e.prototype.notifyError=function(){this.subscribeToNextSource()},e.prototype.notifyComplete=function(){this.subscribeToNextSource()},e.prototype._error=function(t){this.subscribeToNextSource(),this.unsubscribe()},e.prototype._complete=function(){this.subscribeToNextSource(),this.unsubscribe()},e.prototype.subscribeToNextSource=function(){var t=this.nextSources.shift();if(t){var e=new i.IY(this),n=this.destination;n.add(e);var r=(0,i.ft)(t,e);r!==e&&n.add(r)}else this.destination.complete()},e}(i.Ds);function eL(){return function(t){return t.lift(new eN)}}var eN=function(){function t(){}return t.prototype.call=function(t,e){return e.subscribe(new e7(t))},t}(),e7=function(t){function e(e){var n=t.call(this,e)||this;return n.hasPrev=!1,n}return r.ZT(e,t),e.prototype._next=function(t){var e;this.hasPrev?e=[this.prev,t]:this.hasPrev=!0,this.prev=t,e&&this.destination.next(e)},e}(f.L),eP=n(8463);function eD(t,e){return function(n){return[(0,t3.h)(t,e)(n),(0,t3.h)((0,eP.f)(t,e))(n)]}}function eO(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t.length;if(0===n)throw Error("list of properties cannot be empty.");return function(e){return(0,tW.U)(eB(t,n))(e)}}function eB(t,e){return function(n){for(var r=n,i=0;i<e;i++){var $=null!=r?r[t[i]]:void 0;if(void 0===$)return;r=$}return r}}var ej=n(211);function eU(t){return t?e5(function(){return new ej.xQ},t):e5(new ej.xQ)}var eF=n(9233);function eW(t){return function(e){return e5(new eF.X(t))(e)}}var ez=n(364);function eH(){return function(t){return e5(new ez.c)(t)}}var eV=n(2630);function eq(t,e,n,r){n&&"function"!=typeof n&&(r=n);var i="function"==typeof n?n:void 0,$=new eV.t(t,e,r);return function(t){return e5(function(){return $},i)(t)}}var eZ=n(8821);function eG(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){return 1===t.length&&(0,F.k)(t[0])&&(t=t[0]),e.lift.call(eZ.S3.apply(void 0,[e].concat(t)))}}function eY(t){return void 0===t&&(t=-1),function(e){return 0===t?(0,tA.c)():t<0?e.lift(new eK(-1,e)):e.lift(new eK(t-1,e))}}var eK=function(){function t(t,e){this.count=t,this.source=e}return t.prototype.call=function(t,e){return e.subscribe(new eQ(t,this.count,this.source))},t}(),eQ=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.count=n,i.source=r,i}return r.ZT(e,t),e.prototype.complete=function(){if(!this.isStopped){var e=this.source,n=this.count;if(0===n)return t.prototype.complete.call(this);n>-1&&(this.count=n-1),e.subscribe(this._unsubscribeAndRecycle())}},e}(f.L);function eJ(t){return function(e){return e.lift(new e9(t))}}var e9=function(){function t(t){this.notifier=t}return t.prototype.call=function(t,e){return e.subscribe(new eX(t,this.notifier,e))},t}(),eX=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.notifier=n,i.source=r,i.sourceIsBeingSubscribedTo=!0,i}return r.ZT(e,t),e.prototype.notifyNext=function(){this.sourceIsBeingSubscribedTo=!0,this.source.subscribe(this)},e.prototype.notifyComplete=function(){if(!1===this.sourceIsBeingSubscribedTo)return t.prototype.complete.call(this)},e.prototype.complete=function(){if(this.sourceIsBeingSubscribedTo=!1,!this.isStopped){if(this.retries||this.subscribeToRetries(),!this.retriesSubscription||this.retriesSubscription.closed)return t.prototype.complete.call(this);this._unsubscribeAndRecycle(),this.notifications.next(void 0)}},e.prototype._unsubscribe=function(){var t=this.notifications,e=this.retriesSubscription;t&&(t.unsubscribe(),this.notifications=void 0),e&&(e.unsubscribe(),this.retriesSubscription=void 0),this.retries=void 0},e.prototype._unsubscribeAndRecycle=function(){var e=this._unsubscribe;return this._unsubscribe=null,t.prototype._unsubscribeAndRecycle.call(this),this._unsubscribe=e,this},e.prototype.subscribeToRetries=function(){var e;this.notifications=new ej.xQ;try{e=(0,this.notifier)(this.notifications)}catch(n){return t.prototype.complete.call(this)}this.retries=e,this.retriesSubscription=(0,i.ft)(e,new i.IY(this))},e}(i.Ds);function nt(t){return void 0===t&&(t=-1),function(e){return e.lift(new ne(t,e))}}var ne=function(){function t(t,e){this.count=t,this.source=e}return t.prototype.call=function(t,e){return e.subscribe(new nn(t,this.count,this.source))},t}(),nn=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.count=n,i.source=r,i}return r.ZT(e,t),e.prototype.error=function(e){if(!this.isStopped){var n=this.source,r=this.count;if(0===r)return t.prototype.error.call(this,e);r>-1&&(this.count=r-1),n.subscribe(this._unsubscribeAndRecycle())}},e}(f.L);function nr(t){return function(e){return e.lift(new ni(t,e))}}var ni=function(){function t(t,e){this.notifier=t,this.source=e}return t.prototype.call=function(t,e){return e.subscribe(new n$(t,this.notifier,this.source))},t}(),n$=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.notifier=n,i.source=r,i}return r.ZT(e,t),e.prototype.error=function(e){if(!this.isStopped){var n=this.errors,r=this.retries,$=this.retriesSubscription;if(r)this.errors=void 0,this.retriesSubscription=void 0;else{n=new ej.xQ;try{r=(0,this.notifier)(n)}catch(o){return t.prototype.error.call(this,o)}$=(0,i.ft)(r,new i.IY(this))}this._unsubscribeAndRecycle(),this.errors=n,this.retries=r,this.retriesSubscription=$,n.next(e)}},e.prototype._unsubscribe=function(){var t=this.errors,e=this.retriesSubscription;t&&(t.unsubscribe(),this.errors=void 0),e&&(e.unsubscribe(),this.retriesSubscription=void 0),this.retries=void 0},e.prototype.notifyNext=function(){var t=this._unsubscribe;this._unsubscribe=null,this._unsubscribeAndRecycle(),this._unsubscribe=t,this.source.subscribe(this)},e}(i.Ds),no=n(3018);function ns(t){return function(e){return e.lift(new nx(t))}}var nx=function(){function t(t){this.notifier=t}return t.prototype.call=function(t,e){var n=new na(t),r=e.subscribe(n);return r.add((0,i.ft)(this.notifier,new i.IY(n))),r},t}(),na=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.hasValue=!1,e}return r.ZT(e,t),e.prototype._next=function(t){this.value=t,this.hasValue=!0},e.prototype.notifyNext=function(){this.emitValue()},e.prototype.notifyComplete=function(){this.emitValue()},e.prototype.emitValue=function(){this.hasValue&&(this.hasValue=!1,this.destination.next(this.value))},e}(i.Ds);function nu(t,e){return void 0===e&&(e=x.P),function(n){return n.lift(new nc(t,e))}}var nc=function(){function t(t,e){this.period=t,this.scheduler=e}return t.prototype.call=function(t,e){return e.subscribe(new nl(t,this.period,this.scheduler))},t}(),nl=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.period=n,i.scheduler=r,i.hasValue=!1,i.add(r.schedule(nh,n,{subscriber:i,period:n})),i}return r.ZT(e,t),e.prototype._next=function(t){this.lastValue=t,this.hasValue=!0},e.prototype.notifyNext=function(){this.hasValue&&(this.hasValue=!1,this.destination.next(this.lastValue))},e}(f.L);function nh(t){var e=t.subscriber,n=t.period;e.notifyNext(),this.schedule(t,n)}function nf(t,e){return function(n){return n.lift(new nd(t,e))}}var nd=function(){function t(t,e){this.compareTo=t,this.comparator=e}return t.prototype.call=function(t,e){return e.subscribe(new np(t,this.compareTo,this.comparator))},t}(),np=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.compareTo=n,i.comparator=r,i._a=[],i._b=[],i._oneComplete=!1,i.destination.add(n.subscribe(new n0(e,i))),i}return r.ZT(e,t),e.prototype._next=function(t){this._oneComplete&&0===this._b.length?this.emit(!1):(this._a.push(t),this.checkValues())},e.prototype._complete=function(){this._oneComplete?this.emit(0===this._a.length&&0===this._b.length):this._oneComplete=!0,this.unsubscribe()},e.prototype.checkValues=function(){for(var t=this,e=t._a,n=t._b,r=t.comparator;e.length>0&&n.length>0;){var i=e.shift(),$=n.shift(),o=!1;try{o=r?r(i,$):i===$}catch(s){this.destination.error(s)}o||this.emit(!1)}},e.prototype.emit=function(t){var e=this.destination;e.next(t),e.complete()},e.prototype.nextB=function(t){this._oneComplete&&0===this._a.length?this.emit(!1):(this._b.push(t),this.checkValues())},e.prototype.completeB=function(){this._oneComplete?this.emit(0===this._a.length&&0===this._b.length):this._oneComplete=!0},e}(f.L),n0=function(t){function e(e,n){var r=t.call(this,e)||this;return r.parent=n,r}return r.ZT(e,t),e.prototype._next=function(t){this.parent.nextB(t)},e.prototype._error=function(t){this.parent.error(t),this.unsubscribe()},e.prototype._complete=function(){this.parent.completeB(),this.unsubscribe()},e}(f.L);function ny(){return new ej.xQ}function ng(){return function(t){return(0,no.x)()(e5(ny)(t))}}function nb(t,e,n){var r;return r=t&&"object"==typeof t?t:{bufferSize:t,windowTime:e,refCount:!1,scheduler:n},function(t){var e,n,i,$,o,s,x,a,u,c,l,h;return t.lift((o=void 0===($=(e=r).bufferSize)?Number.POSITIVE_INFINITY:$,x=void 0===(s=e.windowTime)?Number.POSITIVE_INFINITY:s,a=e.refCount,u=e.scheduler,c=0,l=!1,h=!1,function(t){var e;c++,!n||l?(l=!1,e=(n=new eV.t(o,x,u)).subscribe(this),i=t.subscribe({next:function(t){n.next(t)},error:function(t){l=!0,n.error(t)},complete:function(){h=!0,i=void 0,n.complete()}}),h&&(i=void 0)):e=n.subscribe(this),this.add(function(){c--,e.unsubscribe(),e=void 0,i&&!h&&a&&0===c&&(i.unsubscribe(),i=void 0,n=void 0)})}))}}function n_(t){return function(e){return e.lift(new nm(t,e))}}var nm=function(){function t(t,e){this.predicate=t,this.source=e}return t.prototype.call=function(t,e){return e.subscribe(new nv(t,this.predicate,this.source))},t}(),nv=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.predicate=n,i.source=r,i.seenValue=!1,i.index=0,i}return r.ZT(e,t),e.prototype.applySingleValue=function(t){this.seenValue?this.destination.error("Sequence contains more than one element"):(this.seenValue=!0,this.singleValue=t)},e.prototype._next=function(t){var e=this.index++;this.predicate?this.tryNext(t,e):this.applySingleValue(t)},e.prototype.tryNext=function(t,e){try{this.predicate(t,e,this.source)&&this.applySingleValue(t)}catch(n){this.destination.error(n)}},e.prototype._complete=function(){var t=this.destination;this.index>0?(t.next(this.seenValue?this.singleValue:void 0),t.complete()):t.error(new t4.K)},e}(f.L);function nw(t){return function(e){return e.lift(new n8(t))}}var n8=function(){function t(t){this.total=t}return t.prototype.call=function(t,e){return e.subscribe(new n1(t,this.total))},t}(),n1=function(t){function e(e,n){var r=t.call(this,e)||this;return r.total=n,r.count=0,r}return r.ZT(e,t),e.prototype._next=function(t){++this.count>this.total&&this.destination.next(t)},e}(f.L);function nS(t){return function(e){return e.lift(new n2(t))}}var n2=function(){function t(t){if(this._skipCount=t,this._skipCount<0)throw new t6.W}return t.prototype.call=function(t,e){return 0===this._skipCount?e.subscribe(new f.L(t)):e.subscribe(new nk(t,this._skipCount))},t}(),nk=function(t){function e(e,n){var r=t.call(this,e)||this;return r._skipCount=n,r._count=0,r._ring=Array(n),r}return r.ZT(e,t),e.prototype._next=function(t){var e=this._skipCount,n=this._count++;if(n<e)this._ring[n]=t;else{var r=n%e,i=this._ring,$=i[r];i[r]=t,this.destination.next($)}},e}(f.L);function nE(t){return function(e){return e.lift(new n6(t))}}var n6=function(){function t(t){this.notifier=t}return t.prototype.call=function(t,e){return e.subscribe(new n3(t,this.notifier))},t}(),n3=function(t){function e(e,n){var r=t.call(this,e)||this;r.hasValue=!1;var $=new i.IY(r);r.add($),r.innerSubscription=$;var o=(0,i.ft)(n,$);return o!==$&&(r.add(o),r.innerSubscription=o),r}return r.ZT(e,t),e.prototype._next=function(e){this.hasValue&&t.prototype._next.call(this,e)},e.prototype.notifyNext=function(){this.hasValue=!0,this.innerSubscription&&this.innerSubscription.unsubscribe()},e.prototype.notifyComplete=function(){},e}(i.Ds);function n4(t){return function(e){return e.lift(new nC(t))}}var nC=function(){function t(t){this.predicate=t}return t.prototype.call=function(t,e){return e.subscribe(new n5(t,this.predicate))},t}(),n5=function(t){function e(e,n){var r=t.call(this,e)||this;return r.predicate=n,r.skipping=!0,r.index=0,r}return r.ZT(e,t),e.prototype._next=function(t){var e=this.destination;this.skipping&&this.tryCallPredicate(t),this.skipping||e.next(t)},e.prototype.tryCallPredicate=function(t){try{var e=this.predicate(t,this.index++);this.skipping=Boolean(e)}catch(n){this.destination.error(n)}},e}(f.L);function nM(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[t.length-1];return(0,b.K)(n)?(t.pop(),function(e){return(0,H.z)(t,e,n)}):function(e){return(0,H.z)(t,e)}}var nI=n(6650),nA=n(5812),nT=function(t){function e(e,n,r){void 0===n&&(n=0),void 0===r&&(r=nI.e);var i=t.call(this)||this;return i.source=e,i.delayTime=n,i.scheduler=r,(!(0,nA.k)(n)||n<0)&&(i.delayTime=0),r&&"function"==typeof r.schedule||(i.scheduler=nI.e),i}return r.ZT(e,t),e.create=function(t,n,r){return void 0===n&&(n=0),void 0===r&&(r=nI.e),new e(t,n,r)},e.dispatch=function(t){var e=t.source,n=t.subscriber;return this.add(e.subscribe(n))},e.prototype._subscribe=function(t){var n=this.delayTime,r=this.source;return this.scheduler.schedule(e.dispatch,n,{source:r,subscriber:t})},e}(td.y);function nR(t,e){return void 0===e&&(e=0),function(n){return n.lift(new nL(t,e))}}var nL=function(){function t(t,e){this.scheduler=t,this.delay=e}return t.prototype.call=function(t,e){return new nT(e,this.delay,this.scheduler).subscribe(t)},t}();function nN(t,e){return"function"==typeof e?function(n){return n.pipe(nN(function(n,r){return(0,W.D)(t(n,r)).pipe((0,tW.U)(function(t,i){return e(n,t,r,i)}))}))}:function(e){return e.lift(new n7(t))}}var n7=function(){function t(t){this.project=t}return t.prototype.call=function(t,e){return e.subscribe(new nP(t,this.project))},t}(),nP=function(t){function e(e,n){var r=t.call(this,e)||this;return r.project=n,r.index=0,r}return r.ZT(e,t),e.prototype._next=function(t){var e,n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this._innerSub(e)},e.prototype._innerSub=function(t){var e=this.innerSubscription;e&&e.unsubscribe();var n=new i.IY(this),r=this.destination;r.add(n),this.innerSubscription=(0,i.ft)(t,n),this.innerSubscription!==n&&r.add(this.innerSubscription)},e.prototype._complete=function(){var e=this.innerSubscription;e&&!e.closed||t.prototype._complete.call(this),this.unsubscribe()},e.prototype._unsubscribe=function(){this.innerSubscription=void 0},e.prototype.notifyComplete=function(){this.innerSubscription=void 0,this.isStopped&&t.prototype._complete.call(this)},e.prototype.notifyNext=function(t){this.destination.next(t)},e}(i.Ds);function nD(){return nN(ee.y)}function nO(t,e){return e?nN(function(){return t},e):nN(function(){return t})}function nB(t){return function(e){return e.lift(new nj(t))}}var nj=function(){function t(t){this.notifier=t}return t.prototype.call=function(t,e){var n=new nU(t),r=(0,i.ft)(this.notifier,new i.IY(n));return r&&!n.seenValue?(n.add(r),e.subscribe(n)):n},t}(),nU=function(t){function e(e){var n=t.call(this,e)||this;return n.seenValue=!1,n}return r.ZT(e,t),e.prototype.notifyNext=function(){this.seenValue=!0,this.complete()},e.prototype.notifyComplete=function(){},e}(i.Ds);function nF(t,e){return void 0===e&&(e=!1),function(n){return n.lift(new nW(t,e))}}var nW=function(){function t(t,e){this.predicate=t,this.inclusive=e}return t.prototype.call=function(t,e){return e.subscribe(new nz(t,this.predicate,this.inclusive))},t}(),nz=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.predicate=n,i.inclusive=r,i.index=0,i}return r.ZT(e,t),e.prototype._next=function(t){var e,n=this.destination;try{e=this.predicate(t,this.index++)}catch(r){return void n.error(r)}this.nextOrComplete(t,e)},e.prototype.nextOrComplete=function(t,e){var n=this.destination;Boolean(e)?n.next(t):(this.inclusive&&n.next(t),n.complete())},e}(f.L),nH=n(3306),nV=n(4156);function nq(t,e,n){return function(r){return r.lift(new nZ(t,e,n))}}var nZ=function(){function t(t,e,n){this.nextOrObserver=t,this.error=e,this.complete=n}return t.prototype.call=function(t,e){return e.subscribe(new nG(t,this.nextOrObserver,this.error,this.complete))},t}(),nG=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $._tapNext=nH.Z,$._tapError=nH.Z,$._tapComplete=nH.Z,$._tapError=r||nH.Z,$._tapComplete=i||nH.Z,(0,nV.m)(n)?($._context=$,$._tapNext=n):n&&($._context=n,$._tapNext=n.next||nH.Z,$._tapError=n.error||nH.Z,$._tapComplete=n.complete||nH.Z),$}return r.ZT(e,t),e.prototype._next=function(t){try{this._tapNext.call(this._context,t)}catch(e){return void this.destination.error(e)}this.destination.next(t)},e.prototype._error=function(t){try{this._tapError.call(this._context,t)}catch(e){return void this.destination.error(e)}this.destination.error(t)},e.prototype._complete=function(){try{this._tapComplete.call(this._context)}catch(t){return void this.destination.error(t)}return this.destination.complete()},e}(f.L),nY={leading:!0,trailing:!1};function nK(t,e){return void 0===e&&(e=nY),function(n){return n.lift(new nQ(t,!!e.leading,!!e.trailing))}}var nQ=function(){function t(t,e,n){this.durationSelector=t,this.leading=e,this.trailing=n}return t.prototype.call=function(t,e){return e.subscribe(new nJ(t,this.durationSelector,this.leading,this.trailing))},t}(),nJ=function(t){function e(e,n,r,i){var $=t.call(this,e)||this;return $.destination=e,$.durationSelector=n,$._leading=r,$._trailing=i,$._hasValue=!1,$}return r.ZT(e,t),e.prototype._next=function(t){this._hasValue=!0,this._sendValue=t,this._throttled||(this._leading?this.send():this.throttle(t))},e.prototype.send=function(){var t=this._hasValue,e=this._sendValue;t&&(this.destination.next(e),this.throttle(e)),this._hasValue=!1,this._sendValue=void 0},e.prototype.throttle=function(t){var e=this.tryDurationSelector(t);e&&this.add(this._throttled=(0,i.ft)(e,new i.IY(this)))},e.prototype.tryDurationSelector=function(t){try{return this.durationSelector(t)}catch(e){return this.destination.error(e),null}},e.prototype.throttlingDone=function(){var t=this._throttled,e=this._trailing;t&&t.unsubscribe(),this._throttled=void 0,e&&this.send()},e.prototype.notifyNext=function(){this.throttlingDone()},e.prototype.notifyComplete=function(){this.throttlingDone()},e}(i.Ds);function n9(t,e,n){return void 0===e&&(e=x.P),void 0===n&&(n=nY),function(r){return r.lift(new nX(t,e,n.leading,n.trailing))}}var nX=function(){function t(t,e,n,r){this.duration=t,this.scheduler=e,this.leading=n,this.trailing=r}return t.prototype.call=function(t,e){return e.subscribe(new rt(t,this.duration,this.scheduler,this.leading,this.trailing))},t}(),rt=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;return o.duration=n,o.scheduler=r,o.leading=i,o.trailing=$,o._hasTrailingValue=!1,o._trailingValue=null,o}return r.ZT(e,t),e.prototype._next=function(t){this.throttled?this.trailing&&(this._trailingValue=t,this._hasTrailingValue=!0):(this.add(this.throttled=this.scheduler.schedule(re,this.duration,{subscriber:this})),this.leading?this.destination.next(t):this.trailing&&(this._trailingValue=t,this._hasTrailingValue=!0))},e.prototype._complete=function(){this._hasTrailingValue&&this.destination.next(this._trailingValue),this.destination.complete()},e.prototype.clearThrottle=function(){var t=this.throttled;t&&(this.trailing&&this._hasTrailingValue&&(this.destination.next(this._trailingValue),this._trailingValue=null,this._hasTrailingValue=!1),t.unsubscribe(),this.remove(t),this.throttled=null)},e}(f.L);function re(t){t.subscriber.clearThrottle()}var rn=n(1410);function rr(t){return void 0===t&&(t=x.P),function(e){return(0,rn.P)(function(){return e.pipe(eb(function(e,n){var r=e.current;return{value:n,current:t.now(),last:r}},{current:t.now(),value:void 0,last:void 0}),(0,tW.U)(function(t){var e=t.current,n=t.last,r=t.value;return new ri(r,e-n)}))})}}var ri=function(t,e){this.value=t,this.interval=e},r$=n(1462);function ro(t,e,n){return void 0===n&&(n=x.P),function(r){var i=ta(t),$=i?+t-n.now():Math.abs(t);return r.lift(new rs($,i,e,n))}}var rs=function(){function t(t,e,n,r){this.waitFor=t,this.absoluteTimeout=e,this.withObservable=n,this.scheduler=r}return t.prototype.call=function(t,e){return e.subscribe(new rx(t,this.absoluteTimeout,this.waitFor,this.withObservable,this.scheduler))},t}(),rx=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;return o.absoluteTimeout=n,o.waitFor=r,o.withObservable=i,o.scheduler=$,o.scheduleTimeout(),o}return r.ZT(e,t),e.dispatchTimeout=function(t){var e=t.withObservable;t._unsubscribeAndRecycle(),t.add((0,i.ft)(e,new i.IY(t)))},e.prototype.scheduleTimeout=function(){var t=this.action;t?this.action=t.schedule(this,this.waitFor):this.add(this.action=this.scheduler.schedule(e.dispatchTimeout,this.waitFor,this))},e.prototype._next=function(e){this.absoluteTimeout||this.scheduleTimeout(),t.prototype._next.call(this,e)},e.prototype._unsubscribe=function(){this.action=void 0,this.scheduler=null,this.withObservable=null},e}(i.Ds),ra=n(4944);function ru(t,e){return void 0===e&&(e=x.P),ro(t,(0,ra._)(new r$.W),e)}function rc(t){return void 0===t&&(t=x.P),(0,tW.U)(function(e){return new rl(e,t.now())})}var rl=function(t,e){this.value=t,this.timestamp=e};function rh(t,e,n){return 0===n?[e]:(t.push(e),t)}function rf(){return ew(rh,[])}function rd(t){return function(e){return e.lift(new rp(t))}}var rp=function(){function t(t){this.windowBoundaries=t}return t.prototype.call=function(t,e){var n=new r0(t),r=e.subscribe(n);return r.closed||n.add((0,i.ft)(this.windowBoundaries,new i.IY(n))),r},t}(),r0=function(t){function e(e){var n=t.call(this,e)||this;return n.window=new ej.xQ,e.next(n.window),n}return r.ZT(e,t),e.prototype.notifyNext=function(){this.openWindow()},e.prototype.notifyError=function(t){this._error(t)},e.prototype.notifyComplete=function(){this._complete()},e.prototype._next=function(t){this.window.next(t)},e.prototype._error=function(t){this.window.error(t),this.destination.error(t)},e.prototype._complete=function(){this.window.complete(),this.destination.complete()},e.prototype._unsubscribe=function(){this.window=null},e.prototype.openWindow=function(){var t=this.window;t&&t.complete();var e=this.destination,n=this.window=new ej.xQ;e.next(n)},e}(i.Ds);function ry(t,e){return void 0===e&&(e=0),function(n){return n.lift(new rg(t,e))}}var rg=function(){function t(t,e){this.windowSize=t,this.startWindowEvery=e}return t.prototype.call=function(t,e){return e.subscribe(new rb(t,this.windowSize,this.startWindowEvery))},t}(),rb=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.destination=e,i.windowSize=n,i.startWindowEvery=r,i.windows=[new ej.xQ],i.count=0,e.next(i.windows[0]),i}return r.ZT(e,t),e.prototype._next=function(t){for(var e=this.startWindowEvery>0?this.startWindowEvery:this.windowSize,n=this.destination,r=this.windowSize,i=this.windows,$=i.length,o=0;o<$&&!this.closed;o++)i[o].next(t);var s=this.count-r+1;if(s>=0&&s%e==0&&!this.closed&&i.shift().complete(),++this.count%e==0&&!this.closed){var x=new ej.xQ;i.push(x),n.next(x)}},e.prototype._error=function(t){var e=this.windows;if(e)for(;e.length>0&&!this.closed;)e.shift().error(t);this.destination.error(t)},e.prototype._complete=function(){var t=this.windows;if(t)for(;t.length>0&&!this.closed;)t.shift().complete();this.destination.complete()},e.prototype._unsubscribe=function(){this.count=0,this.windows=null},e}(f.L);function r_(t){var e=x.P,n=null,r=Number.POSITIVE_INFINITY;return(0,b.K)(arguments[3])&&(e=arguments[3]),(0,b.K)(arguments[2])?e=arguments[2]:(0,nA.k)(arguments[2])&&(r=Number(arguments[2])),(0,b.K)(arguments[1])?e=arguments[1]:(0,nA.k)(arguments[1])&&(n=Number(arguments[1])),function(i){return i.lift(new rm(t,n,r,e))}}var rm=function(){function t(t,e,n,r){this.windowTimeSpan=t,this.windowCreationInterval=e,this.maxWindowSize=n,this.scheduler=r}return t.prototype.call=function(t,e){return e.subscribe(new rw(t,this.windowTimeSpan,this.windowCreationInterval,this.maxWindowSize,this.scheduler))},t}(),rv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._numberOfNextedValues=0,e}return r.ZT(e,t),e.prototype.next=function(e){this._numberOfNextedValues++,t.prototype.next.call(this,e)},Object.defineProperty(e.prototype,"numberOfNextedValues",{get:function(){return this._numberOfNextedValues},enumerable:!0,configurable:!0}),e}(ej.xQ),rw=function(t){function e(e,n,r,i,$){var o=t.call(this,e)||this;o.destination=e,o.windowTimeSpan=n,o.windowCreationInterval=r,o.maxWindowSize=i,o.scheduler=$,o.windows=[];var s=o.openWindow();if(null!==r&&r>=0){var x={subscriber:o,window:s,context:null},a={windowTimeSpan:n,windowCreationInterval:r,subscriber:o,scheduler:$};o.add($.schedule(rS,n,x)),o.add($.schedule(r1,r,a))}else{var u={subscriber:o,window:s,windowTimeSpan:n};o.add($.schedule(r8,n,u))}return o}return r.ZT(e,t),e.prototype._next=function(t){for(var e=this.windows,n=e.length,r=0;r<n;r++){var i=e[r];i.closed||(i.next(t),i.numberOfNextedValues>=this.maxWindowSize&&this.closeWindow(i))}},e.prototype._error=function(t){for(var e=this.windows;e.length>0;)e.shift().error(t);this.destination.error(t)},e.prototype._complete=function(){for(var t=this.windows;t.length>0;){var e=t.shift();e.closed||e.complete()}this.destination.complete()},e.prototype.openWindow=function(){var t=new rv;return this.windows.push(t),this.destination.next(t),t},e.prototype.closeWindow=function(t){t.complete();var e=this.windows;e.splice(e.indexOf(t),1)},e}(f.L);function r8(t){var e=t.subscriber,n=t.windowTimeSpan,r=t.window;r&&e.closeWindow(r),t.window=e.openWindow(),this.schedule(t,n)}function r1(t){var e=t.windowTimeSpan,n=t.subscriber,r=t.scheduler,i=t.windowCreationInterval,$=n.openWindow(),o=this,s={action:o,subscription:null},x={subscriber:n,window:$,context:s};s.subscription=r.schedule(rS,e,x),o.add(s.subscription),o.schedule(t,i)}function rS(t){var e=t.subscriber,n=t.window,r=t.context;r&&r.action&&r.subscription&&r.action.remove(r.subscription),e.closeWindow(n)}function r2(t,e){return function(n){return n.lift(new rk(t,e))}}var rk=function(){function t(t,e){this.openings=t,this.closingSelector=e}return t.prototype.call=function(t,e){return e.subscribe(new rE(t,this.openings,this.closingSelector))},t}(),rE=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.openings=n,i.closingSelector=r,i.contexts=[],i.add(i.openSubscription=(0,M.D)(i,n,n)),i}return r.ZT(e,t),e.prototype._next=function(t){var e=this.contexts;if(e)for(var n=e.length,r=0;r<n;r++)e[r].window.next(t)},e.prototype._error=function(e){var n=this.contexts;if(this.contexts=null,n)for(var r=n.length,i=-1;++i<r;){var $=n[i];$.window.error(e),$.subscription.unsubscribe()}t.prototype._error.call(this,e)},e.prototype._complete=function(){var e=this.contexts;if(this.contexts=null,e)for(var n=e.length,r=-1;++r<n;){var i=e[r];i.window.complete(),i.subscription.unsubscribe()}t.prototype._complete.call(this)},e.prototype._unsubscribe=function(){var t=this.contexts;if(this.contexts=null,t)for(var e=t.length,n=-1;++n<e;){var r=t[n];r.window.unsubscribe(),r.subscription.unsubscribe()}},e.prototype.notifyNext=function(t,e,n,r,i){if(t===this.openings){var $=void 0;try{$=(0,this.closingSelector)(e)}catch(o){return this.error(o)}var s=new ej.xQ,x=new C.w,a={window:s,subscription:x};this.contexts.push(a);var u=(0,M.D)(this,$,a);u.closed?this.closeWindow(this.contexts.length-1):(u.context=a,x.add(u)),this.destination.next(s)}else this.closeWindow(this.contexts.indexOf(t))},e.prototype.notifyError=function(t){this.error(t)},e.prototype.notifyComplete=function(t){t!==this.openSubscription&&this.closeWindow(this.contexts.indexOf(t.context))},e.prototype.closeWindow=function(t){if(-1!==t){var e=this.contexts,n=e[t],r=n.window,i=n.subscription;e.splice(t,1),r.complete(),i.unsubscribe()}},e}(I.L);function r6(t){return function(e){return e.lift(new r3(t))}}var r3=function(){function t(t){this.closingSelector=t}return t.prototype.call=function(t,e){return e.subscribe(new r4(t,this.closingSelector))},t}(),r4=function(t){function e(e,n){var r=t.call(this,e)||this;return r.destination=e,r.closingSelector=n,r.openWindow(),r}return r.ZT(e,t),e.prototype.notifyNext=function(t,e,n,r,i){this.openWindow(i)},e.prototype.notifyError=function(t){this._error(t)},e.prototype.notifyComplete=function(t){this.openWindow(t)},e.prototype._next=function(t){this.window.next(t)},e.prototype._error=function(t){this.window.error(t),this.destination.error(t),this.unsubscribeClosingNotification()},e.prototype._complete=function(){this.window.complete(),this.destination.complete(),this.unsubscribeClosingNotification()},e.prototype.unsubscribeClosingNotification=function(){this.closingNotification&&this.closingNotification.unsubscribe()},e.prototype.openWindow=function(t){void 0===t&&(t=null),t&&(this.remove(t),t.unsubscribe());var e=this.window;e&&e.complete();var n,r=this.window=new ej.xQ;this.destination.next(r);try{n=(0,this.closingSelector)()}catch(i){return this.destination.error(i),void this.window.error(i)}this.add(this.closingNotification=(0,M.D)(this,n))},e}(I.L);function rC(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){"function"==typeof t[t.length-1]&&(n=t.pop());var n,r=t;return e.lift(new r5(r,n))}}var r5=function(){function t(t,e){this.observables=t,this.project=e}return t.prototype.call=function(t,e){return e.subscribe(new rM(t,this.observables,this.project))},t}(),rM=function(t){function e(e,n,r){var i=t.call(this,e)||this;i.observables=n,i.project=r,i.toRespond=[];var $=n.length;i.values=Array($);for(var o=0;o<$;o++)i.toRespond.push(o);for(o=0;o<$;o++){var s=n[o];i.add((0,M.D)(i,s,void 0,o))}return i}return r.ZT(e,t),e.prototype.notifyNext=function(t,e,n){this.values[n]=e;var r=this.toRespond;if(r.length>0){var i=r.indexOf(n);-1!==i&&r.splice(i,1)}},e.prototype.notifyComplete=function(){},e.prototype._next=function(t){if(0===this.toRespond.length){var e=[t].concat(this.values);this.project?this._tryProject(e):this.destination.next(e)}},e.prototype._tryProject=function(t){var e;try{e=this.project.apply(this,t)}catch(n){return void this.destination.error(n)}this.destination.next(e)},e}(I.L),rI=n(5080);function rA(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e){return e.lift.call(rI.$R.apply(void 0,[e].concat(t)))}}function rT(t){return function(e){return e.lift(new rI.mx(t))}}},5987(t,e,n){"use strict";n.d(e,{ZT:()=>i});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function i(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}},9509(t,e,n){var r=n(8764),i=r.Buffer;function $(t,e){for(var n in t)e[n]=t[n]}function o(t,e,n){return i(t,e,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:($(r,e),e.Buffer=o),o.prototype=Object.create(i.prototype),$(i,o),o.from=function(t,e,n){if("number"==typeof t)throw TypeError("Argument must not be a number");return i(t,e,n)},o.alloc=function(t,e,n){if("number"!=typeof t)throw TypeError("Argument must be a number");var r=i(t);return void 0!==e?"string"==typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},o.allocUnsafe=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return i(t)},o.allocUnsafeSlow=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return r.SlowBuffer(t)}},7253(t,e,n){let r=n(9539),i=n(7187);var $="object"==typeof Reflect?Reflect:null,o=$&&"function"==typeof $.apply?$.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};function s(){i.call(this)}function x(t,e,n){try{o(t,e,n)}catch(r){setTimeout(()=>{throw r})}}function a(t,e){for(var n=Array(e),r=0;r<e;++r)n[r]=t[r];return n}t.exports=s,r.inherits(s,i),s.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var r="error"===t,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){if(e.length>0&&($=e[0]),$ instanceof Error)throw $;var $,o=Error("Unhandled error."+($?" ("+$.message+")":""));throw o.context=$,o}var s=i[t];if(void 0===s)return!1;if("function"==typeof s)x(s,this,e);else{var u=s.length,c=a(s,u);for(n=0;n<u;++n)x(c[n],this,e)}return!0}},7478(t,e,n){"use strict";var r=n(210),i=n(1924),$=n(631),o=r("%TypeError%"),s=r("%WeakMap%",!0),x=r("%Map%",!0),a=i("WeakMap.prototype.get",!0),u=i("WeakMap.prototype.set",!0),c=i("WeakMap.prototype.has",!0),l=i("Map.prototype.get",!0),h=i("Map.prototype.set",!0),f=i("Map.prototype.has",!0),d=function(t,e){for(var n,r=t;null!==(n=r.next);r=n)if(n.key===e)return r.next=n.next,n.next=t.next,t.next=n,n};t.exports=function(){var t,e,n,r={assert:function(t){if(!r.has(t))throw new o("Side channel does not contain "+$(t))},get:function(r){if(s&&r&&("object"==typeof r||"function"==typeof r)){if(t)return a(t,r)}else if(x){if(e)return l(e,r)}else if(n){var i,$,o;return i=n,(o=d(i,$=r))&&o.value}},has:function(r){if(s&&r&&("object"==typeof r||"function"==typeof r)){if(t)return c(t,r)}else if(x){if(e)return f(e,r)}else if(n){var i,$;return i=n,!!d(i,$=r)}return!1},set:function(r,i){var $,o,a,c;s&&r&&("object"==typeof r||"function"==typeof r)?(t||(t=new s),u(t,r,i)):x?(e||(e=new x),h(e,r,i)):(n||(n={key:{},next:null}),$=n,o=r,a=i,(c=d($,o))?c.value=a:$.next={key:o,next:$.next,value:a})}};return r}},2553(t,e,n){"use strict";var r=n(9509).Buffer,i=r.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function $(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(r.isEncoding===i||!i(t)))throw Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=x,this.end=a,e=4;break;case"utf8":this.fillLast=s,e=4;break;case"base64":this.text=u,this.end=c,e=3;break;default:return this.write=l,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(e)}function o(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function s(t){var e=this.lastTotal-this.lastNeed,n=function(t,e,n){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==n?n:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function x(t,e){if((t.length-e)%2==0){var n=t.toString("utf16le",e);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function a(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,n)}return e}function u(t,e){var n=(t.length-e)%3;return 0===n?t.toString("base64",e):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-n))}function c(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function l(t){return t.toString(this.encoding)}function h(t){return t&&t.length?this.write(t):""}e.s=$,$.prototype.write=function(t){var e,n;if(0===t.length)return"";if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<t.length?e?e+this.text(t,n):this.text(t,n):e||""},$.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},$.prototype.text=function(t,e){var n=function(t,e,n){var r=e.length-1;if(r<n)return 0;var i=o(e[r]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--r<n||-2===i?0:(i=o(e[r]))>=0?(i>0&&(t.lastNeed=i-2),i):--r<n||-2===i?0:(i=o(e[r]))>=0?(i>0&&(2===i?i=0:t.lastNeed=i-3),i):0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=n;var r=t.length-(n-this.lastNeed);return t.copy(this.lastChar,0,r),t.toString("utf8",e,r)},$.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},655(t,e,n){"use strict";n.r(e),n.d(e,{__assign:()=>$,__asyncDelegator:()=>m,__asyncGenerator:()=>_,__asyncValues:()=>v,__await:()=>b,__awaiter:()=>u,__classPrivateFieldGet:()=>C,__classPrivateFieldIn:()=>I,__classPrivateFieldSet:()=>M,__createBinding:()=>l,__decorate:()=>s,__exportStar:()=>h,__extends:()=>i,__generator:()=>c,__importDefault:()=>E,__importStar:()=>k,__makeTemplateObject:()=>w,__metadata:()=>a,__param:()=>x,__read:()=>d,__rest:()=>o,__spread:()=>p,__spreadArray:()=>g,__spreadArrays:()=>y,__values:()=>f});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var $=function(){return($=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function o(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)0>e.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}function s(t,e,n,r){var i,$=arguments.length,o=$<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(o=($<3?i(o):$>3?i(e,n,o):i(e,n))||o);return $>3&&o&&Object.defineProperty(e,n,o),o}function x(t,e){return function(n,r){e(n,r,t)}}function a(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function u(t,e,n,r){return new(n||(n=Promise))(function(i,$){function o(t){try{x(r.next(t))}catch(e){$(e)}}function s(t){try{x(r.throw(t))}catch(e){$(e)}}function x(t){var e;t.done?i(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(o,s)}x((r=r.apply(t,e||[])).next())})}function c(t,e){var n,r,i,$,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return $={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&($[Symbol.iterator]=function(){return this}),$;function s($){return function(s){return function($){if(n)throw TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&$[0]?r.return:$[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,$[1])).done)return i;switch(r=0,i&&($=[2&$[0],i.value]),$[0]){case 0:case 1:i=$;break;case 4:return o.label++,{value:$[1],done:!1};case 5:o.label++,r=$[1],$=[0];continue;case 7:$=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==$[0]&&2!==$[0])){o=0;continue}if(3===$[0]&&(!i||$[1]>i[0]&&$[1]<i[3])){o.label=$[1];break}if(6===$[0]&&o.label<i[1]){o.label=i[1],i=$;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push($);break}i[2]&&o.ops.pop(),o.trys.pop();continue}$=e.call(t,o)}catch(s){$=[6,s],r=0}finally{n=i=0}if(5&$[0])throw $[1];return{value:$[0]?$[1]:void 0,done:!0}}([$,s])}}}var l=Object.create?function(t,e,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);i&&!("get"in i?!e.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]};function h(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||l(e,t,n)}function f(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function d(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,$=n.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(r=$.next()).done;)o.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(n=$.return)&&n.call($)}finally{if(i)throw i.error}}return o}function p(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(d(arguments[e]));return t}function y(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),i=0;for(e=0;e<n;e++)for(var $=arguments[e],o=0,s=$.length;o<s;o++,i++)r[i]=$[o];return r}function g(t,e,n){if(n||2===arguments.length)for(var r,i=0,$=e.length;i<$;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))}function b(t){return this instanceof b?(this.v=t,this):new b(t)}function _(t,e,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(t,e||[]),$=[];return r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r;function o(t){i[t]&&(r[t]=function(e){return new Promise(function(n,r){$.push([t,e,n,r])>1||s(t,e)})})}function s(t,e){var n;try{(n=i[t](e)).value instanceof b?Promise.resolve(n.value.v).then(x,a):u($[0][2],n)}catch(r){u($[0][3],r)}}function x(t){s("next",t)}function a(t){s("throw",t)}function u(t,e){t(e),$.shift(),$.length&&s($[0][0],$[0][1])}}function m(t){var e,n;return e={},r("next"),r("throw",function(t){throw t}),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,i){e[r]=t[r]?function(e){return(n=!n)?{value:b(t[r](e)),done:"return"===r}:i?i(e):e}:i}}function v(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=f(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise(function(r,i){!function(t,e,n,r){Promise.resolve(r).then(function(e){t({value:e,done:n})},e)}(r,i,(e=t[n](e)).done,e.value)})}}}function w(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var S=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};function k(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&l(e,t,n);return S(e,t),e}function E(t){return t&&t.__esModule?t:{default:t}}function C(t,e,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(t):r?r.value:e.get(t)}function M(t,e,n,r,i){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(t,n):i?i.value=n:e.set(t,n),n}function I(t,e){if(null===e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof t?e===t:t.has(e)}},4927(t,e,n){function r(t){try{if(!n.g.localStorage)return!1}catch(e){return!1}var r=n.g.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=function(t,e){if(r("noDeprecation"))return t;var n=!1;return function(){if(!n){if(r("throwDeprecation"))throw Error(e);r("traceDeprecation")?console.trace(e):console.warn(e),n=!0}return t.apply(this,arguments)}}},384(t){t.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},5955(t,e,n){"use strict";var r=n(2584),i=n(8662),$=n(6430),o=n(5692);function s(t){return t.call.bind(t)}var x="undefined"!=typeof BigInt,a="undefined"!=typeof Symbol,u=s(Object.prototype.toString),c=s(Number.prototype.valueOf),l=s(String.prototype.valueOf),h=s(Boolean.prototype.valueOf);if(x)var f=s(BigInt.prototype.valueOf);if(a)var d=s(Symbol.prototype.valueOf);function p(t,e){if("object"!=typeof t)return!1;try{return e(t),!0}catch(n){return!1}}function y(t){return"[object Map]"===u(t)}function g(t){return"[object Set]"===u(t)}function b(t){return"[object WeakMap]"===u(t)}function _(t){return"[object WeakSet]"===u(t)}function m(t){return"[object ArrayBuffer]"===u(t)}function v(t){return"undefined"!=typeof ArrayBuffer&&(m.working?m(t):t instanceof ArrayBuffer)}function w(t){return"[object DataView]"===u(t)}function S(t){return"undefined"!=typeof DataView&&(w.working?w(t):t instanceof DataView)}e.isArgumentsObject=r,e.isGeneratorFunction=i,e.isTypedArray=o,e.isPromise=function(t){return"undefined"!=typeof Promise&&t instanceof Promise||null!==t&&"object"==typeof t&&"function"==typeof t.then&&"function"==typeof t.catch},e.isArrayBufferView=function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):o(t)||S(t)},e.isUint8Array=function(t){return"Uint8Array"===$(t)},e.isUint8ClampedArray=function(t){return"Uint8ClampedArray"===$(t)},e.isUint16Array=function(t){return"Uint16Array"===$(t)},e.isUint32Array=function(t){return"Uint32Array"===$(t)},e.isInt8Array=function(t){return"Int8Array"===$(t)},e.isInt16Array=function(t){return"Int16Array"===$(t)},e.isInt32Array=function(t){return"Int32Array"===$(t)},e.isFloat32Array=function(t){return"Float32Array"===$(t)},e.isFloat64Array=function(t){return"Float64Array"===$(t)},e.isBigInt64Array=function(t){return"BigInt64Array"===$(t)},e.isBigUint64Array=function(t){return"BigUint64Array"===$(t)},y.working="undefined"!=typeof Map&&y(new Map),e.isMap=function(t){return"undefined"!=typeof Map&&(y.working?y(t):t instanceof Map)},g.working="undefined"!=typeof Set&&g(new Set),e.isSet=function(t){return"undefined"!=typeof Set&&(g.working?g(t):t instanceof Set)},b.working="undefined"!=typeof WeakMap&&b(new WeakMap),e.isWeakMap=function(t){return"undefined"!=typeof WeakMap&&(b.working?b(t):t instanceof WeakMap)},_.working="undefined"!=typeof WeakSet&&_(new WeakSet),e.isWeakSet=function(t){return _(t)},m.working="undefined"!=typeof ArrayBuffer&&m(new ArrayBuffer),e.isArrayBuffer=v,w.working="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView&&w(new DataView(new ArrayBuffer(1),0,1)),e.isDataView=S;var k="undefined"!=typeof SharedArrayBuffer?SharedArrayBuffer:void 0;function E(t){return"[object SharedArrayBuffer]"===u(t)}function C(t){return void 0!==k&&(void 0===E.working&&(E.working=E(new k)),E.working?E(t):t instanceof k)}function M(t){return p(t,c)}function I(t){return p(t,l)}function A(t){return p(t,h)}function T(t){return x&&p(t,f)}function R(t){return a&&p(t,d)}e.isSharedArrayBuffer=C,e.isAsyncFunction=function(t){return"[object AsyncFunction]"===u(t)},e.isMapIterator=function(t){return"[object Map Iterator]"===u(t)},e.isSetIterator=function(t){return"[object Set Iterator]"===u(t)},e.isGeneratorObject=function(t){return"[object Generator]"===u(t)},e.isWebAssemblyCompiledModule=function(t){return"[object WebAssembly.Module]"===u(t)},e.isNumberObject=M,e.isStringObject=I,e.isBooleanObject=A,e.isBigIntObject=T,e.isSymbolObject=R,e.isBoxedPrimitive=function(t){return M(t)||I(t)||A(t)||T(t)||R(t)},e.isAnyArrayBuffer=function(t){return"undefined"!=typeof Uint8Array&&(v(t)||C(t))},["isProxy","isExternal","isModuleNamespaceObject"].forEach(function(t){Object.defineProperty(e,t,{enumerable:!1,value:function(){throw Error(t+" is not supported in userland")}})})},9539(t,e,n){var r=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),n={},r=0;r<e.length;r++)n[e[r]]=Object.getOwnPropertyDescriptor(t,e[r]);return n},i=/%[sdj%]/g;e.format=function(t){if(!g(t)){for(var e=[],n=0;n<arguments.length;n++)e.push(x(arguments[n]));return e.join(" ")}n=1;for(var r=arguments,$=r.length,o=String(t).replace(i,function(t){if("%%"===t)return"%";if(n>=$)return t;switch(t){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(e){return"[Circular]"}default:return t}}),s=r[n];n<$;s=r[++n])p(s)||!m(s)?o+=" "+s:o+=" "+x(s);return o},e.deprecate=function(t,n){if("undefined"!=typeof process&&!0===process.noDeprecation)return t;if("undefined"==typeof process)return function(){return e.deprecate(t,n).apply(this,arguments)};var r=!1;return function(){if(!r){if(process.throwDeprecation)throw Error(n);process.traceDeprecation?console.trace(n):console.error(n),r=!0}return t.apply(this,arguments)}};var $={},o=/^$/;if(({NODE_ENV:"production",WALLETLINK_URL:void 0,WALLETLINK_VERSION:"3.3.0"}).NODE_DEBUG){var s={NODE_ENV:"production",WALLETLINK_URL:void 0,WALLETLINK_VERSION:"3.3.0"}.NODE_DEBUG;o=RegExp("^"+(s=s.replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^").toUpperCase())+"$","i")}function x(t,n){var r={seen:[],stylize:u};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),d(n)?r.showHidden=n:n&&e._extend(r,n),b(r.showHidden)&&(r.showHidden=!1),b(r.depth)&&(r.depth=2),b(r.colors)&&(r.colors=!1),b(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=a),c(r,t,r.depth)}function a(t,e){var n=x.styles[e];return n?"\x1b["+x.colors[n][0]+"m"+t+"\x1b["+x.colors[n][1]+"m":t}function u(t,e){return t}function c(t,n,r){if(t.customInspect&&n&&S(n.inspect)&&n.inspect!==e.inspect&&(!n.constructor||n.constructor.prototype!==n)){var i=n.inspect(r,t);return g(i)||(i=c(t,i,r)),i}var $=function(t,e){if(b(e))return t.stylize("undefined","undefined");if(g(e)){var n="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(n,"string")}return y(e)?t.stylize(""+e,"number"):d(e)?t.stylize(""+e,"boolean"):p(e)?t.stylize("null","null"):void 0}(t,n);if($)return $;var o,s,x=Object.keys(n),a=(o=x,s={},o.forEach(function(t,e){s[t]=!0}),s);if(t.showHidden&&(x=Object.getOwnPropertyNames(n)),w(n)&&(x.indexOf("message")>=0||x.indexOf("description")>=0))return l(n);if(0===x.length){if(S(n)){var u=n.name?": "+n.name:"";return t.stylize("[Function"+u+"]","special")}if(_(n))return t.stylize(RegExp.prototype.toString.call(n),"regexp");if(v(n))return t.stylize(Date.prototype.toString.call(n),"date");if(w(n))return l(n)}var m,k,E,C,M="",A=!1,T=["{","}"];return f(n)&&(A=!0,T=["[","]"]),S(n)&&(M=" [Function"+(n.name?": "+n.name:"")+"]"),_(n)&&(M=" "+RegExp.prototype.toString.call(n)),v(n)&&(M=" "+Date.prototype.toUTCString.call(n)),w(n)&&(M=" "+l(n)),0!==x.length||A&&0!=n.length?r<0?_(n)?t.stylize(RegExp.prototype.toString.call(n),"regexp"):t.stylize("[Object]","special"):(t.seen.push(n),m=A?function(t,e,n,r,i){for(var $=[],o=0,s=e.length;o<s;++o)I(e,String(o))?$.push(h(t,e,n,r,String(o),!0)):$.push("");return i.forEach(function(i){i.match(/^\d+$/)||$.push(h(t,e,n,r,i,!0))}),$}(t,n,r,a,x):x.map(function(e){return h(t,n,r,a,e,A)}),t.seen.pop(),k=m,E=M,C=T,k.reduce(function(t,e){return e.indexOf("\n"),t+e.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?C[0]+(""===E?"":E+"\n ")+" "+k.join(",\n  ")+" "+C[1]:C[0]+E+" "+k.join(", ")+" "+C[1]):T[0]+M+T[1]}function l(t){return"["+Error.prototype.toString.call(t)+"]"}function h(t,e,n,r,i,$){var o,s,x;if((x=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]}).get?s=x.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):x.set&&(s=t.stylize("[Setter]","special")),I(r,i)||(o="["+i+"]"),s||(0>t.seen.indexOf(x.value)?(s=p(n)?c(t,x.value,null):c(t,x.value,n-1)).indexOf("\n")>-1&&(s=$?s.split("\n").map(function(t){return"  "+t}).join("\n").substr(2):"\n"+s.split("\n").map(function(t){return"   "+t}).join("\n")):s=t.stylize("[Circular]","special")),b(o)){if($&&i.match(/^\d+$/))return s;(o=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(o=o.substr(1,o.length-2),o=t.stylize(o,"name")):(o=o.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),o=t.stylize(o,"string"))}return o+": "+s}function f(t){return Array.isArray(t)}function d(t){return"boolean"==typeof t}function p(t){return null===t}function y(t){return"number"==typeof t}function g(t){return"string"==typeof t}function b(t){return void 0===t}function _(t){return m(t)&&"[object RegExp]"===k(t)}function m(t){return"object"==typeof t&&null!==t}function v(t){return m(t)&&"[object Date]"===k(t)}function w(t){return m(t)&&("[object Error]"===k(t)||t instanceof Error)}function S(t){return"function"==typeof t}function k(t){return Object.prototype.toString.call(t)}function E(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(t){if(!$[t=t.toUpperCase()]){if(o.test(t)){var n=process.pid;$[t]=function(){var r=e.format.apply(e,arguments);console.error("%s %d: %s",t,n,r)}}else $[t]=function(){}}return $[t]},e.inspect=x,x.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},x.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.types=n(5955),e.isArray=f,e.isBoolean=d,e.isNull=p,e.isNullOrUndefined=function(t){return null==t},e.isNumber=y,e.isString=g,e.isSymbol=function(t){return"symbol"==typeof t},e.isUndefined=b,e.isRegExp=_,e.types.isRegExp=_,e.isObject=m,e.isDate=v,e.types.isDate=v,e.isError=w,e.types.isNativeError=w,e.isFunction=S,e.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},e.isBuffer=n(384);var C=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function M(){var t=new Date,e=[E(t.getHours()),E(t.getMinutes()),E(t.getSeconds())].join(":");return[t.getDate(),C[t.getMonth()],e].join(" ")}function I(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",M(),e.format.apply(e,arguments))},e.inherits=n(5717),e._extend=function(t,e){if(!e||!m(e))return t;for(var n=Object.keys(e),r=n.length;r--;)t[n[r]]=e[n[r]];return t};var A="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function T(t,e){if(!t){var n=Error("Promise was rejected with a falsy value");n.reason=t,t=n}return e(t)}e.promisify=function(t){if("function"!=typeof t)throw TypeError('The "original" argument must be of type Function');if(A&&t[A]){var e;if("function"!=typeof(e=t[A]))throw TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,A,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,n,r=new Promise(function(t,r){e=t,n=r}),i=[],$=0;$<arguments.length;$++)i.push(arguments[$]);i.push(function(t,r){t?n(t):e(r)});try{t.apply(this,i)}catch(o){n(o)}return r}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),A&&Object.defineProperty(e,A,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,r(t))},e.promisify.custom=A,e.callbackify=function(t){if("function"!=typeof t)throw TypeError('The "original" argument must be of type Function');function e(){for(var e=[],n=0;n<arguments.length;n++)e.push(arguments[n]);var r=e.pop();if("function"!=typeof r)throw TypeError("The last argument must be of type Function");var i=this,$=function(){return r.apply(i,arguments)};t.apply(this,e).then(function(t){process.nextTick($.bind(null,null,t))},function(t){process.nextTick(T.bind(null,t,$))})}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),Object.defineProperties(e,r(t)),e}},6430(t,e,n){"use strict";var r=n(4029),i=n(3083),$=n(1924),o=$("Object.prototype.toString"),s=n(6410)(),x="undefined"==typeof globalThis?n.g:globalThis,a=i(),u=$("String.prototype.slice"),c={},l=n(882),h=Object.getPrototypeOf;s&&l&&h&&r(a,function(t){if("function"==typeof x[t]){var e=new x[t];if(Symbol.toStringTag in e){var n=h(e),r=l(n,Symbol.toStringTag);if(!r){var i=h(n);r=l(i,Symbol.toStringTag)}c[t]=r.get}}});var f=n(5692);t.exports=function(t){var e,n;return!!f(t)&&(s&&Symbol.toStringTag in t?(e=t,n=!1,r(c,function(t,r){if(!n)try{var i=t.call(e);i===r&&(n=i)}catch($){}}),n):u(o(t),8,-1))}},7529(t){t.exports=function(){for(var t={},n=0;n<arguments.length;n++){var r=arguments[n];for(var i in r)e.call(r,i)&&(t[i]=r[i])}return t};var e=Object.prototype.hasOwnProperty},6601(){},4654(){},2361(){},4616(){},3083(t,e,n){"use strict";var r=["BigInt64Array","BigUint64Array","Float32Array","Float64Array","Int16Array","Int32Array","Int8Array","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray"],i="undefined"==typeof globalThis?n.g:globalThis;t.exports=function(){for(var t=[],e=0;e<r.length;e++)"function"==typeof i[r[e]]&&(t[t.length]=r[e]);return t}},882(t,e,n){"use strict";var r=n(210)("%Object.getOwnPropertyDescriptor%",!0);if(r)try{r([],"length")}catch(i){r=null}t.exports=r},626(t){"use strict";t.exports={i8:"3.3.0"}}},_0x31d7c1={};function _0x5b7ba3(t){var e=_0x31d7c1[t];if(void 0!==e)return e.exports;var n=_0x31d7c1[t]={id:t,loaded:!1,exports:{}};return _0x4dd2ac[t].call(n.exports,n,n.exports,_0x5b7ba3),n.loaded=!0,n.exports}_0x5b7ba3.amdO={},_0x5b7ba3.d=(t,e)=>{for(var n in e)_0x5b7ba3.o(e,n)&&!_0x5b7ba3.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},_0x5b7ba3.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),_0x5b7ba3.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),_0x5b7ba3.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},_0x5b7ba3.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var _0x26053c={};_0x5b7ba3(5811)})();