/* Navigation Styles */
#navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

/* Logo */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-img {
    width: 32px;
    height: 32px;
}

.logo-text {
    background: linear-gradient(135deg, var(--btn-primary), var(--btn-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Links */
.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.nav-link.active {
    color: var(--text-accent);
    background-color: var(--bg-tertiary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--text-accent);
    border-radius: 1px;
}

/* Right Side Navigation */
.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Gas Tracker */
.gas-tracker {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.gas-icon {
    font-size: 1rem;
}

.gas-price {
    font-weight: 600;
    color: var(--text-primary);
}

.gas-unit {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
}

.theme-icon {
    font-size: 1.125rem;
    transition: transform 0.2s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

/* Connect Wallet Button */
.connect-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--btn-primary), var(--btn-accent));
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.connect-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.wallet-icon {
    font-size: 1rem;
}

/* Wallet Info */
.wallet-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    min-width: 200px;
}

.wallet-address {
    font-size: 0.75rem;
    font-family: 'Monaco', 'Menlo', monospace;
    color: var(--text-secondary);
    word-break: break-all;
}

.wallet-balance {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.disconnect-btn {
    padding: 0.5rem 1rem;
    background-color: var(--error);
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.disconnect-btn:hover {
    background-color: #dc2626;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.75rem;
        height: 70px;
    }
    
    .nav-left {
        gap: 1rem;
    }
    
    .nav-links {
        display: none;
    }
    
    .nav-right {
        gap: 0.5rem;
    }
    
    .gas-tracker {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .connect-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
    
    .wallet-text {
        display: none;
    }
    
    .theme-toggle {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .logo-text {
        display: none;
    }
    
    .gas-tracker {
        display: none;
    }
    
    .nav-container {
        height: 60px;
    }
}

/* Mobile Menu (for future implementation) */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 3px;
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.mobile-menu-btn span {
    width: 100%;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.2s ease;
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
    }
}

/* Pro Dashboard Indicator */
.pro-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background-color: var(--btn-accent);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
