// Production Configuration and Environment Setup
class ProductionConfig {
    constructor() {
        this.environment = this.detectEnvironment();
        this.contracts = this.getContractAddresses();
        this.apiKeys = this.getAPIKeys();
        this.features = this.getFeatureFlags();
        
        this.init();
    }

    detectEnvironment() {
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'development';
        } else if (hostname.includes('staging') || hostname.includes('test')) {
            return 'staging';
        } else {
            return 'production';
        }
    }

    getContractAddresses() {
        const contracts = {
            development: {
                // Testnet addresses (Goerli/Sepolia)
                wtfToken: '0x...', // Replace with testnet WTF token
                wtfStaking: '0x...', // Replace with testnet staking contract
                lpToken: '0x...', // Replace with testnet LP token
                lpStaking: '0x...', // Replace with testnet LP staking
                nftCollection: '0x...', // Replace with testnet NFT contract
                chainId: 5 // Goerli
            },
            staging: {
                // Staging environment contracts
                wtfToken: '0x...',
                wtfStaking: '0x...',
                lpToken: '0x...',
                lpStaking: '0x...',
                nftCollection: '0x...',
                chainId: 5 // Goerli
            },
            production: {
                // Mainnet addresses
                wtfToken: '******************************************',
                wtfStaking: '0x...', // Replace with actual mainnet staking contract
                lpToken: '0x...', // Replace with actual mainnet LP token
                lpStaking: '0x...', // Replace with actual mainnet LP staking
                nftCollection: '0x...', // Replace with actual mainnet NFT contract
                chainId: 1 // Ethereum Mainnet
            }
        };

        return contracts[this.environment];
    }

    getAPIKeys() {
        // In production, these should come from environment variables
        // For client-side apps, use a backend proxy for sensitive keys
        return {
            etherscan: process.env.ETHERSCAN_API_KEY || 'YourEtherscanAPIKey',
            coingecko: null, // CoinGecko doesn't require API key for basic usage
            coinmarketcap: process.env.CMC_API_KEY || 'YourCMCAPIKey',
            oneinch: process.env.ONEINCH_API_KEY || null,
            opensea: process.env.OPENSEA_API_KEY || 'YourOpenSeaAPIKey',
            alchemy: process.env.ALCHEMY_API_KEY || 'YourAlchemyAPIKey',
            infura: process.env.INFURA_API_KEY || 'YourInfuraAPIKey'
        };
    }

    getFeatureFlags() {
        return {
            // Feature toggles for different environments
            enableRealTimeUpdates: this.environment === 'production',
            enableAdvancedCharts: true,
            enableNotifications: this.environment !== 'development',
            enableAnalytics: this.environment === 'production',
            enableErrorTracking: this.environment !== 'development',
            enableCaching: true,
            enableFallbacks: true,
            
            // API feature flags
            useMultipleGasAPIs: true,
            useDEXAggregators: this.environment === 'production',
            useRealNFTData: this.environment !== 'development',
            useBlockchainData: true,
            
            // UI feature flags
            showDebugInfo: this.environment === 'development',
            showPerformanceMetrics: this.environment !== 'production',
            enableBetaFeatures: this.environment !== 'production'
        };
    }

    init() {
        // Set global configuration
        window.PRODUCTION_CONFIG = {
            environment: this.environment,
            contracts: this.contracts,
            apiKeys: this.apiKeys,
            features: this.features
        };

        // Update API config with production settings
        if (window.apiConfig) {
            this.updateAPIConfig();
        }

        // Initialize error tracking in production
        if (this.features.enableErrorTracking) {
            this.initErrorTracking();
        }

        // Initialize analytics in production
        if (this.features.enableAnalytics) {
            this.initAnalytics();
        }

        // Set up performance monitoring
        this.initPerformanceMonitoring();

        console.log(`🚀 Fees.WTF initialized in ${this.environment} mode`);
    }

    updateAPIConfig() {
        // Update API endpoints with production keys
        Object.keys(this.apiKeys).forEach(apiName => {
            if (this.apiKeys[apiName] && window.apiConfig.endpoints[apiName]) {
                window.apiConfig.endpoints[apiName].apiKey = this.apiKeys[apiName];
            }
        });

        // Update rate limits for production
        if (this.environment === 'production') {
            window.apiConfig.rateLimits = {
                ...window.apiConfig.rateLimits,
                etherscan: { requests: 5, window: 1000 },
                coingecko: { requests: 50, window: 60000 }, // Higher limit for production
                opensea: { requests: 4, window: 1000 }
            };
        }
    }

    initErrorTracking() {
        // Initialize error tracking service (Sentry, LogRocket, etc.)
        // Example for Sentry:
        /*
        if (window.Sentry) {
            window.Sentry.init({
                dsn: 'YOUR_SENTRY_DSN',
                environment: this.environment,
                beforeSend(event) {
                    // Filter out development errors
                    if (event.environment === 'development') {
                        return null;
                    }
                    return event;
                }
            });
        }
        */
        console.log('Error tracking initialized for', this.environment);
    }

    initAnalytics() {
        // Initialize analytics (Google Analytics, Mixpanel, etc.)
        // Example for Google Analytics:
        /*
        if (window.gtag) {
            window.gtag('config', 'GA_MEASUREMENT_ID', {
                page_title: 'Fees.WTF',
                page_location: window.location.href
            });
        }
        */
        console.log('Analytics initialized for', this.environment);
    }

    initPerformanceMonitoring() {
        // Monitor key performance metrics
        if ('performance' in window) {
            // Track page load time
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
                
                if (this.features.enableAnalytics) {
                    // Send to analytics service
                    this.trackPerformance('page_load', loadTime);
                }
            });

            // Track navigation timing
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    console.log('Navigation timing:', {
                        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
                        tcp: navigation.connectEnd - navigation.connectStart,
                        request: navigation.responseStart - navigation.requestStart,
                        response: navigation.responseEnd - navigation.responseStart,
                        dom: navigation.domContentLoadedEventEnd - navigation.responseEnd
                    });
                }
            }, 1000);
        }
    }

    trackPerformance(metric, value) {
        // Send performance metrics to analytics
        if (this.features.enableAnalytics && window.gtag) {
            window.gtag('event', 'timing_complete', {
                name: metric,
                value: Math.round(value)
            });
        }
    }

    // Network detection and handling
    getNetworkConfig() {
        return {
            chainId: this.contracts.chainId,
            chainName: this.contracts.chainId === 1 ? 'Ethereum Mainnet' : 'Goerli Testnet',
            rpcUrls: this.getRPCUrls(),
            blockExplorerUrls: this.getBlockExplorerUrls()
        };
    }

    getRPCUrls() {
        const rpcUrls = {
            1: [`https://mainnet.infura.io/v3/${this.apiKeys.infura}`],
            5: [`https://goerli.infura.io/v3/${this.apiKeys.infura}`]
        };
        
        return rpcUrls[this.contracts.chainId] || [];
    }

    getBlockExplorerUrls() {
        const explorers = {
            1: ['https://etherscan.io'],
            5: ['https://goerli.etherscan.io']
        };
        
        return explorers[this.contracts.chainId] || [];
    }

    // Utility methods
    isMainnet() {
        return this.contracts.chainId === 1;
    }

    isTestnet() {
        return this.contracts.chainId !== 1;
    }

    isDevelopment() {
        return this.environment === 'development';
    }

    isProduction() {
        return this.environment === 'production';
    }

    // Feature flag helpers
    isFeatureEnabled(feature) {
        return this.features[feature] || false;
    }

    getContractAddress(contractName) {
        return this.contracts[contractName] || null;
    }

    getAPIKey(apiName) {
        return this.apiKeys[apiName] || null;
    }
}

// Initialize production configuration
window.productionConfig = new ProductionConfig();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductionConfig;
}
