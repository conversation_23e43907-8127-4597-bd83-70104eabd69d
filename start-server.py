#!/usr/bin/env python3
"""
Simple HTTP server to serve the Fees.WTF application
Run this script to start a local development server
"""

import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8000

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    print("🚀 Starting Fees.WTF Development Server...")
    print(f"📁 Serving files from: {os.getcwd()}")
    print(f"🌐 Server will be available at: http://localhost:{PORT}")
    print("💡 Make sure you have a Web3 wallet installed (MetaMask, Coinbase Wallet, etc.)")
    print("\n" + "="*60)
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server started successfully on port {PORT}")
            print("🔗 Opening browser...")
            
            # Open browser automatically
            webbrowser.open(f'http://localhost:{PORT}')
            
            print("\n📋 Available routes:")
            print("   • http://localhost:8000/           - Dashboard")
            print("   • http://localhost:8000/#/stake/wtf - WTF Staking")
            print("   • http://localhost:8000/#/stake/lp  - LP Staking")
            print("   • http://localhost:8000/#/swap      - Token Swap")
            print("   • http://localhost:8000/#/nft       - NFT Collection")
            
            print(f"\n🛑 Press Ctrl+C to stop the server")
            print("="*60 + "\n")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("👋 Thanks for using Fees.WTF!")
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Error: Port {PORT} is already in use")
            print(f"💡 Try using a different port or stop the existing server")
            print(f"🔧 You can also run: python3 start-server.py {PORT + 1}")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Allow custom port as command line argument
    if len(sys.argv) > 1:
        try:
            PORT = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number. Using default port 8000.")
    
    main()
