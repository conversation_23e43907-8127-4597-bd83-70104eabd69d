// Production API Configuration and Management
class APIConfig {
    constructor() {
        // API Endpoints
        this.endpoints = {
            // Gas Price APIs
            etherscan: {
                baseUrl: 'https://api.etherscan.io/api',
                gasPrice: '/api?module=gastracker&action=gasoracle',
                apiKey: this.getApiKey('ETHERSCAN_API_KEY') || 'YourEtherscanAPIKey'
            },
            ethGasStation: {
                baseUrl: 'https://ethgasstation.info/api',
                gasPrice: '/ethgasAPI.json'
            },
            
            // Price APIs
            coingecko: {
                baseUrl: 'https://api.coingecko.com/api/v3',
                ethPrice: '/simple/price?ids=ethereum&vs_currencies=usd&include_24hr_change=true',
                tokenPrices: '/simple/price'
            },
            coinmarketcap: {
                baseUrl: 'https://pro-api.coinmarketcap.com/v1',
                apiKey: this.getApiKey('CMC_API_KEY') || 'YourCMCAPIKey'
            },
            
            // DeFi APIs
            dexscreener: {
                baseUrl: 'https://api.dexscreener.com/latest/dex',
                tokenPairs: '/tokens',
                search: '/search'
            },
            oneinch: {
                baseUrl: 'https://api.1inch.io/v5.0/1',
                quote: '/quote',
                swap: '/swap',
                tokens: '/tokens',
                apiKey: this.getApiKey('ONEINCH_API_KEY')
            },
            zeroex: {
                baseUrl: 'https://api.0x.org',
                quote: '/swap/v1/quote',
                price: '/swap/v1/price'
            },
            
            // NFT APIs
            opensea: {
                baseUrl: 'https://api.opensea.io/api/v1',
                collection: '/collection',
                assets: '/assets',
                events: '/events',
                apiKey: this.getApiKey('OPENSEA_API_KEY')
            },
            alchemy: {
                baseUrl: 'https://eth-mainnet.g.alchemy.com/v2',
                apiKey: this.getApiKey('ALCHEMY_API_KEY') || 'YourAlchemyAPIKey'
            },
            
            // Blockchain RPC
            infura: {
                baseUrl: 'https://mainnet.infura.io/v3',
                apiKey: this.getApiKey('INFURA_API_KEY') || 'YourInfuraAPIKey'
            }
        };
        
        // Rate limiting configuration
        this.rateLimits = {
            etherscan: { requests: 5, window: 1000 }, // 5 requests per second
            coingecko: { requests: 10, window: 60000 }, // 10 requests per minute
            opensea: { requests: 4, window: 1000 }, // 4 requests per second
            oneinch: { requests: 10, window: 1000 }, // 10 requests per second
            default: { requests: 5, window: 1000 }
        };
        
        // Cache configuration
        this.cacheConfig = {
            gasPrice: { ttl: 30000 }, // 30 seconds
            ethPrice: { ttl: 60000 }, // 1 minute
            tokenPrices: { ttl: 30000 }, // 30 seconds
            nftCollection: { ttl: 300000 }, // 5 minutes
            nftAssets: { ttl: 120000 }, // 2 minutes
            swapQuote: { ttl: 10000 }, // 10 seconds
            stakingData: { ttl: 60000 } // 1 minute
        };
        
        this.initializeCache();
        this.initializeRateLimiter();
    }
    
    getApiKey(keyName) {
        // Try to get API key from environment variables or localStorage
        if (typeof process !== 'undefined' && process.env) {
            return process.env[keyName];
        }
        
        // For client-side, check localStorage (not recommended for production)
        if (typeof localStorage !== 'undefined') {
            return localStorage.getItem(keyName);
        }
        
        return null;
    }
    
    initializeCache() {
        this.cache = new Map();
        this.cacheTimestamps = new Map();
    }
    
    initializeRateLimiter() {
        this.rateLimiters = new Map();
        
        Object.keys(this.rateLimits).forEach(api => {
            this.rateLimiters.set(api, {
                requests: [],
                config: this.rateLimits[api]
            });
        });
    }
    
    async checkRateLimit(apiName) {
        const limiter = this.rateLimiters.get(apiName) || this.rateLimiters.get('default');
        const now = Date.now();
        const config = limiter.config;
        
        // Remove old requests outside the window
        limiter.requests = limiter.requests.filter(time => now - time < config.window);
        
        if (limiter.requests.length >= config.requests) {
            const oldestRequest = Math.min(...limiter.requests);
            const waitTime = config.window - (now - oldestRequest);
            
            if (waitTime > 0) {
                console.warn(`Rate limit reached for ${apiName}. Waiting ${waitTime}ms`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
                return this.checkRateLimit(apiName);
            }
        }
        
        limiter.requests.push(now);
        return true;
    }
    
    getCachedData(key) {
        const cached = this.cache.get(key);
        const timestamp = this.cacheTimestamps.get(key);
        
        if (!cached || !timestamp) return null;
        
        const cacheType = this.getCacheType(key);
        const config = this.cacheConfig[cacheType] || { ttl: 60000 };
        
        if (Date.now() - timestamp > config.ttl) {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
            return null;
        }
        
        return cached;
    }
    
    setCachedData(key, data) {
        this.cache.set(key, data);
        this.cacheTimestamps.set(key, Date.now());
    }
    
    getCacheType(key) {
        if (key.includes('gas')) return 'gasPrice';
        if (key.includes('eth-price')) return 'ethPrice';
        if (key.includes('token-price')) return 'tokenPrices';
        if (key.includes('nft-collection')) return 'nftCollection';
        if (key.includes('nft-assets')) return 'nftAssets';
        if (key.includes('swap-quote')) return 'swapQuote';
        if (key.includes('staking')) return 'stakingData';
        return 'default';
    }
    
    async makeRequest(apiName, endpoint, options = {}) {
        const cacheKey = `${apiName}-${endpoint}-${JSON.stringify(options.params || {})}`;
        
        // Check cache first
        const cached = this.getCachedData(cacheKey);
        if (cached && !options.skipCache) {
            return cached;
        }
        
        // Check rate limit
        await this.checkRateLimit(apiName);
        
        try {
            const apiConfig = this.endpoints[apiName];
            if (!apiConfig) {
                throw new Error(`Unknown API: ${apiName}`);
            }
            
            const url = new URL(endpoint, apiConfig.baseUrl);
            
            // Add API key if required
            if (apiConfig.apiKey) {
                if (apiName === 'etherscan') {
                    url.searchParams.append('apikey', apiConfig.apiKey);
                } else if (apiName === 'coinmarketcap') {
                    options.headers = {
                        ...options.headers,
                        'X-CMC_PRO_API_KEY': apiConfig.apiKey
                    };
                } else if (apiName === 'opensea') {
                    options.headers = {
                        ...options.headers,
                        'X-API-KEY': apiConfig.apiKey
                    };
                }
            }
            
            // Add query parameters
            if (options.params) {
                Object.entries(options.params).forEach(([key, value]) => {
                    url.searchParams.append(key, value);
                });
            }
            
            const response = await fetch(url.toString(), {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: options.body ? JSON.stringify(options.body) : undefined
            });
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Cache successful responses
            if (!options.skipCache) {
                this.setCachedData(cacheKey, data);
            }
            
            return data;
            
        } catch (error) {
            console.error(`API request failed for ${apiName}:`, error);
            throw error;
        }
    }
    
    // Utility method to get the best available API for a service
    getBestAPI(service) {
        const apiPriority = {
            gasPrice: ['etherscan', 'ethGasStation'],
            ethPrice: ['coingecko', 'coinmarketcap'],
            tokenPrices: ['coingecko', 'dexscreener'],
            swapQuote: ['oneinch', 'zeroex'],
            nftData: ['opensea', 'alchemy']
        };
        
        return apiPriority[service] || [];
    }
    
    // Health check for APIs
    async checkAPIHealth() {
        const results = {};
        
        for (const [apiName, config] of Object.entries(this.endpoints)) {
            try {
                const startTime = Date.now();
                
                // Simple health check endpoint or basic request
                let testEndpoint = '/';
                if (apiName === 'coingecko') testEndpoint = '/ping';
                if (apiName === 'etherscan') testEndpoint = '/api?module=stats&action=ethsupply';
                
                await this.makeRequest(apiName, testEndpoint, { skipCache: true });
                
                results[apiName] = {
                    status: 'healthy',
                    responseTime: Date.now() - startTime
                };
            } catch (error) {
                results[apiName] = {
                    status: 'unhealthy',
                    error: error.message
                };
            }
        }
        
        return results;
    }
    
    // Clear cache
    clearCache(pattern = null) {
        if (pattern) {
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                    this.cacheTimestamps.delete(key);
                }
            }
        } else {
            this.cache.clear();
            this.cacheTimestamps.clear();
        }
    }
}

// Create global instance
window.apiConfig = new APIConfig();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIConfig;
}
