/* Dashboard Specific Styles */

/* Chart Section */
.chart-section {
    margin-bottom: 3rem;
}

.chart-section h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-container {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    height: 400px;
    position: relative;
}

.chart-container canvas {
    max-height: 100%;
}

/* Tools Section */
.tools-section {
    margin-bottom: 3rem;
}

.tools-section h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--text-accent);
}

.tool-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.tool-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.tool-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Gas Price Indicators */
.gas-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.gas-indicator.low {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.gas-indicator.medium {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.gas-indicator.high {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Network Status */
.network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success);
    animation: pulse 2s infinite;
}

.status-dot.warning {
    background-color: var(--warning);
}

.status-dot.error {
    background-color: var(--error);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.quick-action:hover {
    background-color: var(--bg-tertiary);
    transform: translateY(-1px);
}

.quick-action-icon {
    font-size: 1rem;
}

/* Recent Transactions */
.recent-transactions {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.recent-transactions h3 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: var(--bg-tertiary);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.transaction-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.transaction-icon.send {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.transaction-icon.receive {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.transaction-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.transaction-type {
    font-weight: 500;
    color: var(--text-primary);
}

.transaction-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.transaction-amount {
    font-weight: 600;
    color: var(--text-primary);
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--text-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Dashboard */
@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .tool-card {
        padding: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
        padding: 1rem;
    }
    
    .network-status {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quick-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .tool-card {
        padding: 1rem;
    }
    
    .tool-icon {
        font-size: 2.5rem;
    }
    
    .chart-container {
        height: 250px;
        padding: 0.75rem;
    }
}
