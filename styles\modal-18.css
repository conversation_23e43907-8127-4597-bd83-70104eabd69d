@font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 400;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertRegular.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 400;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertRegularItalic.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 200;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertLight.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 200;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertLightItalic.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 500;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertMedium.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 500;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertMediumItalic.woff') format('woff');
  }
  
  
  @font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 600;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertSemiBold.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 600;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertSemiBoldItalic.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 700;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertBold.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 700;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertBoldItalic.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: normal;
    font-weight: 800;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertHeavy.woff') format('woff');
  }
  
  @font-face {
    font-family: 'Roobert';
    font-style: italic;
    font-weight: 800;
    src: url('https://raw.githubusercontent.com/nitrowo/roobert-cdn/master/RoobertHeavyItalic.woff') format('woff');
  }

#connect-modal {
	z-index: 2147483646;
	transition: .2s ease-in-out;
    font-family: 'Roobert', sans-serif;
}

#connect-modal-overlay {
	height: 100%;
	z-index: 2147483645;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
}

.buttons-container::-webkit-scrollbar {
    width: 6px;
}

.buttons-container::-webkit-scrollbar-track {
    background: transparent;
}

#connect-modal::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 2px solid transparent;
}

[data-theme=dark] .buttons-container {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

[data-theme=dark] .buttons-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}

.buttons-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

[data-theme=dark] .buttons-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
}


.modal-slide-enter {
  transform: translate(-50%, calc(-50% + 20px));
}

.modal-slide-enter-active {
  transform: translate(-50%, -50%);
}

@media (max-width: 768px) {
  .modal-slide-enter {
      transform: translateY(20px);
  }
  
  .modal-slide-enter-active {
      transform: translateY(0);
  }
}



*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: } *,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.scwtw-fixed{position:fixed}.scwtw-inset-0{inset:0px}.scwtw-bottom-0{bottom:0px}.scwtw-z-\[2147483645\]{z-index:2147483645}.scwtw-z-\[2147483646\]{z-index:2147483646}.scwtw-m-\[10px\]{margin:10px}.scwtw-my-\[20px\]{margin-top:20px;margin-bottom:20px}.scwtw-mb-1{margin-bottom:0.25rem}.scwtw-mb-2{margin-bottom:0.5rem}.scwtw-mt-4{margin-top:1rem}.scwtw-flex{display:flex}.scwtw-grid{display:grid}.scwtw-hidden{display:none}.scwtw-h-3{height:0.75rem}.scwtw-h-4{height:1rem}.scwtw-h-6{height:1.5rem}.scwtw-h-\[28px\]{height:28px}.scwtw-h-\[44px\]{height:44px}.scwtw-h-\[454px\]{height:454px}.scwtw-h-\[48px\]{height:48px}.scwtw-h-\[60px\]{height:60px}.scwtw-h-full{height:100%}.scwtw-max-h-\[454px\]{max-height:454px}.scwtw-w-3{width:0.75rem}.scwtw-w-4{width:1rem}.scwtw-w-6{width:1.5rem}.scwtw-w-\[44px\]{width:44px}.scwtw-w-\[48px\]{width:48px}.scwtw-w-\[60px\]{width:60px}.scwtw-w-full{width:100%}.scwtw-min-w-\[48px\]{min-width:48px}.scwtw-min-w-max{min-width:max-content}.scwtw-max-w-\[312px\]{max-width:312px}.scwtw-flex-shrink-0{flex-shrink:0}.scwtw-cursor-pointer{cursor:pointer}.scwtw-cursor-not-allowed{cursor:not-allowed}.scwtw-flex-row{flex-direction:row}.scwtw-flex-col{flex-direction:column}.scwtw-place-content-center{place-content:center}.scwtw-items-center{align-items:center}.scwtw-justify-center{justify-content:center}.scwtw-justify-between{justify-content:space-between}.scwtw-justify-around{justify-content:space-around}.scwtw-gap-4{gap:1rem}.scwtw-gap-\[12px\]{gap:12px}.scwtw-gap-\[32px\]{gap:32px}.scwtw-gap-\[4px\]{gap:4px}.scwtw-gap-x-4{column-gap:1rem}.scwtw-gap-y-2{row-gap:0.5rem}.scwtw-overflow-x-auto{overflow-x:auto}.scwtw-overflow-y-auto{overflow-y:auto}.scwtw-rounded-\[10px\]{border-radius:10px}.scwtw-rounded-\[12px\]{border-radius:12px}.scwtw-rounded-\[13px\]{border-radius:13px}.scwtw-rounded-full{border-radius:9999px}.scwtw-rounded-b-none{border-bottom-right-radius:0px;border-bottom-left-radius:0px}.scwtw-rounded-t-\[16px\]{border-top-left-radius:16px;border-top-right-radius:16px}.scwtw-border{border-width:1px}.scwtw-border-t{border-top-width:1px}.scwtw-border-\[\#0000000a\]{border-color:#0000000a}.scwtw-border-\[\#0000000f\]{border-color:#0000000f}.scwtw-border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.scwtw-bg-\[\#0000000f\]{background-color:#0000000f}.scwtw-bg-\[\#0000004d\]{background-color:#0000004d}.scwtw-bg-\[\#3c42420f\]{background-color:#3c42420f}.scwtw-bg-\[\#8473ff\]{--tw-bg-opacity:1;background-color:rgb(132 115 255 / var(--tw-bg-opacity, 1))}.scwtw-bg-transparent{background-color:transparent}.scwtw-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.scwtw-object-contain{object-fit:contain}.scwtw-p-1\.5{padding:0.375rem}.scwtw-p-2{padding:0.5rem}.scwtw-p-4{padding:1rem}.scwtw-p-\[7px\]{padding:7px}.scwtw-px-4{padding-left:1rem;padding-right:1rem}.scwtw-px-6{padding-left:1.5rem;padding-right:1.5rem}.scwtw-px-8{padding-left:2rem;padding-right:2rem}.scwtw-px-\[12px\]{padding-left:12px;padding-right:12px}.scwtw-px-\[24px\]{padding-left:24px;padding-right:24px}.scwtw-py-4{padding-top:1rem;padding-bottom:1rem}.scwtw-py-8{padding-top:2rem;padding-bottom:2rem}.scwtw-py-\[10px\]{padding-top:10px;padding-bottom:10px}.scwtw-py-\[4px\]{padding-top:4px;padding-bottom:4px}.scwtw-text-left{text-align:left}.scwtw-text-center{text-align:center}.scwtw-text-\[13px\]{font-size:13px}.scwtw-text-\[14px\]{font-size:14px}.scwtw-text-\[16px\]{font-size:16px}.scwtw-text-\[18px\]{font-size:18px}.scwtw-font-bold{font-weight:700}.scwtw-font-medium{font-weight:500}.scwtw-font-normal{font-weight:400}.scwtw-font-semibold{font-weight:600}.scwtw-leading-\[18px\]{line-height:18px}.scwtw-leading-\[20px\]{line-height:20px}.scwtw-leading-\[24px\]{line-height:24px}.scwtw-text-\[\#190037\]{--tw-text-opacity:1;color:rgb(25 0 55 / var(--tw-text-opacity, 1))}.scwtw-text-\[\#25292e\]{--tw-text-opacity:1;color:rgb(37 41 46 / var(--tw-text-opacity, 1))}.scwtw-text-\[\#3c424299\]{color:#3c424299}.scwtw-text-\[\#3c4242cc\]{color:#3c4242cc}.scwtw-text-\[\#8473ff\]{--tw-text-opacity:1;color:rgb(132 115 255 / var(--tw-text-opacity, 1))}.scwtw-text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.scwtw-opacity-0{opacity:0}.scwtw-opacity-100{opacity:1}.scwtw-opacity-50{opacity:0.5}.scwtw-shadow-\[0px_2px_16px_rgba\(0\,0\,0\,0\.16\)\]{--tw-shadow:0px 2px 16px rgba(0,0,0,0.16);--tw-shadow-colored:0px 2px 16px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.scwtw-transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.scwtw-transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.scwtw-duration-200{transition-duration:200ms}.scwtw-duration-300{transition-duration:300ms}.scwtw-ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1)}.hover\:scwtw-scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:scwtw-bg-\[\#3c42421a\]:hover{background-color:#3c42421a}.hover\:scwtw-underline:hover{-webkit-text-decoration-line:underline;text-decoration-line:underline}.active\:scwtw-scale-90:active{--tw-scale-x:.9;--tw-scale-y:.9;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.active\:scwtw-scale-95:active{--tw-scale-x:.95;--tw-scale-y:.95;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[is-visible\=true\]\:scwtw-flex[data-is-visible="true"]{display:flex}@media (min-width: 768px){.md\:scwtw-bottom-auto{bottom:auto}.md\:scwtw-left-1\/2{left:50%}.md\:scwtw-top-1\/2{top:50%}.md\:scwtw-flex{display:flex}.md\:scwtw-contents{display:contents}.md\:scwtw-hidden{display:none}.md\:scwtw-h-\[28px\]{height:28px}.md\:scwtw-h-\[470px\]{height:470px}.md\:scwtw-max-h-\[320px\]{max-height:320px}.md\:scwtw-max-h-\[454px\]{max-height:454px}.md\:scwtw-w-\[28px\]{width:28px}.md\:scwtw-w-\[368px\]{width:368px}.md\:scwtw-w-full{width:100%}.md\:scwtw-min-w-0{min-width:0px}.md\:scwtw--translate-x-1\/2{--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:scwtw--translate-y-1\/2{--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:scwtw-flex-col{flex-direction:column}.md\:scwtw-place-content-start{place-content:start}.md\:scwtw-justify-center{justify-content:center}.md\:scwtw-gap-2{gap:0.5rem}.md\:scwtw-gap-x-3{column-gap:0.75rem}.md\:scwtw-overflow-x-hidden{overflow-x:hidden}.md\:scwtw-rounded-\[16px\]{border-radius:16px}.md\:scwtw-rounded-\[6px\]{border-radius:6px}.md\:scwtw-rounded-t-\[16px\]{border-top-left-radius:16px;border-top-right-radius:16px}.md\:scwtw-border-t{border-top-width:1px}.md\:scwtw-border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.md\:scwtw-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.md\:scwtw-px-5{padding-left:1.25rem;padding-right:1.25rem}.md\:scwtw-px-6{padding-left:1.5rem;padding-right:1.5rem}.md\:scwtw-py-0{padding-top:0px;padding-bottom:0px}.md\:scwtw-pt-2{padding-top:0.5rem}.md\:scwtw-text-\[16px\]{font-size:16px}.md\:scwtw-font-semibold{font-weight:600}.md\:scwtw-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}}.dark\:scwtw-border-\[\#ffffff0a\]:where([data-theme="dark"], [data-theme="dark"] *){border-color:#ffffff0a}.dark\:scwtw-border-\[\#ffffff14\]:where([data-theme="dark"], [data-theme="dark"] *){border-color:#ffffff14}.dark\:scwtw-border-gray-800:where([data-theme="dark"], [data-theme="dark"] *){--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.dark\:scwtw-bg-\[\#00000080\]:where([data-theme="dark"], [data-theme="dark"] *){background-color:#00000080}.dark\:scwtw-bg-\[\#1a1b1f\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-bg-opacity:1;background-color:rgb(26 27 31 / var(--tw-bg-opacity, 1))}.dark\:scwtw-bg-\[\#2c2d31\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-bg-opacity:1;background-color:rgb(44 45 49 / var(--tw-bg-opacity, 1))}.dark\:scwtw-bg-\[\#e0e8ff0d\]:where([data-theme="dark"], [data-theme="dark"] *){background-color:#e0e8ff0d}.dark\:scwtw-text-\[\#e0e8ff99\]:where([data-theme="dark"], [data-theme="dark"] *){color:#e0e8ff99}.dark\:scwtw-text-\[\#fff9\]:where([data-theme="dark"], [data-theme="dark"] *){color:#fff9}.dark\:scwtw-text-\[\#ffffff\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.dark\:scwtw-text-white:where([data-theme="dark"], [data-theme="dark"] *){--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.dark\:hover\:scwtw-bg-\[\#e0e8ff1a\]:hover:where([data-theme="dark"], [data-theme="dark"] *){background-color:#e0e8ff1a}@media (min-width: 768px){.dark\:md\:scwtw-border-gray-800:where([data-theme="dark"], [data-theme="dark"] *){--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.dark\:md\:scwtw-bg-\[\#1a1b1f\]:where([data-theme="dark"], [data-theme="dark"] *){--tw-bg-opacity:1;background-color:rgb(26 27 31 / var(--tw-bg-opacity, 1))}}