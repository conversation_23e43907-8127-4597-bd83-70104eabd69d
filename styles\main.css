/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-accent: #3b82f6;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Button Colors */
    --btn-primary: #3b82f6;
    --btn-primary-hover: #2563eb;
    --btn-secondary: #6b7280;
    --btn-secondary-hover: #4b5563;
    --btn-accent: #10b981;
    --btn-accent-hover: #059669;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
}

[data-theme="dark"] {
    /* Dark Theme */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-accent: #60a5fa;
    --border-color: #334155;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Links */
a {
    color: var(--text-accent);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--btn-primary-hover);
}

.link {
    color: var(--text-accent);
    text-decoration: underline;
}

/* Buttons */
.btn, .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    gap: 0.5rem;
}

.action-btn.primary {
    background-color: var(--btn-primary);
    color: white;
}

.action-btn.primary:hover {
    background-color: var(--btn-primary-hover);
}

.action-btn.secondary {
    background-color: var(--btn-secondary);
    color: white;
}

.action-btn.secondary:hover {
    background-color: var(--btn-secondary-hover);
}

.action-btn.accent {
    background-color: var(--btn-accent);
    color: white;
}

.action-btn.accent:hover {
    background-color: var(--btn-accent-hover);
}

.action-btn.large {
    padding: 1rem 2rem;
    font-size: 1rem;
    width: 100%;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Cards */
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.page-header h1 {
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

/* Views */
.view {
    display: none;
    min-height: calc(100vh - 80px);
    padding-top: 80px;
}

.view.active {
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .page-header {
        padding: 1rem 0;
        margin-bottom: 2rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .action-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .action-btn.large {
        padding: 0.875rem 1.5rem;
    }
}
