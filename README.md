# Fees.WTF - Ethereum Gas Tracker & DeFi Tools

A comprehensive web application for tracking Ethereum gas prices and interacting with DeFi protocols, built on top of the existing wallet connection infrastructure.

## Features

### 🔥 Dashboard
- Real-time gas price tracking (Fast, Standard, Safe)
- ETH price monitoring with 24h change
- Interactive gas price history chart
- Network status indicators
- Quick access to DeFi tools

### 💰 Staking
- **WTF Token Staking**: Stake WTF tokens to earn rewards
- **LP Token Staking**: Stake liquidity provider tokens for enhanced yields
- Real-time APY calculations
- Pending rewards tracking
- One-click claim functionality

### 🔄 Token Swap
- Multi-token swap interface
- Real-time exchange rates
- Price impact calculations
- Slippage protection
- Integration with multiple DEXs

### 🖼️ NFT Collection
- Fees.WTF NFT collection browser
- Rarity-based filtering
- Direct OpenSea integration
- Minting functionality
- Portfolio tracking

### 👛 Wallet Integration
- MetaMask support
- WalletConnect integration
- Coinbase Wallet compatibility
- Automatic reconnection
- Balance tracking

## Technology Stack

- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Styling**: Custom CSS with CSS Variables for theming
- **Charts**: Chart.js for gas price visualization
- **Web3**: Integration with existing wallet connection scripts
- **Architecture**: Modular component-based structure

## File Structure

```
top/top/
├── index.html              # Main application entry point
├── styles/
│   ├── main.css            # Core styles and theme variables
│   ├── navbar.css          # Navigation bar styles
│   ├── dashboard.css       # Dashboard-specific styles
│   ├── stake.css           # Staking interface styles
│   └── swap.css            # Swap and NFT styles
├── js/
│   ├── app.js              # Main application controller
│   ├── router.js           # Client-side routing
│   ├── gas-tracker.js      # Gas price monitoring
│   ├── staking.js          # Staking functionality
│   ├── swap.js             # Token swap features
│   ├── nft.js              # NFT collection management
│   └── wallet.js           # Enhanced wallet management
├── scripts/                # Existing wallet connection scripts
├── styles/                 # Existing modal styles
└── images/                 # Logo and icon assets
```

## Getting Started

### Prerequisites
- Modern web browser with JavaScript enabled
- Web3 wallet (MetaMask, Coinbase Wallet, or WalletConnect compatible)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd feeswtf/top/top
   ```

2. **Serve the files**
   Since this is a client-side application, you need to serve it through a web server:

   **Option A: Using Python (if installed)**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```

   **Option B: Using Node.js (if installed)**
   ```bash
   npx serve .
   ```

   **Option C: Using any other local server**
   - XAMPP, WAMP, or similar
   - VS Code Live Server extension
   - Any other HTTP server

3. **Open in browser**
   Navigate to `http://localhost:8000` (or your server's address)

### Configuration

#### Angular Core Integration
The application includes Angular core integration as requested:
```html
<script charset="UTF-8" type="text/javascript" src="./angular-core-16.2.0.js"></script>
```

Make sure to place the `angular-core-16.2.0.js` file in the root directory.

#### API Integration
For production use, update the following:

1. **Gas Price API**: Replace mock data in `gas-tracker.js` with real API calls
2. **ETH Price API**: Update price fetching in `gas-tracker.js`
3. **Blockchain Integration**: Configure actual contract addresses and ABIs
4. **IPFS/NFT Metadata**: Update NFT image and metadata sources

## Usage

### Navigation
- Use the top navigation bar to switch between different sections
- The gas tracker in the navbar shows current gas prices
- Theme toggle for dark/light mode switching

### Wallet Connection
1. Click "Connect Wallet" in the top right
2. Select your preferred wallet from the modal
3. Approve the connection in your wallet
4. Your wallet info will appear in the navbar
5. Pro dashboard will automatically show upon connection

### Staking
1. Navigate to "Stake WTF" or "Stake LP"
2. Connect your wallet if not already connected
3. Enter the amount you want to stake
4. Click "Stake" and confirm the transaction
5. Monitor your rewards and claim when ready

### Token Swapping
1. Go to the "Swap" section
2. Select tokens using the dropdown menus
3. Enter the amount to swap
4. Review the exchange rate and fees
5. Click "Swap" and confirm the transaction

### NFT Collection
1. Visit the "NFT Collection" section
2. Browse available NFTs with rarity information
3. Click "View on OpenSea" for the full collection
4. Use "Mint NFT" to create new tokens (when connected)

## Customization

### Theming
The application uses CSS custom properties for easy theming:
- Edit `styles/main.css` to modify colors and spacing
- Dark/light themes are automatically handled
- All components respect the theme variables

### Adding New Features
1. Create new JavaScript modules in the `js/` directory
2. Add corresponding CSS in the `styles/` directory
3. Update the router in `router.js` for new pages
4. Initialize new components in `app.js`

### Wallet Integration
The application integrates with existing wallet scripts:
- `scripts/onboard.js` - Main wallet onboarding
- `scripts/wallet-connect-v4.js` - WalletConnect integration
- `scripts/coinbase.js` - Coinbase Wallet support

## Browser Support

- Chrome/Chromium 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Security Considerations

- Never store private keys or sensitive data in localStorage
- Always validate user inputs
- Use HTTPS in production
- Implement proper error handling for wallet interactions
- Validate all blockchain transactions before execution

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the browser console for error messages
- Ensure your wallet is properly connected
- Verify you're on the correct network (Ethereum mainnet)
- Clear browser cache if experiencing issues

## Roadmap

- [ ] Real API integration for gas prices
- [ ] Advanced portfolio analytics
- [ ] Multi-chain support
- [ ] Mobile app version
- [ ] Advanced trading features
- [ ] Governance token integration
