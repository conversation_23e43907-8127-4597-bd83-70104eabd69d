<svg width="80" height="80" fill="none" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
  <mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="80" height="80">
    <path d="M79.521 22.337 45.453 0v12.489L67.308 26.69l-2.571 8.136H45.453v10.348h19.284l2.571 8.136L45.453 67.51V80l34.068-22.266-5.57-17.698 5.57-17.699ZM15.814 45.174h19.212V34.826H15.742l-2.5-8.136L35.026 12.49V0L.958 22.337l5.57 17.699-5.57 17.698L35.098 80V67.511L13.242 53.31l2.57-8.136Z" fill="#1D1D1B"/>
  </mask>
  <g mask="url(#a)">
    <path d="M79.521 22.337 45.453 0v12.489L67.308 26.69l-2.571 8.136H45.453v10.348h19.284l2.571 8.136L45.453 67.51V80l34.068-22.266-5.57-17.698 5.57-17.699ZM15.814 45.174h19.212V34.826H15.742l-2.5-8.136L35.026 12.49V0L.958 22.337l5.57 17.699-5.57 17.698L35.098 80V67.511L13.242 53.31l2.57-8.136Z" fill="#fff"/>
    <path fill="url(#b)" d="M1.06 0h86.955v88.477H1.06z"/>
    <ellipse cx="5.822" cy="17.544" rx="76.484" ry="82.924" transform="rotate(-33.93 5.822 17.544)" fill="url(#c)"/>
  </g>
  <defs>
    <radialGradient id="c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="rotate(72.256 -9.106 12.76) scale(62.739 58.8096)">
      <stop offset=".12" stop-color="#8952FF" stop-opacity=".87"/>
      <stop offset="1" stop-color="#DABDFF" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="b" x1="68.662" y1="85.897" x2="45.75" y2="-8.292" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0B46F9"/>
      <stop offset="1" stop-color="#BBFBE0"/>
    </linearGradient>
  </defs>
</svg>
