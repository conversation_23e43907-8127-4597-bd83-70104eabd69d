/* Swap Specific Styles */

.swap-container {
    max-width: 500px;
    margin: 0 auto;
}

.swap-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
}

.swap-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.swap-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.settings-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.settings-btn:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Swap Form */
.swap-form {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.token-input {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.2s ease;
}

.token-input:hover {
    border-color: var(--text-accent);
}

.input-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.input-header label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.balance {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.input-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.amount-input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    padding: 0;
}

.amount-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.token-select {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.token-select:hover {
    background-color: var(--bg-primary);
}

.token-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Swap Arrow */
.swap-arrow {
    display: flex;
    justify-content: center;
    margin: -0.5rem 0;
    position: relative;
    z-index: 1;
}

.swap-arrow-btn {
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.swap-arrow-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transform: rotate(180deg);
}

/* Swap Info */
.swap-info {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row span:first-child {
    color: var(--text-secondary);
}

.info-row span:last-child {
    color: var(--text-primary);
    font-weight: 500;
}

.impact-low {
    color: var(--success);
}

.impact-medium {
    color: var(--warning);
}

.impact-high {
    color: var(--error);
}

/* Token Modal */
#token-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

#token-modal {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    width: 100%;
    max-width: 400px;
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 0.375rem;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.close-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.token-search {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.token-search input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.token-search input:focus {
    outline: none;
    border-color: var(--text-accent);
}

.token-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.token-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.token-item:hover {
    background-color: var(--bg-tertiary);
}

.token-item-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.token-item-info {
    flex: 1;
}

.token-item-symbol {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.token-item-name {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.token-item-balance {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* NFT Styles */
.nft-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.nft-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.nft-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.nft-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.2s ease;
}

.nft-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nft-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.nft-info {
    padding: 1rem;
}

.nft-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.nft-price {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Pro Dashboard Overlay */
.pro-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.pro-container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    width: 100%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.pro-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.pro-content {
    padding: 1.5rem;
}

.pro-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.pro-stat {
    text-align: center;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 0.75rem;
}

.pro-stat h3 {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.pro-stat .value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.pro-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.pro-btn {
    padding: 1rem;
    background-color: var(--btn-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pro-btn:hover {
    background-color: var(--btn-primary-hover);
}

/* NFT Card Styles */
.nft-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem 0.75rem 0 0;
}

.nft-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nft-card:hover .nft-image {
    transform: scale(1.05);
}

.nft-status {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.nft-status.owned {
    background-color: var(--success);
    color: white;
}

.nft-status.listed {
    background-color: var(--warning);
    color: white;
}

.nft-rarity {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.nft-rarity.common { background-color: #6b7280; }
.nft-rarity.uncommon { background-color: #10b981; }
.nft-rarity.rare { background-color: #3b82f6; }
.nft-rarity.epic { background-color: #8b5cf6; }
.nft-rarity.legendary { background-color: #f59e0b; }
.nft-rarity.mystic { background-color: #ef4444; }
.nft-rarity.divine { background-color: #ec4899; }
.nft-rarity.cosmic { background-color: #6366f1; }
.nft-rarity.ethereal { background-color: #14b8a6; }
.nft-rarity.quantum { background-color: #f97316; }

.nft-info {
    padding: 1rem;
}

.nft-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.nft-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.nft-trait {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.nft-rarity-percent {
    font-size: 0.75rem;
    color: var(--text-accent);
    font-weight: 500;
}

.nft-price-section {
    margin-bottom: 0.75rem;
}

.nft-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.nft-price-usd {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.nft-buy-btn {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--btn-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nft-buy-btn:hover {
    background-color: var(--btn-primary-hover);
}

.nft-buy-btn:disabled {
    background-color: var(--btn-secondary);
    cursor: not-allowed;
}

.nft-owner {
    text-align: center;
    padding: 0.5rem;
    color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .swap-container {
        padding: 0 0.5rem;
    }

    .nft-actions {
        flex-direction: column;
        align-items: center;
    }

    .nft-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .pro-stats {
        grid-template-columns: 1fr;
    }

    .pro-actions {
        grid-template-columns: 1fr;
    }
}
